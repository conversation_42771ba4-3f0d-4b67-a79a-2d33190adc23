import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";

export const cicdSsmClient = new SSMClient({
  region: "ap-southeast-2",
});

export  async  function getParameter (client,name) {
  try {
    const command = new GetParameterCommand({
      Name: name,
    });
    const response = await client.send(command);
    return response;
  } catch (error) {
    console.error("Error fetching parameter:", error);
    throw error;
  }
}
