#!/bin/bash

export BUILD_VERSION=$(date '+%Y%m%d_%H%M%S')
echo Build $BUILD_VERSION started on `date`
export COMMIT_EMAIL=$(git log -1 --pretty=format:'%ae')
export COMMIT_MESSAGE="$(git log -1 --pretty=%B)"
export SLACK_USER=$(echo $COMMIT_EMAIL | cut -d'@' -f 1)
export COMMIT_SHA=$(git rev-parse HEAD)
export BUILD_BRANCH=`git branch -a --contains HEAD | sed -n 2p | awk '{ printf $1 }'`; BUILD_BRANCH=${BUILD_BRANCH#remotes/origin/}
export CLEAN_BRANCH=${BUILD_BRANCH//\//_}
export RELATIVE_REPORT_PATH=$CLEAN_BRANCH/$BUILD_VERSION
export ARTIFACT_TEMP=/artifact_temp
export ARTIFACT_OUTPUT=/artifact_output/$CLEAN_BRANCH
export REPORT_OUTPUT=/reports/$RELATIVE_REPORT_PATH
mkdir -p $ARTIFACT_TEMP
mkdir -p $ARTIFACT_OUTPUT
mkdir -p $REPORT_OUTPUT

# source https://raw.githubusercontent.com/thii/aws-codebuild-extras/master/install
export CI=true
export CODEBUILD=true
export CODEBUILD_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
export CODEBUILD_GIT_BRANCH="$(git symbolic-ref HEAD --short 2>/dev/null)"
if [ "$CODEBUILD_GIT_BRANCH" = "" ] ; then
  export CODEBUILD_GIT_BRANCH="$(git rev-parse HEAD | xargs git name-rev | cut -d' ' -f2 | sed 's/remotes\/origin\///g')";
fi

export CODEBUILD_GIT_CLEAN_BRANCH="$(echo $CODEBUILD_GIT_BRANCH | tr '/' '.')"
export CODEBUILD_GIT_ESCAPED_BRANCH="$(echo $CODEBUILD_GIT_CLEAN_BRANCH | sed -e 's/[]\/$*.^[]/\\\\&/g')"
export CODEBUILD_GIT_MESSAGE="$(git log -1 --pretty=%B)"
export CODEBUILD_GIT_AUTHOR="$(git log -1 --pretty=%an)"
export CODEBUILD_GIT_AUTHOR_EMAIL="$(git log -1 --pretty=%ae)"
export CODEBUILD_GIT_COMMIT="$(git log -1 --pretty=%H)"
export CODEBUILD_GIT_SHORT_COMMIT="$(git log -1 --pretty=%h)"
export CODEBUILD_GIT_TAG="$(git describe --tags --exact-match 2>/dev/null)"
export CODEBUILD_GIT_MOST_RECENT_TAG="$(git describe --tags --abbrev=0)"

export CODEBUILD_PULL_REQUEST=false
if [ "${CODEBUILD_GIT_BRANCH#pr-}" != "$CODEBUILD_GIT_BRANCH" ] ; then
  export CODEBUILD_PULL_REQUEST=${CODEBUILD_GIT_BRANCH#pr-};
fi

export CODEBUILD_PROJECT=${CODEBUILD_BUILD_ID%:$CODEBUILD_LOG_PATH}
export CODEBUILD_BUILD_URL="https://$AWS_DEFAULT_REGION.console.aws.amazon.com/codebuild/home?region=$AWS_DEFAULT_REGION#/builds/$CODEBUILD_BUILD_ID/view/new"

echo "AWS CodeBuild Extra Environment Variables:"
echo "CI = $CI"
echo "CODEBUILD = $CODEBUILD"
echo "CODEBUILD_ACCOUNT_ID = $CODEBUILD_ACCOUNT_ID"
echo "CODEBUILD_GIT_AUTHOR = $CODEBUILD_GIT_AUTHOR"
echo "CODEBUILD_GIT_AUTHOR_EMAIL = $CODEBUILD_GIT_AUTHOR_EMAIL"
echo "CODEBUILD_GIT_BRANCH = $CODEBUILD_GIT_BRANCH"
echo "CODEBUILD_GIT_CLEAN_BRANCH = $CODEBUILD_GIT_CLEAN_BRANCH"
echo "CODEBUILD_GIT_ESCAPED_BRANCH = $CODEBUILD_GIT_ESCAPED_BRANCH"
echo "CODEBUILD_GIT_COMMIT = $CODEBUILD_GIT_COMMIT"
echo "CODEBUILD_GIT_SHORT_COMMIT = $CODEBUILD_GIT_SHORT_COMMIT"
echo "CODEBUILD_GIT_MESSAGE = $CODEBUILD_GIT_MESSAGE"
echo "CODEBUILD_GIT_TAG = $CODEBUILD_GIT_TAG"
echo "CODEBUILD_GIT_MOST_RECENT_TAG = $CODEBUILD_GIT_MOST_RECENT_TAG"
echo "CODEBUILD_PROJECT = $CODEBUILD_PROJECT"
echo "CODEBUILD_PULL_REQUEST = $CODEBUILD_PULL_REQUEST"