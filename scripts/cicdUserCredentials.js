import {readConfig} from "./readCmdConfig.js";

export  function cicdUserCredentials() {
  const obj = readConfig();
  return {
    dev_aws_access_key_id: obj.DEV_AWS_ACCESS_KEY_ID,
    dev_aws_secret_access_key: obj.DEV_AWS_SECRET_ACCESS_KEY
  };
}

export  function githubToken() {
  const obj = readConfig();
  return obj.GITHUB_TOKEN
}
const main = async () => {
  const obj = cicdUserCredentials();
  console.log(`AWS_ACCESS_KEY_ID=${obj.dev_aws_access_key_id} AWS_SECRET_ACCESS_KEY=${obj.dev_aws_secret_access_key}`);
};

await main();
