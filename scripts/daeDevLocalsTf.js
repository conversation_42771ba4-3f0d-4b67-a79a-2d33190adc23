import fs from 'fs/promises';
import path from 'path';
import {githubToken} from "./cicdUserCredentials.js";
import {getApplications} from "./dynamodbUtil.js";

const costCentre = 'tmp';
const env = 'tmp';
const formattedResults = await getApplications();

const customDomains = [
    "tmp.qivs.external.quotablevalue.co.nz",
    "fndc.tmp.qivs.external.quotablevalue.co.nz",
  ];

const readAndWriteFile = async (inputFilePath, outputFilePath) => {
  try {
    let text = await fs.readFile(inputFilePath, 'utf8');
    text = text
      .replace('LAMBDA_APPLICATIONS', JSON.stringify(formattedResults.lambda_applications))
      .replace('K8S_APPLICATIONS', JSON.stringify(formattedResults.k8s_applications))
      .replace('QV_CUSTOM_DOMAINS', JSON.stringify(customDomains))
      .replace('ENV', env)
      .replace('COST_CENTRE', costCentre)
      .replace('QIVS_AMI_ID', "ami-0316a05a6f447312e")
      .replace('GITHUB_TOKEN', githubToken())
    await fs.writeFile(outputFilePath, text, 'utf8');

    console.log(`Successfully copied content from ${inputFilePath} to ${outputFilePath}`);
  } catch (error) {
    console.error('Error processing the file:', error);
  }
};

const inputFilePath = '../cmd/tf/app_template/dev/locals.tf.sample';
const outputFilePath = path.join(path.dirname(inputFilePath), 'locals.tf');

await readAndWriteFile(inputFilePath, outputFilePath);
