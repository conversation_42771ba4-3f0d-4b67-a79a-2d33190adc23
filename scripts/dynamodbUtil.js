import {DynamoDBClient, ScanCommand} from "@aws-sdk/client-dynamodb";

const client = new DynamoDBClient({
  region: "ap-southeast-2",
});

export async function scanTable(tableName) {
  try {
    const command = new ScanCommand({
      TableName: tableName,
    });
    const response = await client.send(command);
    return response.Items;
  } catch (error) {
    console.error("Error scanning table:", error);
  }
}

export async function getApplications() {
  const items = await scanTable("app_stacks");
  const k8s_applications = [];
  const lambda_applications = [];

  items.forEach((item) => {
    const name = item.Name.S;
    const platform = item.Platform.S;

    if (platform === "kubernetes") {
      k8s_applications.push(name);
    } else if (platform === "lambda" && name !== "api-load") {
      lambda_applications.push(name);
    }
  });

  return {
    k8s_applications,
    lambda_applications,
  };
}

export async function getDatastores() {
  const items = await scanTable("datastore_stacks");
  const qivs_datastores = [];
  const pg_datastores = [];
  items.forEach((item) => {
    const name = item.Name.S;
    const platform = item.Type.S;

    if (platform === "QIVS") {
      qivs_datastores.push(name);
    } else if (platform === "PG Monarch") {
      pg_datastores.push(name);
    }
  });
  return {
    qivs_datastores,
    pg_datastores,
  };
}