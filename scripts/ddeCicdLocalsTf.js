import fs from 'fs/promises';
import path from 'path';
import {cicdUserCredentials} from "./cicdUserCredentials.js";
import {getDatastores} from "./dynamodbUtil.js";

const costCentre = 'tmp';
const env = 'tmp';
const cicdServiceUser = cicdUserCredentials();
const lambda_applications = ['api-load']
const qivs_datastores = (await getDatastores()).qivs_datastores
const readAndWriteFile = async (inputFilePath, outputFilePath) => {
  try {
    let text = await fs.readFile(inputFilePath, 'utf8');
    text = text
      .replace('LAMBDA_APPLICATIONS', JSON.stringify(lambda_applications))
      .replace('DATASTORES', JSON.stringify(qivs_datastores))
      .replace('ENV', env)
      .replace('COST_CENTRE', costCentre)
      .replace('DEV_ACCESS_KEY_ID', cicdServiceUser.dev_aws_access_key_id)
      .replace('DEV_SECRET_ACCESS_KEY', cicdServiceUser.dev_aws_secret_access_key);
    await fs.writeFile(outputFilePath, text, 'utf8');

    console.log(`Successfully copied content from ${inputFilePath} to ${outputFilePath}`);
  } catch (error) {
    console.error('Error processing the file:', error);
  }
};

const inputFilePath = '../cmd/tf/data_template/cicd/locals.tf.sample';
const outputFilePath = path.join(path.dirname(inputFilePath), 'locals.tf');

await readAndWriteFile(inputFilePath, outputFilePath);
