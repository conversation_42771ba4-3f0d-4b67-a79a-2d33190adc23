import {cicdSsmClient, getParameter} from "./ssmUtil.js";
import fs from 'fs';
import path from 'path';

export async function getCmdConfig() {
  const cmdSsm = await getParameter(cicdSsmClient, "/cicd/dynamic-environments-cmd");
  const value = cmdSsm.Parameter.Value
  const filePath = '../cmd/config.json';
  fs.writeFileSync(filePath, value, 'utf8');
  console.log('cmd config file created, written to the following path: ' + path.resolve(filePath));
}

await getCmdConfig();
