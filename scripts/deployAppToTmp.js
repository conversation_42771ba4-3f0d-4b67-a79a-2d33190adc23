import { S3Client, CopyObjectCommand } from "@aws-sdk/client-s3";

const appName = process.argv[2];
const branch = process.argv[3];
const sourceBucket = "qv-deployment";
const region = "ap-southeast-2";

const sourceKey = `${appName}/build/${branch.replace(/\//g, '_')}/deploy.zip`;
const destinationKey = `${appName}/deploy/tmp/deploy.zip`;

const s3Client = new S3Client({ region });

async function copyS3Object(sourceBucket, sourceKey, destinationKey) {
  const copyParams = {
    CopySource: `${sourceBucket}/${sourceKey}`,
    Bucket: sourceBucket,
    Key: destinationKey,
  };

  try {
    console.log(`Copying from ${copyParams.CopySource} to ${copyParams.Bucket}/${copyParams.Key}`);
    const data = await s3Client.send(new CopyObjectCommand(copyParams));
    console.log(`Successfully copied file to ${copyParams.Bucket}/${copyParams.Key}`);
  } catch (err) {
    console.error("Error copying file:", err);
  }
}

await copyS3Object(sourceBucket, sourceKey, destinationKey);
