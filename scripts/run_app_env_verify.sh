#!/bin/bash
REGION="ap-southeast-2"
CICD_CLUSTER="dynamic-environments"
SUBNETS="subnet-097383e7e4f76f0ec,subnet-0dd620c23992d88ae,subnet-0881390c25645dc44"
SECURITY_GROUPS="sg-04918d91687fd6b24"
TASK="app-env-verify"
TASK_DEF="app-env-verify-task"
BRANCH_NAME="feature/DEV3-831"
DDE=""

# Describe Task Definition
REVISION=$(aws ecs describe-task-definition --task-definition $TASK_DEF --region $REGION --query 'taskDefinition.revision' --output text)
echo "Revision: $REVISION"

# Properly escaped PAYLOAD
PAYLOAD="{\\\"branch\\\":\\\"${BRANCH_NAME}\\\",\\\"dde\\\":\\\"${DDE}\\\"}"

# COMMAND constructed with escaped PAYLOAD
COMMAND="cd /app/cmd && node src/index.js --name ${TASK} --payload '${PAYLOAD}'"

# Using single quotes for --overrides value to avoid needing to escape double quotes in JSON
OVERRIDES='{"containerOverrides": [{"name": "'$TASK_DEF'", "command": ["sh", "-c", "'"$COMMAND"'"]}]}'

RUN_TASK_OUTPUT=$(aws ecs run-task --cluster $CICD_CLUSTER \
     --task-definition $TASK_DEF:$REVISION \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNETS],securityGroups=[$SECURITY_GROUPS],assignPublicIp=DISABLED}" \
    --overrides "$OVERRIDES" \
    --region $REGION)

TASK_ARN=$(echo $RUN_TASK_OUTPUT | jq -r '.tasks[0].taskArn')
echo "Started ECS task with ID: $TASK_ARN"

TASK_ID=${TASK_ARN##*/}
AWS_BASE_URL="https://${REGION}.console.aws.amazon.com"
TASK_URL="$AWS_BASE_URL/ecs/v2/clusters/$CICD_CLUSTER/tasks/$TASK_ID/logs?region=$REGION"
echo "taskUrl: $TASK_URL"

LOG_EVENT_VIEWER_URL="$AWS_BASE_URL/cloudwatch/home?region=$REGION#logEventViewer:group=/aws/ecs/task/${TASK};stream=$TASK_DEF/$TASK_DEF/$TASK_ID"
echo "logEventViewerUrl: $LOG_EVENT_VIEWER_URL"

echo $LOG_EVENT_VIEWER_URL
