#!/bin/bash

CICD_PATH="infra/cicd"
BOILERPLATE_FILE="../../boilerplate.tf.sample"

APPLICATION_FOLDERS=(
    "api-consent"
    "api-load"
    "api-objection"
    "api-picklist"
    "api-report-generator"
    "api-sales"
    "api-stats"
    "api-stream"
    "api-ta"
    "api-floor-plan"
    "api-maps-test2"
    "api-pdf-report-generator"
    "api-property"
    "api-rtv"
    "api-sale-analysis-test2"
    "api-search"
    "api-worksheet"
    "api-property-details"
    "api-public-reports"
    "api-testfactory"
)

ENV_FOLDERS=(
    "build"
    "dev"
    "test"
    "prod"
)

run_terraform() {
    local target_path=$1
    echo "Processing folder: $target_path"
    cd "$target_path" || { echo "Folder not found: $target_path"; return; }

    # config the backend, only for the first time
    cp "$BOILERPLATE_FILE" "boilerplate.tf"
    RELATIVE_PATH=$(git rev-parse --show-prefix)
    RELATIVE_PATH=${RELATIVE_PATH%/}
    terraform init -backend-config="key=${RELATIVE_PATH}" -reconfigure

    terraform plan

    # run plan first to check the diff then uncomment the line below to apply the changes
    # terraform apply -auto-approve
    echo "Completed processing for folder: $target_path"
    cd - > /dev/null 2>&1
}

# Loop through each application folder
for app_folder in "${APPLICATION_FOLDERS[@]}"; do
    echo "Processing application: $app_folder"

    APP_PATH="$CICD_PATH/$app_folder"

    if [ -f "$APP_PATH/main.tf" ]; then
        run_terraform "$APP_PATH"
    fi

    for env_folder in "${ENV_FOLDERS[@]}"; do
        TARGET_PATH="$APP_PATH/$env_folder"

        if [ -d "$TARGET_PATH" ]; then
            run_terraform "$TARGET_PATH"
        else
            echo "Folder not found: $TARGET_PATH"
        fi
    done
done

echo "All folders have been processed."