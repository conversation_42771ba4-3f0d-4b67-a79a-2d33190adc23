#!/bin/bash
REGION="ap-southeast-2"
CICD_CLUSTER="dynamic-environments"
SUBNETS="subnet-097383e7e4f76f0ec,subnet-0dd620c23992d88ae,subnet-0881390c25645dc44"
SECURITY_GROUPS="sg-04918d91687fd6b24"
TASK="pg-dump"
TASK_DEF="pg-dump-task"
# Run ECS Task
RUN_TASK_OUTPUT=$(aws ecs run-task --cluster $CICD_CLUSTER \
    --task-definition $TASK_DEF:1 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNETS],securityGroups=[$SECURITY_GROUPS],assignPublicIp=DISABLED}" \
    --region $REGION)

TASK_ARN=$(echo $RUN_TASK_OUTPUT | jq -r '.tasks[0].taskArn')
echo "Started ECS task with ID: $TASK_ARN"

TASK_ID=${TASK_ARN##*/}
AWS_BASE_URL="https://${REGION}.console.aws.amazon.com"
TASK_URL="$AWS_BASE_URL/ecs/v2/clusters/$CICD_CLUSTER/tasks/$TASK_ID/logs?region=$REGION"
echo "taskUrl: $TASK_URL"

LOG_EVENT_VIEWER_URL="$AWS_BASE_URL/cloudwatch/home?region=$REGION#logEventViewer:group=/aws/ecs/task/${TASK};stream=$TASK_DEF/$TASK_DEF/$TASK_ID"
echo "logEventViewerUrl: $LOG_EVENT_VIEWER_URL"

echo $LOG_EVENT_VIEWER_URL
