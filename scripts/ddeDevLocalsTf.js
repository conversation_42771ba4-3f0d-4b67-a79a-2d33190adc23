import fs from 'fs/promises';
import path from 'path';

const costCentre = 'tmp';
const env = 'tmp';
const temDrive = 'E'
const readAndWriteFile = async (inputFilePath, outputFilePath) => {
  try {
    let text = await fs.readFile(inputFilePath, 'utf8');
    text = text
      .replace('DEVICE_NAME', `xvd${temDrive.toLowerCase()}`)
      .replace('DRIVE_NAME', `CICDDB01 dynamic drive ${temDrive}: for local testing`)
      .replace('SIZE', '20')
      .replace('ENV', env)
      .replace('COST_CENTRE', costCentre);
    await fs.writeFile(outputFilePath, text, 'utf8');

    console.log(`Successfully copied content from ${inputFilePath} to ${outputFilePath}`);
  } catch (error) {
    console.error('Error processing the file:', error);
  }
};

const inputFilePath = '../cmd/tf/data_template/dev/locals.tf.sample';
const outputFilePath = path.join(path.dirname(inputFilePath), 'locals.tf');

await readAndWriteFile(inputFilePath, outputFilePath);
