{"name": "dynamic-environments-scripts", "version": "1.0.0", "description": "", "type": "module", "engines": {"node": ">=20.0.0"}, "scripts": {"get-cmd-config": "node ./getCmdConfig.js", "cicd-service-user": "node ./cicdUserCredentials.js", "dae-cicd-tf": "node ./daeCicdLocalsTf.js", "dae-dev-tf": "node ./daeDevLocalsTf.js", "dde-cicd-tf": "node ./ddeCicdLocalsTf.js", "dde-dev-tf": "node ./ddeDevLocalsTf.js", "deploy-app-to-tmp": "node ./deployAppToTmp.js qvms feature/DEV-4866-add-qvms-to-dae"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.624.0", "@aws-sdk/client-s3": "^3.635.0", "@aws-sdk/client-ssm": "^3.624.0"}}