import fs from 'fs/promises';
import path from 'path';
import { getApplications} from "./dynamodbUtil.js";
import {cicdUserCredentials} from "./cicdUserCredentials.js";
import { readConfig } from "./readCmdConfig.js";

const costCentre = 'tmp';
const env = 'tmp';
const formattedResults = await getApplications();
const cicdServiceUser = cicdUserCredentials();
const config = readConfig();

const readAndWriteFile = async (inputFilePath, outputFilePath) => {
  try {
    let text = await fs.readFile(inputFilePath, 'utf8');
    text = text
      .replace('LAMBDA_APPLICATIONS', JSON.stringify(formattedResults.lambda_applications))
      .replace('K8S_APPLICATIONS', JSON.stringify(formattedResults.k8s_applications))
      .replace('ENV', env)
      .replace('COST_CENTRE', costCentre)
      .replace('QIVS_AMI_ID', "ami-0316a05a6f447312e")
      .replace('DEV_ACCESS_KEY_ID', cicdServiceUser.dev_aws_access_key_id)
      .replace('DEV_SECRET_ACCESS_KEY', cicdServiceUser.dev_aws_secret_access_key)
      .replace('EXTERNAL_USER_PASSWORD', config['EXTERNAL_USER_PASSWORD'])
      .replace('INTERNAL_USER_PASSWORD', config['INTERNAL_USER_PASSWORD'])
      .replace('INTERNAL_USER_CUSTOMER_CARE_PASSWORD', config['INTERNAL_USER_CUSTOMER_CARE_PASSWORD'])
      .replace('INTERNAL_USER_STANDARD_PASSWORD', config['INTERNAL_USER_STANDARD_PASSWORD']);

    await fs.writeFile(outputFilePath, text, 'utf8');

    console.log(`Successfully copied content from ${inputFilePath} to ${outputFilePath}`);
  } catch (error) {
    console.error('Error processing the file:', error);
  }
};

const inputFilePath = '../cmd/tf/app_template/cicd/locals.tf.sample';
const outputFilePath = path.join(path.dirname(inputFilePath), 'locals.tf');

await readAndWriteFile(inputFilePath, outputFilePath);
