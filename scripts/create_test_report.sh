#!/bin/bash

PROJECT_NAME=$1
ENV=$2
TEST_TYPE=$3
BUILD_BRANCH=$4
BUILD_VERSION=$5
COMMIT_SHA=$6

cat > payload.json <<EOF
{
  "body": "{\"appName\":\"$PROJECT_NAME\",\"env\":\"$ENV\",\"testType\":\"$TEST_TYPE\",\"branch\":\"$BUILD_BRANCH\",\"buildVersion\":\"$BUILD_VERSION\",\"commitSha\":\"$COMMIT_SHA\"}"
}
EOF

cat payload.json

aws lambda invoke --function-name api-dynamic-environments-cicd-createTestReport --payload fileb://payload.json response.json

cat response.json

export REPORT_FOLDER="$(jq -r '.body.data.folder' response.json)"
export REPORT_ID="$(jq -r '.body.data.reportId' response.json)"
export REPORT_VIEW_URL="https://launchpad.internal.quotablevalue.co.nz/test-reports?reportId=$REPORT_ID"
echo "REPORT_FOLDER = $REPORT_FOLDER"
echo "REPORT_ID = $REPORT_ID"
echo "REPORT_VIEW_URL = $REPORT_VIEW_URL"
