#!/bin/bash

# Check if at least two arguments are passed
if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <dataEnv> <datastore>"
    exit 1
fi

# Assign command line arguments to variables
DATA_ENV=$1
DATA_STORE=$2

REGION="ap-southeast-2" # Example region
CICD_CLUSTER="dynamic-environments"
SUBNETS="subnet-097383e7e4f76f0ec,subnet-0dd620c23992d88ae,subnet-0881390c25645dc44"
SECURITY_GROUPS="sg-04918d91687fd6b24"
TASK="db-scripts"

# Assuming getTask and isLocalDebug are replaced with actual values or logic
TASK_DEF="db-scripts-task"

echo "taskDef: $TASK_DEF"

# Describe Task Definition
REVISION=$(aws ecs describe-task-definition --task-definition $TASK_DEF --region $REGION --query 'taskDefinition.revision' --output text)
echo "Revision: $REVISION"

# Build command with passed parameters
ENV_OPTION=""
ENV_OPTION+=" --dataEnv $DATA_ENV"
ENV_OPTION+=" --datastore $DATA_STORE"

COMMAND="sh -c 'cd /app/cmd && node src/index.js --name $TASK $ENV_OPTION --local-debug false'"
echo "Command: $COMMAND"

# Run ECS Task
RUN_TASK_OUTPUT=$(aws ecs run-task --cluster $CICD_CLUSTER \
    --task-definition $TASK_DEF:$REVISION \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNETS],securityGroups=[$SECURITY_GROUPS],assignPublicIp=DISABLED}" \
    --overrides "{\"containerOverrides\": [{\"name\": \"$TASK_DEF\", \"command\": [\"$COMMAND\"]}]}" \
    --region $REGION)

TASK_ARN=$(echo $RUN_TASK_OUTPUT | jq -r '.tasks[0].taskArn')
echo "Started ECS task with ID: $TASK_ARN"

TASK_ID=${TASK_ARN##*/}
AWS_BASE_URL="https://${REGION}.console.aws.amazon.com"
TASK_URL="$AWS_BASE_URL/ecs/v2/clusters/$CICD_CLUSTER/tasks/$TASK_ID/logs?region=$REGION"
echo "taskUrl: $TASK_URL"

LOG_EVENT_VIEWER_URL="$AWS_BASE_URL/cloudwatch/home?region=$REGION#logEventViewer:group=/aws/ecs/task/${TASK};stream=$TASK_DEF/$TASK_DEF/$TASK_ID"
echo "logEventViewerUrl: $LOG_EVENT_VIEWER_URL"

# Return or echo the log event viewer URL
echo $LOG_EVENT_VIEWER_URL
