## Resources created by DAE

DAE create resources in both cicd & dev account, in case of failing to delete the resources, you can delete them
manually.

### Resources in cicd account

- CodePipeline
    - monarch-${app}-pipeline-${env}
    
- Event Bridge
    - ${app}-pipeline-event-rule-${env}

- Parameter Store
    - /${env}/${app}/infra

- S3
    - qv-deployment/${app}/deployment/${env}
    - qv-deployment-reports/${app}-deploy-${env}

- CloudWatch Logs
    - ${app}-deploy-${env}

### Resources in dev account

- Parameter Store
    - /${env}/${app}
    - /${env}/context
    - /${env}/report-generator

- Certificates
    - ${env}.dae.qvapi.co.nz

- Route53
    - ${env}.monarch.internal.quotablevalue.co.nz
    - ${env}.dae.qvapi.co.nz

- EC2
    - Monarch-${env} Kubernetes
    - Monarch-${env} Kafka

- LB
    - Monarch-${env}-PublicALB
    - Monarch-${env}-PrivateALB

- Target Groups
    - Monarch-${env}-PrivateALB
    - Monarch-${env}-PrivateWebALB
    - Monarch-${env}-PublicALB

- S3
    - qv-${local.env}-reports
    - monarch-kubernetes-deployment-dev/${env}

- SQS
    - report-generator-${env}

- Cloudformation
    - /${app}-${env}

- API Gateway
    - Custom domain names
        - ${env}.dae.qvapi.co.nz
    - APIs
        - ${app}-${env}
    - Usage Plan
        - ${env}-MonarchApiLambdaKey-usage-plan

- CloudWatch Logs
    - /aws/containerinsights/kubernetes-${env}/application
    - /aws/containerinsights/kubernetes-${env}/dataplane
    - /aws/containerinsights/kubernetes-${env}/performance
- Network
    - second IP for QIVS, descripion is ${env} QIVS Second Network Interface
- IAM
    - Policies
        - S3-Get-Access-Bucket-${env}-Reports

              https://us-east-1.console.aws.amazon.com/iamv2/home?region=ap-southeast-2#/policies
             ``` shell 
                 aws iam delete-policy --policy-arn arn:aws:iam::373100629421:policy/S3-Get-Access-Bucket-QV-${env}-Reports
             ```
    - Roles
        - API-Lambda-Execution-${env}
- ECS
    - Cluster
        - report-generator-${env}

              https://ap-southeast-2.console.aws.amazon.com/ecs/v2/clusters?region=ap-southeast-2
- ECR
    - Repository
        - report-generator-${env}

              https://ap-southeast-2.console.aws.amazon.com/ecr/repositories?region=ap-southeast-2
            ``` shell
                   aws ecr delete-repository --repository-name report-generator-${env} --force
            ```
  