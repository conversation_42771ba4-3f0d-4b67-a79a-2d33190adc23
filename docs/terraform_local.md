## Setting Up Terraform Locally from DAE/DDE

### Terraform Setup in DAEs

The following structure is used for Terraform setup in DAE/DDE:

- **cmd/tf/**
    - **app_template/** (DAE's Terraform templates)
        - **cicd/**  (Templates for the CICD account)
            - **locals.tf.sample** (Template file for `locals.tf`)
        - **dev/**   (Templates for the dev account)
            - **locals.tf.sample**
        - **modules/**
    - **data_template/** (DDE's Terraform templates)
        - **cicd/** (Templates for the CICD account)
            - **locals.tf.sample**
        - **dev/** (Templates for the dev account)
            - **locals.tf.sample**
        - **modules/**

During the `app-env-create` process, Terraform templates are conditionally copied based on DAE stacks and optional flags.
if the resources are only needed in the Monarch stack, the file name is prefixed with `monarch_`, if the resources are only needed in the Public Website stack, the file name is prefixed with `qvconz_`, and if the resources are only needed in the qivs, the file name is prefixed with `qivs_`.

The `locals.tf` files are created from the `locals.tf.sample` files in the respective directories, with placeholders replaced by actual values.

For detailed implementation, refer to the `applyTfScripts` function in `cmd/src/service/applyTfScripts.js`.

When making changes to the Terraform templates, ensure that `test/unit/applyTfScripts.test.js` is updated and that all tests are passing.


### Testing DAE Terraform Templates Locally

1. **Retrieve `/dynamic-environments/cmd/config.json` from SSM**

    ```bash
    aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account
    cd scripts
    npm run get-cmd-config
    ```

2. **Generate `locals.tf` Files for CICD and Dev Accounts**

    ```bash
    aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account
    cd scripts
    npm run dae-cicd-tf
    npm run dae-dev-tf
    ```

   **Note:** You can adjust the values of `k8s_applications` and `lambda_applications` to include fewer applications if you don't need to test all of them. These values are used to generate the CodeBuild pipelines for the applications.
   once you have the `locals.tf` files, you can test the Terraform templates locally, all files in `cmd/tf` are just terraform script, you can add whatever terraform script you want to test.
   if you need more values not present in the `locals.tf` files, see step 6 to add more values to `locals.tf` files.

3. **Modify DAE Infrastructure in the CICD Account**

    ```bash
    aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account

    cd cmd/tf/app_template/cicd
    terraform init -reconfigure
    terraform plan
    terraform apply
    # Remember to destroy the resources after testing
    terraform destroy
    ```

4. **Modify DAE Infrastructure in the Dev Account**

   Use the CICD-Service-User credentials to make changes in the dev account:

    ```bash
    # Get the credentials from the dev account
    aws-mfa --assume-role arn:aws:iam::************:role/DEV-RW-Cross-Account
    cd scripts
    npm run cicd-service-user

    cd cmd/tf/app_template/dev
    AWS_ACCESS_KEY_ID=ID AWS_SECRET_ACCESS_KEY=KEY terraform init -reconfigure
    AWS_ACCESS_KEY_ID=ID AWS_SECRET_ACCESS_KEY=KEY terraform plan
    AWS_ACCESS_KEY_ID=ID AWS_SECRET_ACCESS_KEY=KEY terraform apply
    # Remember to destroy the resources after testing
    AWS_ACCESS_KEY_ID=ID AWS_SECRET_ACCESS_KEY=KEY terraform destroy
    ```
5. **Add Places holders to `locals.tf.sample` files**

    If you need to add more values to the `locals.tf` files, you can add them to the `locals.tf.sample` files in the respective directories.
    The placeholders follow the naming convention all caps with underscores, e.g. `PLACE_HOLDER`.
    locate the following code in `cmd/src/service/applyTfScripts.js` and add the new placeholders to the `locals.tf.sample` files.
```js
   if (path.endsWith('locals.tf.sample')) {
      text = text
      .replace('LAMBDA_APPLICATIONS', JSON.stringify(lambdaApplications));
   }
```

### Testing DDE Terraform Templates Locally

the process will be exactly the same as DAE, just cd into data_template instead of app_template, replace `dae` with `dde` in the commands above,
e.g. `npm run dde-cicd-tf` instead of `npm run dae-cicd-tf`


### Test Deployment of Applications

Create required SSM parameters for the application manually, then run the following command to deploy the application to the tmp account:

```bash
    cd scripts
    npm run deploy-app-to-tmp APP BRANCH
```