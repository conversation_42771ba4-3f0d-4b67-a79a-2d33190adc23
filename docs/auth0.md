## Create SPA app

![SPA_0](./imgs/SPA_0.png)

## Config SPA app

![SPA_1](./imgs/SPA_1.png)

## Create API app

![API](./imgs/API.png)

## Config AWS API Gateway

Create V2 API Gateway using HTTP Protocol which supports JWT authorization.
Copy audience from API app to API Gateway, this is a must step, otherwise, the JWT token will be invalid.
![GW](./imgs/GW.png)

## Enable RBAC

![RBAC](./imgs/RBAC.png)

## Create Permissions

![Permission](./imgs/Permission.png)

## Create Roles

![Roles](./imgs/Role.png)

## Add Users

![Users](./imgs/User.png)


