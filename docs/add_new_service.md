## How to Add a New Application to DAE/DDE

### Ensure the New Service Has All the Configurations in Place

#### For Lambda Services
- `lambda-service/config.sample.json`
- `lambda-service/test/config.sample.json`

Ensure these configurations are copied to the build artifact. Refer to the [buildspec](https://github.com/Quotable-Value/lambda-template/blob/master/buildspec.yml).

#### For Kubernetes Services
- `k8s-service/kubernetes-configuration/env/k8s-service-podpreset.json`
- `k8s-service/new-kubernetes-configuration/test/config.sample.json`

### Update Config Context in Dynamic Environments (for Lambda Service)

Locate the function `getInitDefaultAppEnvConfig` in [`common/service/getDefaultAppEnvConfig.js`](https://github.com/Quotable-Value/dynamic-environments/blob/master/common/service/getDefaultAppEnvConfig.js).

Add a new URL for the new Lambda service and update `test/config.sample.json` accordingly.

### Add the New Service to the `app_stacks` Table in AWS CI/CD Account

Create a new record in the DynamoDB table [`app_stacks`](https://ap-southeast-2.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-2#item-explorer?maximize=true&operation=SCAN&table=app_stacks).

A good example for Lambda is [`api-objection`](https://ap-southeast-2.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-2#edit-item?itemMode=2&pk=api-objection&route=ROUTE_ITEM_EXPLORER&sk=&table=app_stacks).

If the Lambda does not follow the standard naming convention, check out [`api-property-details`](https://ap-southeast-2.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-2#edit-item?itemMode=2&pk=api-property-details&route=ROUTE_ITEM_EXPLORER&sk=&table=app_stacks).

A good example for Kubernetes is [`user-profile`](https://ap-southeast-2.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-2#edit-item?itemMode=2&pk=user-profile&route=ROUTE_ITEM_EXPLORER&sk=&table=app_stacks).

Ensure the `Platform` & `Stacks` options are configured properly.

**Platform Options:**
- Lambda
- Kubernetes
- ECS

**Stacks Options:**
- Monarch
- Public Website
- DDE (use this for DDE services)

For DAE, use the option `Monarch`, `Public Website`, or both.
For DDE, use the option `DDE`.

### Add Pod Configuration to the `pods_configuration` Table in AWS CI/CD Account (for Lambda Service)

Create a new record in the DynamoDB table [`pods_configuration`](https://ap-southeast-2.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-2#item-explorer?operation=SCAN&table=pods_configuration).

### Create a CodeBuild Project for Running Tests

Create a PR to add a new CodeBuild project in [`monarch-tests`](https://github.com/Quotable-Value/cicd/blob/master/infra/cicd/monarch-tests/build/main.tf).

Note: Tests for ECS services are not available, so you can skip this step for ECS services.
