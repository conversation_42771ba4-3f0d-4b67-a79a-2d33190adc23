# dynamic-environments

## cmd

all command-line apps, built on node.js and using the [commander](https://www.npmjs.com/package/commander)
library.

## lambda

APIs to serve the UI .

## ui

UI for the dynamic-environments project.

## infra

dynamic-environments project related infrastructure


# Local Development

## Install docker

Install [docker](https://docs.docker.com/engine/install) to your local to build images for `cmd`

## Download SSH key to access cicd managed EC2

Download [SSH key](https://ap-southeast-2.console.aws.amazon.com/systems-manager/parameters/%252Fcicd%252Fcicd-managed-pem/description?region=ap-southeast-2&tab=Table#list_parameter_filters=Name:Contains:cicd-)
to your local to do ssh commands.

```shell
aws ssm get-parameter --name "/cicd/cicd-managed-pem" --output text --query 'Parameter.Value' > cicd-managed.pem
chmod 400 cicd-managed.pem
```

## Install aws-cli

Install [aws-cli](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) as you may need to
delete aws resources manually when something goes wrong.

## local setup

see cmd/README.md to setup command line apps.
see lambda/README.md for more details.

## Add new app to dae
[Add new service](docs/add_new_service.md) to dae.

## Setup terraform local development
[terraform local development](docs/terraform_local.md)

## Dev Launchpad

[Dev Launchpad](https://dev.launchpad.internal.quotablevalue.co.nz) is a replica of the production Launchpad with some limitations. It is used for testing and development purposes.

App Environments and Data Environments are only available in the Launchpad in which they were created. Other resources, such as builds and reports, are environment-neutral and therefore available in both Launchpad and Dev Launchpad.


### Special Lambda Functions in Dev Launchpad

#### verifyAppEnvironment
This is a scheduled Lambda function used to verify the DAE.

It is currently disabled in the dev launchpad.

#### buildEventsSubscriber

This function subscribes to the SNS topic `build-notification` to receive CodeBuild events and insert build data into the DynamoDB table, facilitating auto-deployment.

In the dev launchpad, it listens to `build-notification-dev`, where no build events are currently being published.

Launchpad is able to handle auto-deployment for both launchpad and dev launchpad, but  CodeBuild events won't be processed in dev launchpad.

To test this functionality, manually send messages to `build-notification-dev`.

#### reportsEventHandler

This function subscribes to S3 events for reports, parsing the report and inserting the data into the DynamoDB table.

In the dev launchpad, it listens to the `qv-deployment-reports-dev` bucket, where no reports are currently being uploaded.

To test this functionality, manually upload a report to the bucket.
