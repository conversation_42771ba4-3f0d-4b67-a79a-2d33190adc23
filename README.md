# CICD

## Path structure for terraform

For modules shared between projects
- infra/modules/
- infra/<account(cicd|prod|dev)>/modules/

For pipelines and build projects
- infra/cicd/\<project>/build
- infra/cicd/\<project>/<environment_name>

## Storing state in S3

When running terraform, move the [boilerplate.tf.sample](./infra/cicd/boilerplate.tf.sample) file into your pipeline or build folder.
The following commands use the relative folder paths to your project terraform, for storing state in S3.
If state isn't stored in S3 then it is kept on your local machine, and others will see conflicts if they try to run your terraform.

```shell
# for powershell: 
terraform init -backend-config="key=$((git rev-parse --show-prefix).Substring(0, (git rev-parse --show-prefix).length-1))"
# for bash:
terraform init -backend-config="key=${$(git rev-parse --show-prefix)%?}"

# for migrating state to s3
terraform init -migrate-state
``` 
### Importing state missing from S3

- Use the init commands described above which use the relative path to the project root as the S3 key.
- Run `terraform apply` to see what conflicts it complains about.
- In the error message it will reference a terraform resource identifier and some kind of AWS resource identifier, which can be used in the `terraform import` command, e.g.
```shell
# example of importing state for the backoffice prod pipeline
terraform import module.pipeline.aws_iam_policy.codebuild_policy arn:aws:iam::948396734470:policy/service-role/costbuilder-backoffice-prod-codebuild-policy
terraform import module.pipeline.aws_iam_role.codebuild_role costbuilder-backoffice-prod-codebuild-role
terraform import module.pipeline.aws_iam_role.codepipeline_role costbuilder-backoffice-prod-pipeline-role
terraform import module.pipeline.aws_codebuild_project.lambda arn:aws:codebuild:ap-southeast-2:948396734470:project/costbuilder-backoffice_lambda_prod
terraform import module.pipeline.aws_codebuild_project.ui arn:aws:codebuild:ap-southeast-2:948396734470:project/costbuilder-backoffice_ui_prod
```
Note: the AWS identifier used by terraform for importing, is not always the ARN. It varies for different kinds of resources and is mentioned in the terraform docs what to use for importing.
