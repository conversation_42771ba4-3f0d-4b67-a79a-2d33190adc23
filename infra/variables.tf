variable "region" {
  description = "AWS region."
  default     = "ap-southeast-2"
  type        = string
}

variable "profile" {
  description = "AWS profile."
  type        = string
}

variable "environment" {
  description = "Target environment to deploy."
  type        = string

  validation {
    condition     = contains(["dev", "uat", "test", "prod"], var.environment)
    error_message = "Allowed values for environment are \"dev\", \"uat\", \"test\" or \"prod\"."
  }
}

variable "account_cicd" {
  type = string
}

variable "account_dev" {
  type = string
}

variable "account_uat" {
  type = string
}

variable "account_test" {
  type = string
}

variable "account_prod" {
  type = string
}

variable "artifact_store" {
  type = string
}

variable "project_name" {
  type = string
}

variable "billing_name" {
  type = string
}

variable "cicd_system_role" {
  type = string
}

variable "repo" {
  type = string
}

variable "branch" {
  type = string
}

variable "buildspec_path" {
  type = string
}

variable "build_notification_sns" {
  type = string
}
