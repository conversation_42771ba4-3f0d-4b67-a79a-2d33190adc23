module "ssm_tfvars_dev" {
  source           = "../../modules/ssm_tfvars"
  app_env          = "dev"
  app_name         = local.project_name
  additional_value = <<EOF
cloudwatch_groups  = ["/ecs/devqvconz"]
  EOF
}

module "pipeline_dev" {
  source            = "../../modules/ecs-deploy"
  app_name          = local.project_name
  ecs_service_name  = "devqvconz"
  ecs_cluster_name  = "devqvconz"
  ecr_repo_name     = "devqvconz"
  ecr_tag           = "latest"
  deploy_role_arn   = "arn:aws:iam::373100629421:role/qv-public-website-deploy-role"
  terraform_enabled = true
  migration         = true
  use_vpc           = true
  vpc_security_group_ids = ["sg-09ddfff9146c4d0d8"]
  vpc_subnets = ["subnet-0dd620c23992d88ae"]
  vpc_id            = "vpc-06474f6d3a210028b"
  data_env          = "dev"
  app_env           = "dev"
}
