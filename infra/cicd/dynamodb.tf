// data environments
resource "aws_dynamodb_table" "data_environments" {
  name         = "data_environments"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Name"

  attribute {
    name = "Name"
    type = "S"
  }
  attribute {
    name = "Status"
    type = "S"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "Status"
    projection_type = "ALL"
  }
}


resource "aws_dynamodb_table" "tf_scripts" {
  name         = "tf_scripts"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Path"

  attribute {
    name = "Path"
    type = "S"
  }

  attribute {
    name = "Env"
    type = "S"
  }

  global_secondary_index {
    name            = "EnvIndex"
    hash_key        = "Env"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "datastores" {
  name         = "datastores"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Key" // EnvName + DataStoreType to be the unique key

  attribute {
    name = "Key"
    type = "S"
  }

  attribute {
    name = "Env"
    type = "S"
  }

  global_secondary_index {
    name            = "EnvIndex"
    hash_key        = "Env"
    projection_type = "ALL"
  }
}

// app environments
resource "aws_dynamodb_table" "app_environments" {
  name         = "app_environments"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Name"

  attribute {
    name = "Name"
    type = "S"
  }
  attribute {
    name = "Status"
    type = "S"
  }

  attribute {
    name = "DataEnv"
    type = "S"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "Status"
    projection_type = "ALL"
  }
  global_secondary_index {
    name            = "DataEnvIndex"
    hash_key        = "DataEnv"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "applications" {
  name         = "applications"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Key" // EnvName + application to be the unique key

  attribute {
    name = "Key"
    type = "S"
  }

  attribute {
    name = "Env"
    type = "S"
  }

  global_secondary_index {
    name            = "EnvIndex"
    hash_key        = "Env"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "builds" {
  name         = "builds"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Key"  //  projectName + gitBranch to be the unique key

  attribute {
    name = "Key"
    type = "S"
  }
}

resource "aws_dynamodb_table" "pods_configuration" {
  name         = "pods_configuration"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "AppName"
  attribute {
    name = "AppName"
    type = "S"
  }
}

resource "aws_dynamodb_table" "app_stacks" {
  name         = "app_stacks"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Name"
  attribute {
    name = "Name"
    type = "S"
  }
}

resource "aws_dynamodb_table" "datastore_stacks" {
  name         = "datastore_stacks"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Name"
  attribute {
    name = "Name"
    type = "S"
  }
}


resource "aws_dynamodb_table" "deployments" {
  name         = "deployments"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "DeploymentId"

  attribute {
    name = "DeploymentId"
    type = "S"
  }

  attribute {
    name = "Env"
    type = "S"
  }

  global_secondary_index {
    name            = "EnvIndex"
    hash_key        = "Env"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "deployment_builds" {
  name         = "deployment_builds"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "Key" // deploymentId-application

  attribute {
    name = "Key"
    type = "S"
  }

  attribute {
    name = "BuildId"
    type = "S"
  }

  attribute {
    name = "DeploymentId"
    type = "S"
  }
  attribute {
    name = "Env"
    type = "S"
  }

  global_secondary_index {
    name            = "BuildIdIndex"
    hash_key        = "BuildId"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "DeploymentIdIndex"
    hash_key        = "DeploymentId"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "EnvIndex"
    hash_key        = "Env"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "test_reports" {
  name         = "test_reports"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "ReportId"

  attribute {
    name = "ReportId"
    type = "S"
  }

  ttl {
    attribute_name = "ExpiresAt"
    enabled = true
  }
}