module "dev-qvnz-restore-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-qvnz-restore"
  image_tag    = "dev"
}

module "dev-pg-restore-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-pg-restore"
  image_tag    = "dev"
}

module "dev-data-env-destroy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-data-env-destroy"
  image_tag    = "dev"
  memory       = "2048"
  cpu          = "1024"
}

module "dev-data-env-update-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-data-env-update"
  image_tag    = "dev"
}


module "dev-data-env-create-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-data-env-create"
  image_tag    = "dev"
  memory       = "2048"
  cpu          = "1024"
}


module "dev-app-env-destroy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-destroy"
  image_tag    = "dev"
  memory       = "2048"
  cpu          = "1024"
}


module "dev-app-env-create-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-create"
  image_tag    = "dev"
  memory       = "2048"
  cpu          = "1024"
}

module "dev-app-env-update-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-update"
  image_tag    = "dev"
}

module "dev-app-env-verify-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-verify"
  image_tag    = "dev"
  memory       = "2048"
  cpu          = "1024"
}

module "dev-app-env-tests-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-tests"
  image_tag    = "dev"
}

module "dev-db-scripts-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-db-scripts"
  image_tag    = "dev"
}

module "dev-application-deploy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-application-deploy"
  image_tag    = "dev"
}

module "dev-app-env-reindex-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "dev-app-env-reindex"
  image_tag    = "dev"
}
