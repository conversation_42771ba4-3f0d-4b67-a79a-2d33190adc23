module "build" {
  source           = "../../../modules/ecs-deploy"
  app_name         = "report-generator"
  app_env          = "preprod"
  data_env         = "preprod"
  ecr_repo_name    = "report-generator-preprod"
  ecs_cluster_name = "report-generator-preprod"
  ecs_service_name = "report-generator-preprod"
  account_id       = "************"
  deploy_role_arn  = "arn:aws:iam::************:role/report-generator-prod-deploy-role"
}
