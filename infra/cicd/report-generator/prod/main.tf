module "build" {
  source           = "../../../modules/ecs-deploy"
  app_name         = "report-generator"
  app_env          = "prod"
  data_env         = "prod"
  ecr_repo_name    = "report-generator-prod"
  ecs_cluster_name = "report-generator-prod"
  ecs_service_name = "report-generator-prod"
  account_id       = "************"
  deploy_role_arn  = "arn:aws:iam::************:role/report-generator-prod-deploy-role"
}
