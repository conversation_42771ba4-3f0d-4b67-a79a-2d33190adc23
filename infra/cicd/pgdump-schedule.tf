resource "aws_cloudwatch_event_rule" "pgdump_scheduled_task" {
  name                = "12_00_am_sunday_winter"
  description         = "Schedule to run ECS task at 12:00 AM every Monday in Auckland timezone during winter"
  schedule_expression = "cron(00 12 ? * SUN *)"
}


resource "aws_cloudwatch_event_target" "scheduled_task_event_target" {
  target_id = "${local.task_name}-target"
  rule      = aws_cloudwatch_event_rule.pgdump_scheduled_task.name
  arn       = aws_ecs_cluster.cicd-cluster.arn
  role_arn  = aws_iam_role.scheduled_task_cloudwatch.arn

  ecs_target {
    task_count          = 1
    task_definition_arn = aws_ecs_task_definition.pgdump_task.arn
    launch_type         = "FARGATE"
    network_configuration {
      assign_public_ip = false
      subnets          = local.subnets
      security_groups  = local.security_group
    }
  }
}
