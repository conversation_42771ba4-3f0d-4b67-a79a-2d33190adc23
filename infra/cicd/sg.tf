resource "aws_security_group" "lb-security-group" {
  name        = "${local.cluster_name}-lb-security-group-${var.environment}"
  description = "Allow HTTP inbound traffic"
  vpc_id      = local.vpc_id

  ingress {
    description = "Dynamic Environments - UI"
    from_port   = 80
    to_port     = 80
    protocol    = "TCP"
    cidr_blocks = ["10.0.0.0/8", "*********/8"]
  }

  ingress {
    description = "Dynamic Environments - API"
    from_port   = 3000
    to_port     = 3000
    protocol    = "TCP"
    cidr_blocks = ["10.0.0.0/8", "*********/8"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_security_group" "ecs-security-group" {
  name        = "${local.cluster_name}-ecs-security-group-${var.environment}"
  description = "Allow inbound traffic from LB"
  vpc_id      = local.vpc_id

  ingress {
    description     = "Load balancer1"
    from_port       = 80
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.lb-security-group.id]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}
