locals {
  product_name     = "costbuilder"
  application_name = "backoffice"
  branch_name      = "test"
  env              = "test"
}

module "pipeline" {
  source              = "../../modules/costbuilder-backoffice"
  product_name        = local.product_name
  application_name    = local.application_name
  project_name        = "${local.product_name}-${local.application_name}"
  project_description = "Costbuilder Backoffice"
  github_repo         = "Quotable-Value/costbuilder-back-office"

  env                     = local.env
  branch_name             = local.branch_name
  api_config_key          = "/${local.env}/costbuilder/backoffice/lambda/infra"
  api_domain              = "${local.product_name}.${local.application_name}.${local.env}.qvapi.co.nz"
  vite_auth0_domain       = "login.test.qvmonarch.co.nz"
  vite_auth0_client_id    = "TCCgiwi21lRmYxOreDcb5HY2gfLvMDZu"
  ui_site_bucket          = "costbuilder-site-test"
  cloudfront_distribution = "E3FQETC0RJJB9G"
  deploy_role_arn         = "arn:aws:iam::************:role/ci-cd-account-service-role"

  common_tags = {
    product = local.product_name
    app     = local.application_name
    env     = "cicd"
    stack   = local.env
  }
}
