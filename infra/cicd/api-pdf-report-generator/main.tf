locals {
  app_name = "api-pdf-report-generator"
  github_repo = "https://github.com/Quotable-Value/api-pdf-report-generator.git"
  dev = "dev"
  test = "test"
  infra_yml_dev = <<-EOT
    securityGroupIds:
      - "sg-02c8653614bd671df"
    subnetIds:
      - "subnet-5df45139"
      - "subnet-0775aa71"
    role: "arn:aws:iam::373100629421:role/API-Lambda-Execution-Dev"
    region: "ap-southeast-2"
    apiKeys:
      - "customer-reports-dev-qv"
    domainName: "dev.qvapi.co.nz"
    certificateName: "dev.qvapi.co.nz"
    basePath: "pdf-report-generator"
    costCentre: "dev"
  EOT
  infra_yml_test = <<-EOT
    securityGroupIds:
      - "sg-06e45e3814e3b9284"
    subnetIds:
      - "subnet-5df45139"
      - "subnet-0775aa71"
    role: "arn:aws:iam::373100629421:role/API-Lambda-Execution-Test"
    region: "ap-southeast-2"
    apiKeys:
      - "customer-reports-test-qv"
    domainName: "test.qvapi.co.nz"
    certificateName: "test.qvapi.co.nz"
    basePath: "pdf-report-generator"
    costCentre: "test"
  EOT
}

module "build" {
  source = "../../modules/lambda/build"
  app_name    = "${local.app_name}"
  bucket_path_override = "${local.app_name}"
  github_repo = local.github_repo
}

data "aws_ssm_parameter" "dev_api_key" {
  name = "/dev/lambda-api-key"
}

data "aws_ssm_parameter" "test_api_key" {
  name = "/test/lambda-api-key"
}

module "dev-deploy" {
  source = "../../modules/lambda/deploy"

  app_name    = local.app_name
  app_env     = local.dev
  data_env    = local.dev
}

module "test-deploy" {
  source = "../../modules/lambda/deploy"

  app_name    = local.app_name
  app_env     = local.test
  data_env    = local.test
}

module "dev-config" {
    source = "../../modules/lambda/config"

    app_name = local.app_name
    app_env  = local.dev
    host     = "https://${local.app_name}.${local.dev}.qvapi.co.nz"
    api_key  = data.aws_ssm_parameter.dev_api_key.value
    infra_yml = local.infra_yml_dev
}

module "test-config" {
    source = "../../modules/lambda/config"

    app_name = local.app_name
    app_env  = local.test
    host     = "https://${local.app_name}.${local.test}.qvapi.co.nz"
    api_key  = data.aws_ssm_parameter.test_api_key.value
    infra_yml = local.infra_yml_test
}