data "aws_caller_identity" "current" {}

resource "aws_iam_role" "service_role" {
  name_prefix        = "service_role_"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
    EOF
}

data "aws_iam_policy" "task_execution" {
  arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy" "ecs_execution" {
  role   = aws_iam_role.service_role.id
  name   = "AmazonECSTaskExecutionRolePolicy"
  policy = data.aws_iam_policy.task_execution.policy
}

resource "aws_iam_role" "dynamic_environments_task_role" {
  name_prefix        = "dynamic-db-env-task-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
    EOF
}

resource "aws_iam_role_policy_attachment" "task_role_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess"
  role       = aws_iam_role.dynamic_environments_task_role.name
}

resource "aws_iam_role_policy" "task_execution" {
  role   = aws_iam_role.dynamic_environments_task_role.id
  name   = "AmazonECSTaskExecutionRolePolicy"
  policy = data.aws_iam_policy.task_execution.policy
}


resource "aws_iam_role" "dynamic_environments_ecs_task_role" {
  name = "dynamic_environments_ecs_task_role"

  assume_role_policy = jsonencode({
    Version   = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        "Effect": "Allow",
        "Principal": {
          "AWS": "arn:aws:iam::730899178549:root"
        },
        "Action": "sts:AssumeRole",
        "Condition": {}
      }
    ]
  })
}


resource "aws_iam_policy" "ecs_task_policy" {
  name = "ecs_task_policy"

  policy = jsonencode({
    Version   = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:RunTask"
        ]
        Resource = "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "iam:PassRole",
          "iam:GetRole",
          "iam:ListRoles",
        ],
        "Resource" : [
          "*"
        ]
      },
      {
        Effect = "Allow"
        "Action" : [
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:GetItem",
          "dynamodb:Scan",
          "dynamodb:Query",
          "dynamodb:UpdateItem"
        ],
        Resource = "arn:aws:dynamodb:ap-southeast-2:************:table/*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "ssm:DescribeParameters",
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParameterHistory",
          "ssm:GetParametersByPath",
          "ssm:PutParameter",
          "ssm:DeleteParameter",
          "ssm:UpdateParameter",
          "ssm:ListTagsForResource",
          "ssm:AddTagsToResource",
        ],
        "Resource" : "arn:aws:ssm:ap-southeast-2:************:*"
      },
      {
        "Sid" : "AllowFullAccessToBucket",
        "Effect" : "Allow",
        "Action" : [
          "s3:*"
        ],
        "Resource" : [
          "arn:aws:s3:::qv-deployment",
          "arn:aws:s3:::qv-deployment/*",
          "arn:aws:s3:::qv-terraform",
          "arn:aws:s3:::qv-terraform/*",
          "arn:aws:s3:::qv-deployment-reports",
          "arn:aws:s3:::qv-deployment-reports/*",
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "events:PutRule",
          "events:DeleteRule",
          "events:DescribeRule",
          "events:PutTargets",
          "events:ListTargetsByRule",
          "events:RemoveTargets",
          "events:ListTagsForResource",
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "codebuild:CreateProject",
          "codebuild:BatchGetProjects",
          "codebuild:DeleteProject",
          "codebuild:UpdateProject",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "codepipeline:CreatePipeline",
          "codepipeline:GetPipeline",
          "codepipeline:DeletePipeline",
          "codepipeline:ListTagsForResource",
          "codepipeline:GetPipelineState",
          "codepipeline:TagResource"
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : "kms:DescribeKey",
        "Resource" : "arn:aws:kms:ap-southeast-2:************:key/a145cd3e-ff6c-48e0-9cc6-b03270a84bdd"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "logs:*"
        ],
        "Resource" : "arn:aws:logs:ap-southeast-2:************:log-group:*:log-stream:*"
      },
      {
        "Effect" : "Allow",
        "Action" : "ecs:DescribeTaskDefinition",
        "Resource" : "*"
      },
      {
        "Sid": "VisualEditor0",
        "Effect": "Allow",
        "Action": "sts:AssumeRole",
        "Resource": [
          "arn:aws:iam::************:role/ci-cd-account-service-role",
          "arn:aws:iam::************:role/ci-cd-account-service-role"
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "cloudfront:*"
        ],
        "Resource": [
          "arn:aws:cloudfront::************:distribution/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_policy_attachment" {
  policy_arn = aws_iam_policy.ecs_task_policy.arn
  role       = aws_iam_role.dynamic_environments_ecs_task_role.name
}


resource "aws_iam_role" "scheduled_task_cloudwatch" {
  name               = "${local.task_name}-cloudwatch-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "events.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "scheduled_task_cloudwatch_policy" {
  name   = "${local.task_name}-cloudwatch-policy"
  role   = aws_iam_role.scheduled_task_cloudwatch.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecs:RunTask"
      ],
      "Resource": [
        "${replace(aws_ecs_task_definition.pgdump_task.arn, "/:\\d+$/", ":*")}"
      ]
    },
    {
      "Effect": "Allow",
      "Action": "iam:PassRole",
      "Resource": [
        "*"
      ]
    }
  ]
}
EOF
}

resource "aws_iam_policy" "codepipeline_execution_policy" {
  name        = "CodePipelineExecutionPolicy"
  description = "Policy to allow starting CodePipeline execution"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "codepipeline:StartPipelineExecution"
            ],
            "Resource": [
                "arn:aws:codepipeline:ap-southeast-2:************:*"
            ]
        }
    ]
}
EOF
}

resource "aws_iam_role" "codepipeline_execution_role" {
  name               = "CodePipelineExecutionRole"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "events.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "codepipeline_execution_attachment" {
  policy_arn = aws_iam_policy.codepipeline_execution_policy.arn
  role       = aws_iam_role.codepipeline_execution_role.name
}
