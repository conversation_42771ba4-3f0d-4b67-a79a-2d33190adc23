locals {
  task_name = "pg-dump"
}

data "aws_iam_role" "ecs-execution-role" {
  name = "ecsTaskExecutionRole"
}
data "aws_iam_role" "ecs-task-role" {
  name = "dynamic_environments_ecs_task_role"
}

data "aws_ecs_cluster" "cicd-cluster" {
  cluster_name = "dynamic-environments"
}


resource "aws_cloudwatch_log_group" "pgdump_task_logs_group" {
  name = "/aws/ecs/task/${local.task_name}"
}
resource "aws_ecs_task_definition" "pgdump_task" {
  family                   = "${local.task_name}-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = 1024
  memory                   = 2048

  execution_role_arn    = data.aws_iam_role.ecs-execution-role.arn
  task_role_arn         = data.aws_iam_role.ecs-task-role.arn
  container_definitions = jsonencode([
    {
      name             = "${local.task_name}-task"
      image            = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/cicd/dynamic-environments-pgdump:latest"
      command          = ["node", "src/index.js"]
      logConfiguration = {
        logDriver = "awslogs"
        options   = {
          "awslogs-region" = local.region
          "awslogs-group"  = aws_cloudwatch_log_group.pgdump_task_logs_group.name
          "awslogs-stream-prefix" : "${local.task_name}-task"
        }
      }
    }
  ])
  depends_on = [
    data.aws_ecs_cluster.cicd-cluster,
  ]
}