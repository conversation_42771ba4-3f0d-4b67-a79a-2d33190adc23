locals {
  app_name       = "api-worksheet"
  github_repo    = "https://github.com/Quotable-Value/rural-worksheet.git"
  prod           = "prod"
  API_KEY_PROD   = ""
  infra_yml_prod = <<-EOT
    securityGroupIds:
      - "sg-07f6f506be094e5ab"
    subnetIds:
      - "subnet-d802d3bc"
      - "subnet-362ad640"
    role: "arn:aws:iam::466814882768:role/API-Lambda-Execution-Prod"
    region: "ap-southeast-2"
    apiKeys:
      - "customer-reports-prod-qv"
    domainName: "api-worksheet.prod.qvapi.co.nz"
    certificateName: "prod.qvapi.co.nz"
    basePath: ""
    costCentre: "prod"
  EOT
}

module "prod-deploy" {
  source = "../../../modules/lambda/deploy"

  app_name = local.app_name
  app_env  = local.prod
  data_env = local.prod
  preprod  = true
}

module "prod-config" {
  source = "../../../modules/lambda/config"

  app_name  = local.app_name
  app_env   = local.prod
  host      = "https://${local.app_name}.${local.prod}.qvapi.co.nz"
  api_key   = local.API_KEY_PROD
  infra_yml = local.infra_yml_prod
}
