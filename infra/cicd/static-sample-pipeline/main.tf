# resource "aws_s3_bucket" "static_test_bucket" {
#   bucket = "static-sample-test"
#   acl    = "private"
# }

# resource "aws_s3_bucket" "static_test_reports_bucket" {
#   bucket = "static-sample-test-reports"
#   acl    = "private"
# }

locals {
  build_bucket           = "qv-deployment"
  report_bucket          = "qv-deployment-reports"
  deploy_bucket          = "qv-deployment-reports"
  project_name           = "static-sample"
  repo_dir               = "src/static-sample"
  pipeline_name          = "${local.project_name}-pipeline"
  buildspec_path         = "${local.repo_dir}/buildspec.yml"
  source_file_path_regex = "${local.repo_dir}/.*"
}

module "pipeline" {
  source                      = "../../modules/static-pipeline"
  project_name                = local.project_name
  repo_dir                    = local.repo_dir
  github_repo                 = "https://github.com/Quotable-Value/cicd.git"
  region                      = local.region
  node_runtime_version        = 14
  buildspec_path              = local.buildspec_path
  source_file_path_regex      = local.source_file_path_regex
  build_bucket_name           = local.build_bucket
  build_artifact_base_path    = local.project_name
  build_artifact_key          = "${local.project_name}/deploy.zip"
  report_bucket_name          = local.report_bucket
  report_bucket_base_path     = local.project_name
  deploy_bucket_name          = local.deploy_bucket
  deploy_artifact_base_path   = "${local.project_name}-test-deploy"
  log_group_name              = local.pipeline_name
  pipeline_name               = local.pipeline_name
  service_role_arn            = "arn:aws:iam::************:role/ci-cd-account-access-role"
  pipeline_bucket_name        = "codepipeline-ap-southeast-2-***********"
  env_deploy_stages           = [
        {
        env      = "CICD"
        env_vars = []
        }
    ]
}
