locals {
  services = [
    { name = "api-consent", terraform_enabled = false, use_vpc = false },
    { name = "api-floor-plan", terraform_enabled = true, use_vpc = false },
    { name = "api-load", terraform_enabled = false, use_vpc = false },
    { name = "api-maps", terraform_enabled = false, use_vpc = false },
    { name = "api-objection", terraform_enabled = false, use_vpc = false },
    { name = "api-pdf-report-generator", terraform_enabled = false, use_vpc = false },
    { name = "api-picklist", terraform_enabled = false, use_vpc = false },
    { name = "api-property", terraform_enabled = false, use_vpc = false },
    # { name = "api-property-details" },  #note: is a qvconz service
    { name = "api-public-reports", terraform_enabled = false, use_vpc = false }, #note: shared with qvconz
    { name = "api-report-generator", terraform_enabled = false, use_vpc = false },
    { name = "api-rtv", terraform_enabled = false, use_vpc = false },
    { name = "api-sales", terraform_enabled = false, use_vpc = false },
    { name = "api-sale-analysis", terraform_enabled = false, use_vpc = false },
    { name = "api-search", terraform_enabled = false, use_vpc = false },
    { name = "api-stats", terraform_enabled = false, use_vpc = false },
    { name = "api-stream", terraform_enabled = true, use_vpc = true },
    { name = "api-ta", terraform_enabled = false, use_vpc = false },
    { name = "api-testfactory", terraform_enabled = false, use_vpc = false },
    { name = "api-worksheet", terraform_enabled = false, use_vpc = false },
  ]

  environments = [
    {
      env            = "uat",
      lambda_api_key = data.aws_ssm_parameter.uat_lambda_api_key.value,
      infra_content  = <<EOF
securityGroupIds:
- "sg-0b983b70e58b5cb29"
subnetIds:
- "subnet-5df45139"
- "subnet-0775aa71"
role: "arn:aws:iam::373100629421:role/API-Lambda-Execution-UAT"
region: "ap-southeast-2"
EOF
      tfvars = {
        api-stream = <<EOF
opensearch_host               = "https://vpc-qv-dev-private-os-73qcevg3ldxn54nvyi5n2ml2t4.ap-southeast-2.es.amazonaws.com"
opensearch_public_host        = "https://vpc-qv-dev-public-os-32lvthtby2twskiauq7ho4hnvu.ap-southeast-2.es.amazonaws.com"
reindexer_subnet_ids          = ["subnet-7f997709", "subnet-6c5c9f08", "subnet-da22e983"]
#                                opensearch cluster,     monarch alb,            monarch postgres,       NO qivs slice for UAT
reindexer_security_groups     = ["sg-07ceb995cd54dc6dd", "sg-085b3c99222e99030", "sg-0b983b70e58b5cb29"]
lambda_execution_role_name    = "API-Lambda-Execution-UAT"
aws_batch_lambda_policy_arn   = "arn:aws:iam::373100629421:policy/API-Lambda-Execution-Additional-Permissions"
index_manager_username        = "index-manager"
index_manager_password        = "$U7wpM3mDRkC57HN"
EOF
      }
    }
  ]
}

data "aws_ssm_parameter" "uat_lambda_api_key" {
  name = "/uat/lambda-api-key"
}

module "service" {
  # for each service and each environment, create a module instance
  for_each = {
    for item in flatten([
      for service in local.services : [
        for env in local.environments : {
          key               = "${service.name}-${env.env}"
          service_name      = service.name
          env               = env.env
          terraform_enabled = service.terraform_enabled
          use_vpc           = service.use_vpc
        }
      ]
    ])
    : item.key => item
  }

  source = "../../../modules/lambda/deploy"

  app_name          = each.value.service_name
  app_env           = each.value.env
  data_env          = each.value.env
  terraform_enabled = each.value.terraform_enabled
  use_vpc           = each.value.use_vpc
}

module "ssm" {
  # for each service and each environment, create a module instance
  for_each = {
    for item in flatten([
      for service in local.services : [
        for env in local.environments : {
          key            = "${service.name}-${env.env}"
          service_name   = service.name
          env            = env.env
          lambda_api_key = env.lambda_api_key
          infra_content  = env.infra_content
        }
      ]
    ])
    : item.key => item
  }

  source = "../../../modules/lambda/ssm"

  app_name = each.value.service_name
  env      = each.value.env

  lambda_api_key = each.value.lambda_api_key
  infra_content  = each.value.infra_content
}

module "ssm_tfvars" {
  for_each = {
    for item in flatten([
      for service in local.services : [
        for env in local.environments : {
          key          = "${service.name}-${env.env}"
          service_name = service.name
          env          = env.env
          additional_value = lookup(env.tfvars, service.name, "")
        }
      ]
    ])
    : item.key => item
  }
  source = "../../../modules/ssm_tfvars"

  app_name         = each.value.service_name
  app_env          = each.value.env
  additional_value = each.value.additional_value
}