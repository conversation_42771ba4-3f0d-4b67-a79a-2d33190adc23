locals {
  k8s_services = [
    { name = "audit" },
    { name = "classification" },
    { name = "home-valuation" },
    { name = "media" },
    { name = "monarch-web" },
    { name = "property" },
    { name = "qivs-stream" },
    { name = "reporting" },
    { name = "ereporting", github_repo_name = "e-reporting" },
    { name = "roll-maintenance" },
    { name = "sale" },
    { name = "sale-analysis" },
    { name = "stats" },
    { name = "ta" },
    { name = "user-profile" },
  ]
  lambda_services = [
    { name = "api-maps" },
    { name = "api-objection" },
    { name = "api-worksheet", github_repo_name = "rural-worksheet" },
    { name = "api-report-generator" },
    { name = "api-rtv" },
    { name = "api-sales" },
    { name = "api-floor-plan" },
    { name = "api-pdf-report-generator" },
    { name = "api-picklist" },
    { name = "api-property" },
    { name = "api-property-details", github_repo_name = "api-website", src_path = "src/property-details" },
    { name = "api-public-reports", github_repo_name = "api-website", src_path = "src/public-reports" },
    { name = "api-search", github_repo_name = "search" },
    { name = "api-stats" },
    { name = "api-sale-analysis" },
    { name = "api-load" },
    { name = "api-testfactory" },
    { name = "api-ta" },
    { name = "api-consent" },
    { name = "api-stream" }
  ]
}

module "k8s_test" {
  for_each         = {for service in local.k8s_services : service.name => service}
  source           = "../../../modules/k8s-test"
  service_name     = each.value.name
  github_repo_name = contains(keys(each.value), "github_repo_name") ? each.value.github_repo_name : each.value.name
}

module "lambda_test" {
  source           = "../../../modules/lambda-test"
  for_each         = {for service in local.lambda_services : service.name => service}
  service_name     = each.value.name
  github_repo_name = contains(keys(each.value), "github_repo_name") ? each.value.github_repo_name : each.value.name
  src_path         = contains(keys(each.value), "src_path") ? each.value.src_path : ""
}