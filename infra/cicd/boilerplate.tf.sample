# for powershell: terraform init -backend-config="key=$((git rev-parse --show-prefix).Substring(0, (git rev-parse --show-prefix).length-1))"
# for bash: terraform init -backend-config="key=${$(git rev-parse --show-prefix)%?}"
locals {
  profile = "default"
  region = "ap-southeast-2"
  account_cicd = "************"
  allowed_account_ids = [local.account_cicd]
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "~> 3.53"
    }
  }
  backend "s3" {
    bucket = "qv-terraform"
    region = "ap-southeast-2"
    dynamodb_table = "terraform-state-lock"
  }

  required_version = ">= 1.0.4"
}

provider "aws" {
  allowed_account_ids = local.allowed_account_ids
  region = local.region
  profile = local.profile
}
