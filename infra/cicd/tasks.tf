module "qvnz-restore-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "qvnz-restore"
  memory       = "2048"
  cpu          = "1024"
}

module "pg-restore-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "pg-restore"
  memory       = "2048"
  cpu          = "1024"
}

module "data-env-destroy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "data-env-destroy"
  memory       = "2048"
  cpu          = "1024"
}

module "data-env-create-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "data-env-create"
  memory       = "2048"
  cpu          = "1024"
}

module "data-env-update-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "data-env-update"
  memory       = "2048"
  cpu          = "1024"
}

module "app-env-destroy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-destroy"
  memory       = "2048"
  cpu          = "1024"
}


module "app-env-create-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-create"
  memory       = "2048"
  cpu          = "1024"
}

module "app-env-update-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-update"
  memory       = "2048"
  cpu          = "1024"
}

module "app-env-verify-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-verify"
  memory       = "2048"
  cpu          = "1024"
}

module "app-env-tests-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-tests"
}

module "db-scripts-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "db-scripts"
}

module "application-deploy-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "application-deploy"
  memory       = "2048"
  cpu          = "1024"
}

module "app-env-reindex-task" {
  source       = "../modules/ecs-cmd-task"
  cmd_app_name = "app-env-reindex"
  memory       = "2048"
  cpu          = "1024"
}