locals {
  build_bucket           = "qv-deployment"
  report_bucket          = "qv-deployment-reports"
  deploy_bucket          = "qv-maps"
  project_name           = "qv-maps"
  repo_dir               = "src"
  pipeline_name          = "${local.project_name}-pipeline"
  buildspec_path         = "${local.repo_dir}/buildspec.yml"
  source_file_path_regex = "${local.repo_dir}/.*"
}

module "pipeline" {
  source                    = "../../modules/static-pipeline"
  project_name              = local.project_name
  repo_dir                  = local.repo_dir
  github_repo               = "https://github.com/Quotable-Value/qv-maps.git"
  region                    = local.region
  node_runtime_version      = 14
  buildspec_path            = local.buildspec_path
  source_file_path_regex    = local.source_file_path_regex
  build_bucket_name         = local.build_bucket
  build_artifact_base_path  = local.project_name
  build_artifact_key        = "${local.project_name}/deploy.zip"
  report_bucket_name        = local.report_bucket
  report_bucket_base_path   = local.project_name
  deploy_bucket_name        = local.deploy_bucket
  deploy_artifact_base_path = ""
  log_group_name            = local.pipeline_name
  pipeline_name             = local.pipeline_name
  service_role_arn          = "arn:aws:iam::************:role/ci-cd-account-access-role"
  pipeline_bucket_name      = "codepipeline-ap-southeast-2-***********"
  env_deploy_stages         = [
    {
      env      = "DEV"
      env_vars = []
    }
  ]
}
