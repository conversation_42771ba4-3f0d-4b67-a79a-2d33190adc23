locals {
  app_name = "api-property-details"
  env = "test"
  infra_yml_test = <<-EOT
    securityGroupIds:
      - "sg-07d813fd30f853149"
    subnetIds:
      - "subnet-5df45139"
      - "subnet-0775aa71"
    role: "arn:aws:iam::373100629421:role/API-Lambda-Execution-Test"
    region: "ap-southeast-2"
    apiKeys:
      - "property-details-test-qv"
    domainName: "test.qvapi.co.nz"
    certificateName: "test.qvapi.co.nz"
    basePath: "public-property-details"
    costCentre: "test"
  EOT
}

data "aws_ssm_parameter" "api_key" {
  name = "/test/lambda-api-key"
}

module "test-deploy" {
  source = "../../../modules/lambda/deploy"

  app_name    = local.app_name
  app_env     = local.env
  data_env    = local.env
}

resource "aws_ssm_parameter" "test_config_param" {
  name        = "/${local.env}/${local.app_name}-test-config"
  description = "config required for running tests for ${local.env} ${local.app_name}"
  type        = "String"
  value = jsonencode({
    integrationTestApiKey = data.aws_ssm_parameter.api_key.value
    integrationTestHost   = "https://${local.env}.qvapi.co.nz/public-property-details"
    testFactoryApiKey     = data.aws_ssm_parameter.api_key.value
    testFactoryHost       = "https://${local.env}.qvapi.co.nz/testfactory"
  })
}

resource "aws_ssm_parameter" "infra_yml_param" {
  name        = "/${local.env}/${local.app_name}/infra"
  description = "infra.yml for ${local.env} ${local.app_name}"
  type        = "String"
  value       = local.infra_yml_test
}