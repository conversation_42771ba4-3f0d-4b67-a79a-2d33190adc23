locals {
  app_name = "api-property-details"
  env = "dev"
  infra_yml_dev = <<-EOT
    securityGroupIds:
      - "sg-08d1291821abf54db"
    subnetIds:
      - "subnet-5df45139"
      - "subnet-0775aa71"
    role: "arn:aws:iam::373100629421:role/API-Lambda-Execution-Dev"
    region: "ap-southeast-2"
    apiKeys:
      - "customer-reports-dev-qv"
    domainName: "dev.qvapi.co.nz"
    certificateName: "dev.qvapi.co.nz"
    basePath: "public-property-details"
    costCentre: "dev"
  EOT
}

data "aws_ssm_parameter" "api_key" {
  name = "/dev/lambda-api-key"
}

module "dev-deploy" {
  source = "../../../modules/lambda/deploy"

  app_name    = local.app_name
  app_env     = local.env
  data_env    = local.env
}

resource "aws_ssm_parameter" "test_config_param" {
  name        = "/${local.env}/${local.app_name}-test-config"
  description = "config required for running tests for ${local.env} ${local.app_name}"
  type        = "String"
  value = jsonencode({
    integrationTestApiKey = data.aws_ssm_parameter.api_key.value
    integrationTestHost   = "https://${local.env}.qvapi.co.nz/public-property-details"
    testFactoryApiKey     = data.aws_ssm_parameter.api_key.value
    testFactoryHost       = "https://${local.env}.qvapi.co.nz/testfactory"
  })
}

resource "aws_ssm_parameter" "infra_yml_param" {
  name        = "/${local.env}/${local.app_name}/infra"
  description = "infra.yml for ${local.env} ${local.app_name}"
  type        = "String"
  value       = local.infra_yml_dev
}