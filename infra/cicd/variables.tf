locals {
  cluster_name = "dynamic-environments"
  vpc_id       = "vpc-06474f6d3a210028b"
  subnets      = [
    "subnet-097383e7e4f76f0ec",
    "subnet-0dd620c23992d88ae",
    "subnet-0881390c25645dc44"
  ]
  security_group = ["sg-04918d91687fd6b24"]
}

variable "deployment_role" {
  type    = string
  default = "arn:aws:iam::************:role/ci-cd-account-access-role"
}
variable "environment" {
  type    = string
  default = "cicd"
}