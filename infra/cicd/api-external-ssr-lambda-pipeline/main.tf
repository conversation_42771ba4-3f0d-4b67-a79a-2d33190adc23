variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-southeast-2"
}

module "pipeline" {
  source               = "../../modules/lambda-pipeline-wrapper"
  project_name         = "api-external-ssr"
  repo_dir             = "api-ssr"
  github_repo          = "https://github.com/Quotable-Value/api-external.git"
  region               = var.region
  node_runtime_version = 14
  env_deploy_stages = [
    {
      env = "Dev"
      env_vars = [
        {
          name  = "API_KEY"
          type  = "PARAMETER_STORE"
          value = "/dev/ssr/apikey"
        }
      ]
    },
    {
      env = "Test"
      env_vars = [
        {
          name  = "API_KEY"
          type  = "PARAMETER_STORE"
          value = "/test/ssr/apikey"
        }
      ]
    },
    {
      env = "Prod"
      env_vars = [
        {
          name  = "API_KEY"
          type  = "PARAMETER_STORE"
          value = "/prod/ssr/apikey"
        }
      ]
    }
  ]
}
