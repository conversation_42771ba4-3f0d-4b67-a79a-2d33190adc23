module "build" {
  source                  = "../../../modules/dynamic-environments-backoffice/deploy"
  app_name                = "dynamic-environments-backoffice"
  ui_site_bucket          = "dev.launchpad.internal.quotablevalue.co.nz"
  cloudfront_distribution = "E1HX0KS7J6MPLQ"
  app_env                 = "dev"
  data_env                = "dev"
  lambda_deploy_role_arn  = "cicd-lambda-deploy-role-arn"
}
