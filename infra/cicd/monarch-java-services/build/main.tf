locals {
  services = [
    { name = "classification" },
    { name = "home-valuation" },
    { name = "media" },
    {
      name = "monarch-web"
      custom_buildspec = "buildspec-monarch-web.yml"
    },
    { name = "property" },
    { name = "reporting" },
    {
      name = "ereporting",
      github_repo = "e-reporting"
    },
    { name = "roll-maintenance" },
    { name = "sale" },
    { name = "sale-analysis" },
    { name = "user-profile" },
  ]
}

module "service" {
  for_each = { for service in local.services : service.name => service }
  source = "../../../modules/monarch"

  service_name     = each.value.name
  s3_bucket_prefix = "${(startswith(each.value.name, "monarch-") ? "" : "monarch-")}${each.value.name}"
  github_repo      = "Quotable-Value/${contains(keys(each.value), "github_repo") ? each.value.github_repo : each.value.name}"

  custom_buildspec = contains(keys(each.value), "custom_buildspec") ? file("${path.module}/${each.value.custom_buildspec}") : ""
}