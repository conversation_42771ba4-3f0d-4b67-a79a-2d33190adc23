# the AWS profile you use should not assume a role, terraform will assume the roles it needs to
# terraform init -backend-config="key=infra/prod/qv-cloudwatch"

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.17.0"
    }
  }
  backend "s3" {
    bucket         = "qv-terraform"
    region         = "ap-southeast-2"
    dynamodb_table = "terraform-state-lock"
    role_arn       = "arn:aws:iam::************:role/ci-cd-account-service-role"
    key            = "infra/dev/qv-cloudwatch"
  }
  required_version = ">= 1.0.4"
}

provider "aws" {
  region = "ap-southeast-2"
  assume_role {
    role_arn = "arn:aws:iam::************:role/ci-cd-account-service-role"
  }
}