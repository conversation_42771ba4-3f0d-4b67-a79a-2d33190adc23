variable "target_account_role_arn" {
  description = "The role ARN to assume in the target account"
  type        = string
  validation {
    condition     = length(var.target_account_role_arn) > 0
    error_message = "Target account role ARN must be provided"
  }
}

variable "region" {
  description = "The region to deploy to"
  type        = string
  default     = "ap-southeast-2"
  validation {
    condition     = length(var.region) > 0
    error_message = "Region must be provided"
  }
}

variable "aws_target_account_number" {
  description = "The target aws account"
  validation {
    condition     = length(var.aws_target_account_number) > 0
    error_message = "Must not be empty"
  }
}

variable "master_user_password" {
  description = "Opensearch internal database master user password to be created"
  validation {
    condition     = length(var.master_user_password) > 0
    error_message = "Must not be empty"
  }
}