# the AWS profile you use should not assume a role, terraform will assume the roles it needs to
# Create a copy of backend.sample.hcl and rename it to backend.hcl with the correct parameters
# terraform init -backend-config="key=infra/prod/qv-opensearch"

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.17.0"
    }
  }
  backend "s3" {
    bucket         = "qv-terraform"
    region         = "ap-southeast-2"
    dynamodb_table = "terraform-state-lock"
    role_arn       = "arn:aws:iam::************:role/CICD-RW-Cross-Account"
    key            = "infra/prod/qv-opensearch"
  }
  required_version = ">= 1.0.4"
}

provider "aws" {
  region = var.region
  assume_role {
    role_arn = var.target_account_role_arn
  }
}