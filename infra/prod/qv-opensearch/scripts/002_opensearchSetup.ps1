# written in powershell 8

$private_url = $Env:os_private_url
$public_url = $Env:os_public_url
$index_manager_password = $Env:os_index_manager_password
$read_only_password = $Env:os_read_only_password
$leader_alias = $Env:os_remote_cluster
$index_manager_user = "index-manager"
$read_only_user = "read-only"
$read_only_role="read-only-role"
$username = "master"
$password = $Env:os_master_password
$securePassword = ConvertTo-SecureString $password -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($username, $securePassword)


function CreateAutoFollow {
    Write-Output "Creating property-autofollow for public cluster"
    $response = PostToOpensearch -url "$public_url/_plugins/_replication/_autofollow?pretty=true" -body ('{"leader_alias": "' + $leader_alias + '", "name": "property-autofollow", "pattern": "*-property-*", "use_roles": {"leader_cluster_role":"all_access", "follower_cluster_role":"all_access"}}')
    Write-Output $response.Content

    Write-Output "Creating sale-autofollow for public cluster"
    $response = PostToOpensearch -url "$public_url/_plugins/_replication/_autofollow?pretty=true" -body ('{"leader_alias": "' + $leader_alias + '", "name": "sale-autofollow", "pattern": "*-sale-*", "use_roles": {"leader_cluster_role":"all_access", "follower_cluster_role":"all_access"}}')
    Write-Output $response.Content
}


function CreateUsers {
    param (
        [string]$url
    )
    $indexManagerRes = PutToOpensearch -url "$url/_plugins/_security/api/internalusers/index-manager" -body ('{"password": "' + $index_manager_password + '" }')
    Write-Output $indexManagerRes.Content

    $readOnlyRes = PutToOpensearch -url "$url/_plugins/_security/api/internalusers/read-only" -body ('{"password": "' + $read_only_password + '" }')
    Write-Output $readOnlyRes.Content
}

function CreateRoles {
    param (
        [string]$url
    )
    $readOnlyRes = PutToOpensearch -url "$url/_plugins/_security/api/roles/$read_only_role" -body ('{"index_permissions": [{"index_patterns": ["*"], "allowed_actions": ["get", "search", "read"]}]}')
    Write-Output $readOnlyRes.Content
}

function MapRoles {
    param (
        [string]$url
    )
    $indexManagerRes = PutToOpensearch -url "$url/_plugins/_security/api/rolesmapping/all_access" -body ('{"users": ["master","' + $index_manager_user + '"]}')
    Write-Output $indexManagerRes.Content

    $readOnlyRes = PutToOpensearch -url "$url/_plugins/_security/api/rolesmapping/$read_only_role" -body ('{"users": ["' + $read_only_user + '"]}')
    Write-Output $readOnlyRes.Content
}

function PostToOpensearch {
    param (
        [string]$url,
        [string]$body
    )

    return Invoke-WebRequest -Uri $url -Method Post -Credential $credential -UseBasicParsing -Headers @{"Content-Type"="application/json"} -Body $body

}
function PutToOpensearch {
    param (
        [string]$url,
        [string]$body
    )

    return Invoke-WebRequest -Uri $url -Method Put -Credential $credential -UseBasicParsing -Headers @{"Content-Type"="application/json"} -Body $body

}

function GetToOpensearch {
    param (
        [string]$url
    )

    return Invoke-WebRequest -Uri $url -Method Get -Credential $credential -UseBasicParsing

}


Write-Output "Creating users for private cluster"
CreateUsers -url $private_url

Write-Output "Creating users for public cluster"
CreateUsers -url $public_url

Write-Output "Creating roles for private cluster"
CreateRoles -url $private_url

Write-Output "Creating roles for public cluster"
CreateRoles -url $public_url

Write-Output "Mapping roles for private cluster"
MapRoles -url $private_url

Write-Output "Mapping roles for public cluster"
MapRoles -url $public_url

Write-Output "Creating autofollow rule for public cluster"
CreateAutoFollow

Write-Output "Getting autofollow rules for public cluster"
$autofollow = GetToOpensearch -url "$public_url/_plugins/_replication/autofollow_stats?pretty=true"
Write-Output $autofollow.Content