data "aws_vpc" "prod_vpc" {
  id = "vpc-51818034"
}

data "aws_subnet" "private_data_a" {
  id = "subnet-5c15d838"
}

data "aws_subnet" "private_data_b" {
  id = "subnet-bda24acb"
}

data "aws_security_group" "search_prod" {
  name = "Monarch-Prod-ES"
}

data "aws_security_group" "search_preprod" {
  name = "Monarch-PreProd-ES"
}

data "aws_security_group" "internal_lambdas_prod" {
  name = "api-monarch-prod-lambda"
}

data "aws_security_group" "internal_lambdas_preprod" {
  name = "api-monarch-preprod-lambda"
}

resource "aws_security_group" "opensearch_security_group" {
  name        = "prod-opensearch-sg"
  description = "Security group for PROD OpenSearch & Reindexing Tasks"

  vpc_id = data.aws_vpc.prod_vpc.id

  ingress {
    description = "Allow traffic from the same security group"
    from_port   = 443
    to_port     = 443
    protocol    = "TCP"
    self        = true
  }

  ingress {
    description     = "Allow traffic from Internal Lambdas"
    from_port       = 443
    to_port         = 443
    protocol        = "TCP"
    security_groups = [data.aws_security_group.internal_lambdas_prod.id, data.aws_security_group.internal_lambdas_preprod.id]
  }
}

module "private-cluster" {
  source          = "../../modules/qv-opensearch"
  engine_version  = "OpenSearch_2.11"
  private         = true
  ebs_volume_size = "240"
  environment     = "prod"
  aws_target_account_number = var.aws_target_account_number
  master_user_password = var.master_user_password
  vpc_options     = {
    subnet_ids = [
      data.aws_subnet.private_data_a.id,
      data.aws_subnet.private_data_b.id
    ]
    security_group_ids = [data.aws_security_group.search_prod.id, data.aws_security_group.search_preprod.id, aws_security_group.opensearch_security_group.id]
  }
}

module "public-cluster" {
 source          = "../../modules/qv-opensearch"
 engine_version  = "OpenSearch_2.11"
 private = false
 ebs_volume_size = "240"
 environment     = "prod"
 aws_target_account_number = var.aws_target_account_number
 master_user_password = var.master_user_password
 vpc_options = {
   subnet_ids = [
     data.aws_subnet.private_data_a.id,
     data.aws_subnet.private_data_b.id
   ]
   security_group_ids = [data.aws_security_group.search_prod.id, data.aws_security_group.search_preprod.id, aws_security_group.opensearch_security_group.id]
 }
}
