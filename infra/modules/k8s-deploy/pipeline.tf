locals {
  dev_deployment_role    = "arn:aws:iam::************:role/ci-cd-account-service-role"
  prod_deployment_role   = "arn:aws:iam::************:role/ci-cd-account-service-role"
  dev_deployment_bucket  = "monarch-kubernetes-deployment-dev"
  prod_deployment_bucket = "monarch-kubernetes-deployment-prod"
  pipeline_bucket_name   = "codepipeline-ap-southeast-2-***********"
  codebuild_bucket_name  = "qv-deployment"
  pipeline_role_arn      = data.aws_iam_role.monarch-pipeline.arn
  s3_kms_key_arn         = data.aws_kms_key.monarch.arn

  artifact_bucket_name = local.codebuild_bucket_name
  artifact_bucket_path = "${(startswith(var.app_name, "monarch-") ? "" : "monarch-")}${var.app_name}"
  pipeline_name        = "${(startswith(var.app_name, "monarch-") ? "" : "monarch-")}${var.app_name}-pipeline-${var.app_env}"
  deploy_file_key      = "${local.artifact_bucket_path}/deploy/${var.app_env}/deploy.zip"
  tf_state_path        = var.tf_state_path != "" ? var.tf_state_path : "${var.app_env}/${var.app_name}"

  deployment = var.app_env == "prod" ? [
    {
      name          = "preprod"
      bucket_name   = local.prod_deployment_bucket
      bucket_path   = "preprod"
      action_role   = local.prod_deployment_role
      approval_step = true
    },
    {
      name          = "prod"
      bucket_name   = local.prod_deployment_bucket
      bucket_path   = "prod"
      action_role   = local.prod_deployment_role
      approval_step = true
    }
  ] : [
    {
      name          = var.app_env
      bucket_name   = local.dev_deployment_bucket
      bucket_path   = var.app_env
      action_role   = local.dev_deployment_role
      approval_step = false
    }
  ]
}

data "aws_iam_role" "monarch-codebuild" {
  name = "codebuild-monarch-role"
}

data "aws_kms_key" "monarch" {
  key_id = "alias/monarch-pipeline-key"
}

data "aws_iam_role" "monarch-pipeline" {
  name = "ci-cd-account-access-role"
}

data "aws_iam_role" "code_pipeline_execution_role" {
  name = "CodePipelineExecutionRole"
}

resource "aws_codepipeline" "pipeline" {
  name     = local.pipeline_name
  role_arn = var.service_role_arn
  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      category = "Source"
      configuration = {
        "PollForSourceChanges" = "false"
        "S3Bucket"             = var.build_bucket_name
        "S3ObjectKey"          = local.deploy_file_key
      }
      input_artifacts = []
      name      = "Source"
      namespace = "SourceVariables"
      output_artifacts = [
        "SourceArtifact",
      ]
      owner     = "AWS"
      provider  = "S3"
      region    = var.region
      run_order = 1
      version   = "1"
    }
  }

  dynamic "stage" {
    for_each = local.deployment
    content {
      name = "${title(stage.value["name"])}-Deployment"

      # Terraform Plan Stage
      action {
        name     = "Terraform-Plan"
        category = "Build"
        configuration = {
          "EnvironmentVariables" = jsonencode([
            {
              name  = "TF_STATE_PATH",
              type  = "PLAINTEXT",
              value = local.tf_state_path,
            },
            {
              name  = "TERRAFORM_ENABLED",
              type  = "PLAINTEXT",
              value = var.terraform_enabled
            }
          ])
          "ProjectName" = "${var.app_name}-terraform-${stage.value["name"]}"
        }
        input_artifacts  = ["SourceArtifact"]
        output_artifacts = ["DeployArtifact-Terraform-${stage.value["name"]}"]
        owner     = "AWS"
        provider  = "CodeBuild"
        region    = var.region
        run_order = 1
        version   = "1"
        namespace = "TerraformVariables-${title(stage.value["name"])}"
      }

      dynamic "action" {
        for_each = var.terraform_enabled && var.app_env == "prod" ? [1] : []
        content {
          name      = "Apply-Terraform-Plan"
          category  = "Approval"
          owner     = "AWS"
          provider  = "Manual"
          run_order = 2
          version   = "1"
          configuration = {
            "CustomData"         = "Please review the deployment plan and approve or reject the deployment."
            "ExternalEntityLink" = "https://s3.console.aws.amazon.com/s3/object/qv-deployment?region=ap-southeast-2&bucketType=general&prefix=${var.app_name}/deploy/${stage.value["name"]}/plan_details.txt"
          }
        }
      }
      dynamic "action" {
        for_each = stage.value["approval_step"] ? [1] : []
        content {
          name     = "Approve-${title(stage.value["name"])}-Deployment"
          category = "Approval"
          owner    = "AWS"
          provider = "Manual"
          version  = "1"
          configuration = {
            "CustomData" = "Please approve the deployment for ${stage.value["name"]}."
          }
          input_artifacts = []
          output_artifacts = []
          run_order = 3
        }
      }

      # Deployment Action
      action {
        name     = "Deploy"
        category = "Build"
        configuration = {
          "EnvironmentVariables" = jsonencode([
            {
              name  = "ENV",
              type  = "PLAINTEXT",
              value = stage.value["name"],
            },
            {
              name  = "DEPLOY_ROLE_ARN",
              type  = "PLAINTEXT",
              value = stage.value["action_role"]
            },
            {
              name  = "BUCKET_NAME",
              type  = "PLAINTEXT",
              value = stage.value["bucket_name"]
            },
            {
              name  = "BUCKET_PATH",
              type  = "PLAINTEXT",
              value = stage.value["bucket_path"]
            },
            {
              name  = "S3_BUCKET",
              type  = "PLAINTEXT",
              value = var.build_bucket_name
            },
            {
              name  = "S3_OBJECT_KEY",
              type  = "PLAINTEXT",
              value = local.deploy_file_key
            },
            {
              name  = "S3_URL_DEPLOY_REPORT_DIR",
              type  = "PLAINTEXT",
              value = "s3://qv-deployment-reports/${var.app_name}-deploy-${stage.value["name"]}",
            },
            {
              name  = "TF_STATE_PATH",
              type  = "PLAINTEXT",
              value = local.tf_state_path,
            },
            {
              name  = "TERRAFORM_ENABLED"
              type  = "PLAINTEXT"
              value = var.terraform_enabled
            }
          ])
          "ProjectName" = "${var.app_name}-deploy-${stage.value["name"]}"
        }
        input_artifacts = var.terraform_enabled ? ["DeployArtifact-Terraform-${stage.value["name"]}"] : ["SourceArtifact"]
        namespace        = "DeployVariables-${title(stage.value["name"])}" # Unique namespace per stage
        output_artifacts = ["DeployArtifact-Final-${title(stage.value["name"])}"]
        owner           = "AWS"
        provider        = "CodeBuild"
        region          = var.region
        run_order       = 4
        version         = "1"
      }
    }
  }
}

resource "aws_cloudwatch_event_rule" "pipeline_event_rule" {
  name        = "${var.app_name}-pipeline-event-rule-${var.app_env}"
  description = "Event rule for triggering the incredible ${var.app_env} ${var.app_name} pipeline!"

  event_pattern = <<EOF
{
  "source": ["aws.s3"],
  "detail-type": ["AWS API Call via CloudTrail"],
  "detail": {
    "eventSource": ["s3.amazonaws.com"],
    "eventName": ["PutObject", "CompleteMultipartUpload", "CopyObject"],
    "requestParameters": {
      "bucketName": ["${var.build_bucket_name}"],
      "key": ["${local.deploy_file_key}"]
    }
  }
}
EOF
}

resource "aws_cloudwatch_event_target" "pipeline_event_target" {
  rule      = aws_cloudwatch_event_rule.pipeline_event_rule.name
  target_id = "${var.app_name}-pipeline-target-${var.app_env}"
  arn       = aws_codepipeline.pipeline.arn
  role_arn  = data.aws_iam_role.code_pipeline_execution_role.arn
  depends_on = [aws_codepipeline.pipeline]
}
