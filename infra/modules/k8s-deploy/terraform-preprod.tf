resource "aws_codebuild_project" "codebuild_terraform_preprod" {
  count                  = var.app_env=="prod" && var.terraform_enabled ? 1 : 0
  name                   = "${var.app_name}-terraform-preprod"
  build_timeout          = 10
  concurrent_build_limit = 1
  queued_timeout         = 30
  service_role           = var.service_role_arn

  source {
    type = "CODEPIPELINE"
    buildspec = templatefile("${path.module}/terraform-buildspec.yml", {
      node_runtime_version = var.node_runtime_version
    })
    insecure_ssl        = false
    report_build_status = false
  }

  dynamic "vpc_config" {
    for_each = var.use_vpc ? [1] : []
    content {
      vpc_id = "vpc-06474f6d3a210028b"
      subnets = ["subnet-097383e7e4f76f0ec", "subnet-0dd620c23992d88ae", "subnet-0881390c25645dc44"]
      security_group_ids = ["sg-0b5a83ef94043e0d1"]
    }
  }

  artifacts {
    name = var.app_name
    type = "CODEPIPELINE"
  }

  cache {
    type = "NO_CACHE"
  }

  logs_config {
    cloudwatch_logs {
      status      = "ENABLED"
      group_name  = local.preprod_terraform_project_name
      stream_name = local.preprod_terraform_project_name
    }
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/cicd/codebuild:latest"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = var.privileged_mode

    environment_variable {
      name  = "APP_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }


    environment_variable {
      name  = "ENV"
      value = "preprod"
      type  = "PLAINTEXT"
    }


    environment_variable {
      name  = "S3_DEPLOY_ARTIFACT_BUCKET"
      value = "s3://${var.build_bucket_name}/${var.app_name}/deploy/preprod"
      type  = "PLAINTEXT"
    }
  }
}
