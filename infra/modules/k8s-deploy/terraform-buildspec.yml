version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts

      - aws sts get-caller-identity
      - mkdir -p /tmp/artifacts
      - ls -al
      - rsync -av ./ /tmp/artifacts/
      - |
        HAS_TERRAFORM=false
        if [[ -d 'infra' ]]; then
          cd infra
          SSM_PATH="/$ENV/$APP_NAME/tfvars"
          echo "getting terraform config from ssm $SSM_PATH"
          aws ssm get-parameter --name "$SSM_PATH" --query 'Parameter.Value' --output text > config.tfvars
          cat config.tfvars
          HAS_TERRAFORM=true
        fi
  build:
    commands:
      - |
        if [[ $TERRAFORM_ENABLED == true && $HAS_TERRAFORM == true ]]; then
          echo "terraform state s3 path $TF_STATE_PATH"
          terraform init -backend-config="key=$TF_STATE_PATH"
          terraform plan -var-file="config.tfvars" -out=plan_out
          terraform show -no-color plan_out > plan_details.txt
          aws s3 cp ./plan_details.txt $S3_DEPLOY_ARTIFACT_BUCKET/
          cp config.tfvars /tmp/artifacts/
          cp plan_out  /tmp/artifacts/
        else
          echo "No terraform files found or terraform is disabled. Skipping terraform plan."
          echo "TERRAFORM_ENABLED $TERRAFORM_ENABLED"
          echo "HAS_TERRAFORM $HAS_TERRAFORM"
        fi
      - cd  /tmp
      - tar -czvf artifacts.tar.gz artifacts/

artifacts:
  files:
    - /tmp/artifacts.tar.gz
