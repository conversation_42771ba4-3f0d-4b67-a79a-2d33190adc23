resource "aws_codebuild_project" "deploy" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 1
  name                   = local.deploy_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    encryption_disabled    = false
    name                   = var.app_name
    override_artifact_name = false
    packaging              = "NONE"
    type                   = "CODEPIPELINE"
  }

  dynamic "vpc_config" {
    for_each = var.use_vpc ? [1] : []
    content {
      vpc_id             = "vpc-06474f6d3a210028b"
      subnets            = ["subnet-097383e7e4f76f0ec", "subnet-0dd620c23992d88ae", "subnet-0881390c25645dc44"]
      security_group_ids = ["sg-0b5a83ef94043e0d1"]
    }
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/cicd/codebuild:latest"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = var.privileged_mode
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "ENV"
      value = lower(var.app_env)
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "S3_URL_DEPLOY_REPORT_DIR"
      value = "s3://${var.report_bucket_name}/${local.deploy_project_name}"
      type  = "PLAINTEXT"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = local.deploy_project_name
      status      = "ENABLED"
      stream_name = local.deploy_project_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec = templatefile("${path.module}/deploy-buildspec.yml", {
      node_runtime_version = var.node_runtime_version
    })
    insecure_ssl        = false
    report_build_status = false
    type                = "CODEPIPELINE"
  }
}
