version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - echo "get git ssh key..."
      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa

      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
      - |
        if [[ $TERRAFORM_ENABLED == true ]]; then
           echo "terraform enabled"
           mv  tmp/artifacts.tar.gz ./
           tar -xzvf artifacts.tar.gz -C ./
           cd artifacts
        fi
      - |
        HAS_TERRAFORM=false
        if [[ -d 'infra' ]]; then
          HAS_TERRAFORM=true
        fi
      - |
        if [[ $TERRAFORM_ENABLED == true && $HAS_TERRAFORM == true ]]; then
          mv config.tfvars infra/
          mv plan_out infra/
          cd infra
          echo "terraform state s3 path $TF_STATE_PATH"
          terraform init -backend-config="key=$TF_STATE_PATH"
          terraform apply "plan_out"
          cd ../
          rm -rf infra
        fi
      - ENV_DIR=$(pwd)
      - sts=$(aws sts assume-role --role-arn $DEPLOY_ROLE_ARN --role-session-name k8s-deploy)
      - aws configure set aws_access_key_id "$(echo $sts | jq -r '.Credentials.AccessKeyId')" --profile deploy
      - aws configure set aws_secret_access_key "$(echo $sts | jq -r '.Credentials.SecretAccessKey')" --profile deploy
      - aws configure set aws_session_token "$(echo $sts | jq -r '.Credentials.SessionToken')" --profile deploy
      - aws sts get-caller-identity --profile deploy

  build:
    commands:
      - echo deploying $ENV
      - cd $ENV_DIR
      - echo $DEPLOYMENT_BUCKET
      - echo $S3_BUCKET
      - echo $S3_OBJECT_KEY
      - echo "deploy s3"
      - echo $DEPLOY_ROLE_ARN
      - aws s3 cp *.zip s3://$BUCKET_NAME/$BUCKET_PATH/ --profile deploy

cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'
