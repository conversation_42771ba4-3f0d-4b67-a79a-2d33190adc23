variable "deployment_app_name" {
  type = string
}

variable "deployment_group_name" {
  type = string
}

variable "deployment_config_name" {
  type = string
}

variable "service_role_arn" {
  type = string
}

variable "ecs_cluster_name" {
  type = string
}

variable "ecs_service_name" {
  type = string
}

variable "lb_listeners" {
  type = list(string)
}

variable "target_groups" {
  type = object({
    blue  = string
    green = string
  })
}
