resource "aws_sns_topic" "home-valuation-topic" {
  name                        = "${var.environment}-monarch-home-valuation-topic.fifo"
  fifo_topic                  = true
  content_based_deduplication = true

  tags = {
    Environment = var.environment
  }
}

resource "aws_sqs_queue" "home-valuation-queue" {
  name                        = "${var.environment}-monarch-home-valuation-stream.fifo"
  fifo_queue                  = true
  content_based_deduplication = true
  visibility_timeout_seconds  = 120

  tags = {
    Environment = var.environment
  }

  depends_on = [
    aws_sns_topic.home-valuation-topic
  ]
}

resource "aws_sqs_queue_policy" "home-valuation-queue-policy" {
  queue_url = aws_sqs_queue.home-valuation-queue.id
  policy    = jsonencode({
    Version   = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Principal = "*",
        Action    = "sqs:SendMessage",
        Resource  = aws_sqs_queue.home-valuation-queue.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = aws_sns_topic.home-valuation-topic.arn
          }
        }
      }
    ]
  })
}

resource "aws_sns_topic_subscription" "home-valuation-topic-subscription" {
  topic_arn            = aws_sns_topic.home-valuation-topic.arn
  protocol             = "sqs"
  endpoint             = aws_sqs_queue.home-valuation-queue.arn
  raw_message_delivery = true
}