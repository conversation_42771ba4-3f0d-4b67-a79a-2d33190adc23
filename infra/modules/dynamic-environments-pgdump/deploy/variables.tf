variable "app_name" {
  type = string
}

variable "app_env" {
  type = string
}

variable "data_env" {
  type = string
}

variable "env_vars" {
  description = "Extra env vars for deploy step"
  type        = list(object({
    name  = string
    value = string
    type  = string
  }))
  default = []
}


variable "build_bucket_name" {
  description = "Bucket to store deploy artifacts in."
  type        = string
  default     = "qv-deployment"
}

variable "report_bucket_name" {
  description = "Bucket to store reports artifacts in."
  type        = string
  default     = "qv-deployment-reports"
}

variable "service_role_arn" {
  description = "Service role ARN."
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-access-role"
}

variable "pipeline_bucket_name" {
  description = "The bucket the pipeline uses to store artifacts between stages"
  type        = string
  default     = "codepipeline-ap-southeast-2-***********"
}

variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-southeast-2"
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type        = string
  default     = "Pacific/Auckland"
}

variable "node_runtime_version" {
  description = "Node runtime version for deploy buildspecs"
  type        = string
  default     = "16"
}

locals {
  deploy_project_name = "${var.app_name}-deploy-${var.app_env}"
}
