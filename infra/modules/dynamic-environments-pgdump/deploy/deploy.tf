resource "aws_codebuild_project" "deploy" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 1
  name                   = local.deploy_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    encryption_disabled    = false
    name                   = var.app_name
    override_artifact_name = false
    packaging              = "NONE"
    type                   = "CODEPIPELINE"
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:6.0"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "IMAGE_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }
    environment_variable {
      name  = "ENV"
      value = lower(var.app_env)
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "DATA_ENV"
      value = lower(var.data_env)
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "DEPLOY_ROLE_ARN"
      value = "${lower(var.data_env)}-lambda-deploy-role-arn"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "S3_URL_DEPLOY_REPORT_DIR"
      value = "s3://${var.report_bucket_name}/${local.deploy_project_name}"
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "SLACK_WEBHOOK"
      value = "/${lower(var.data_env)}/deploy/slack-webhook"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "ENV_CONFIG"
      value = "/${lower(var.data_env)}/${var.app_name}/env"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "PRIVATE_GIT_SSH_KEY"
      value = "/${lower(var.data_env)}/git-ssh-key"
      type  = "PARAMETER_STORE"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = local.deploy_project_name
      status      = "ENABLED"
      stream_name = local.deploy_project_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec = templatefile("${path.module}/deploy-buildspec.yml", {
      node_runtime_version = var.node_runtime_version
    })
    insecure_ssl        = false
    report_build_status = false
    type                = "CODEPIPELINE"
  }
}
