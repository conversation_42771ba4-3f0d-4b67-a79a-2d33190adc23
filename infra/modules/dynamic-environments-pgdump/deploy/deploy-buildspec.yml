version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - BUILD_VERSION=$(cat version.txt)
      - ENV_DIR=$(pwd)
      - COMMIT_SHA=$(cat sha.txt)
      - BUILD_BRANCH=$(cat branch.txt)
      - MSG_START="$BUILD_VERSION $BUILD_BRANCH"
      - |
        echo '
          APP=dynamic-environments-pgdump
          ICON=:rocket:
          CHANNEL='#test-release'
          if [ "$DATA_ENV" = "prod" ]; then
            CHANNEL='#production'
          elif [ "$DATA_ENV" = "dev" ]; then
            CHANNEL='#builds'
          fi
          curl -X POST --data-urlencode "payload={\"channel\":\"$CHANNEL\",\"username\":\"$PROJECT_NAME $ENV\",\"text\":\"$APP: $1\",\"icon_emoji\":\"$ICON\"}" $SLACK_WEBHOOK
        ' > ~/slack.sh
        chmod +x ~/slack.sh
        read -ra ARN_BITS <<<"$(echo $CODEBUILD_BUILD_ARN | sed 's/[:|\/]/ /g')"
        LINK="https://$AWS_REGION.console.aws.amazon.com/codesuite/codebuild/$${ARN_BITS[4]}/projects/$${ARN_BITS[6]}/build/$CODEBUILD_BUILD_ID/?region=$AWS_REGION"
        ~/slack.sh ":checkered_flag: $MSG_START Deploy starting. Env: $ENV, data: $DATA_ENV \\n$LINK" || true
      - ECR_ACCOUNT_ID=************
      - ECR_REGION=ap-southeast-2
      - ECR_URL=$ECR_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/cicd
      - DOCKER_BASE_IMAGE=node:18-alpine
      - DOCKER_IMAGE_FILENAME=docker_image.tar
      - echo Logging in to ECR...
      - aws ecr get-login-password  | docker login --username AWS --password-stdin $ECR_URL$/IMAGE_NAME
      - echo Pulling base docker image...
      - docker pull $ECR_URL/$DOCKER_BASE_IMAGE
      - docker tag $ECR_URL/$DOCKER_BASE_IMAGE $DOCKER_BASE_IMAGE

  build:
    commands:
      - cd $ENV_DIR
      - echo deploying $ENV
      - cd pgdump
      - echo "$ENV_CONFIG" > .env
      - ls -la
      - echo Building docker image...
      - cd ../
      - echo "$PRIVATE_GIT_SSH_KEY" > id_rsa
      - docker build -t $IMAGE_NAME -f pgdump/Dockerfile .
      - docker tag dynamic-environments-pgdump $ECR_URL/$IMAGE_NAME:latest
      - docker push $ECR_URL/$IMAGE_NAME:latest
      - '~/slack.sh ":white_check_mark: $MSG_START Deploy SUCCEEDED" || true'

cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'
