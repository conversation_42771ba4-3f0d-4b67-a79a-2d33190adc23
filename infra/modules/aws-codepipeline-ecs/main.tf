data "aws_kms_key" "pipeline" {
  key_id = "alias/monarch-pipeline-key"
}

resource "aws_codepipeline" "this" {
  name     = "${var.service_name}-pipeline"
  role_arn = var.pipeline_role

  artifact_store {
    location = "codepipeline-ap-southeast-2-49010065853"
    type     = "S3"
    encryption_key {
      id   = data.aws_kms_key.pipeline.arn
      type = "KMS"
    }
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["build_output"]

      configuration = {
        S3Bucket             = var.source_bucket
        S3ObjectKey          = var.source_path
        PollForSourceChanges = "true"
      }
    }
  }

  dynamic "stage" {
    for_each = var.deployments
    content {
      name = "${stage.value["environment"]}-Deploy"

      action {
        category  = "Approval"
        name      = "Approve-${stage.value["environment"]}"
        owner     = "AWS"
        provider  = "Manual"
        run_order = 1
        version   = "1"

        configuration = {
          CustomData = "Approve deployment of ${var.service_name} to ${stage.value["environment"]} environment"
        }
      }

      action {
        category        = "Deploy"
        name            = "Deploy-${stage.value["environment"]}"
        owner           = "AWS"
        provider        = "CodeDeployToECS"
        input_artifacts = ["build_output"]
        run_order       = 2
        role_arn        = stage.value["deployment_role"]
        version         = "1"


        configuration = {
          ApplicationName                = stage.value["application_name"]
          DeploymentGroupName            = stage.value["deployment_group_name"]
          TaskDefinitionTemplateArtifact = "build_output"
          TaskDefinitionTemplatePath     = stage.value["taskdef_path"]
          AppSpecTemplateArtifact        = "build_output"
          AppSpecTemplatePath            = "appspec.yml"
          Image1ArtifactName             = "build_output"
          Image1ContainerName            = "IMAGE1_NAME"
        }

      }
    }
  }
}
