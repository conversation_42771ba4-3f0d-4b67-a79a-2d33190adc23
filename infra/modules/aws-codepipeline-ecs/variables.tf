variable "service_name" {
  type = string
}

variable "pipeline_role" {
  type = string
}

variable "source_bucket" {
  type    = string
  default = "qv-deployment"
}

variable "source_path" {
  type = string
}

variable "deployments" {
  type = list(object({
    environment           = string
    application_name      = string
    deployment_group_name = string
    deployment_role       = string
    taskdef_path          = string
  }))
}
