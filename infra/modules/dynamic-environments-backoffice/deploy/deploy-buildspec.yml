version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
      - cd ui
      - npm install
      - cd ../common
      - npm install --include=dev
      - cd ..
      - sts=$(aws sts assume-role --role-arn $DEPLOY_ROLE_ARN --role-session-name lambda-deploy)
      - aws configure set aws_access_key_id "$(echo $sts | jq -r '.Credentials.AccessKeyId')" --profile deploy
      - aws configure set aws_secret_access_key "$(echo $sts | jq -r '.Credentials.SecretAccessKey')" --profile deploy
      - aws configure set aws_session_token "$(echo $sts | jq -r '.Credentials.SessionToken')" --profile deploy
      - aws sts get-caller-identity --profile deploy
      - BUILD_VERSION=$(cat version.txt)
      - ENV_DIR=$(pwd)

      - COMMIT_SHA=$(cat sha.txt)
      - BUILD_BRANCH=$(cat branch.txt)
      - MSG_START="$BUILD_VERSION $BUILD_BRANCH"
      - |
        echo '
          APP=🪣
          ICON=:rocket:
          CHANNEL='#test-release'
          if [ "$DATA_ENV" = "prod" ]; then
            CHANNEL='#production'
          elif [ "$DATA_ENV" = "dev" ]; then
            CHANNEL='#builds'
          fi
          curl -X POST --data-urlencode "payload={\"channel\":\"$CHANNEL\",\"username\":\"$PROJECT_NAME - $BUILD_BRANCH\",\"text\":\"$APP: $1\",\"icon_emoji\":\"$ICON\"}" $SLACK_WEBHOOK
        ' > ~/slack.sh
        chmod +x ~/slack.sh
        read -ra ARN_BITS <<<"$(echo $CODEBUILD_BUILD_ARN | sed 's/[:|\/]/ /g')"
        LINK="https://$AWS_REGION.console.aws.amazon.com/codesuite/codebuild/$${ARN_BITS[4]}/projects/$${ARN_BITS[6]}/build/$CODEBUILD_BUILD_ID/?region=$AWS_REGION"
        ~/slack.sh ":checkered_flag: $MSG_START Deploy starting. \n\t- *Env*: $ENV\n\t- *Data*: $DATA_ENV\n\t- <$LINK|*Codebuild*>" || true
      - RELATIVE_REPORT_PATH=$ENV/$BUILD_VERSION
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR/$RELATIVE_REPORT_PATH/
      - TEST_OUTPUT_DIRECTORY=/output/test
      - mkdir -p $TEST_OUTPUT_DIRECTORY
  build:
    commands:
      - cd $ENV_DIR
      - echo deploying $ENV
      - cd ui
      - echo "$ENV_CONFIG" > .env
      - npm run build
      - aws s3 sync dist "s3://$UI_SITE_BUCKET/" --profile deploy
      - aws cloudfront create-invalidation --distribution-id "$CLOUDFRONT_DIST" --paths "/*" --profile deploy

      - '~/slack.sh ":white_check_mark: $MSG_START Deploy SUCCEEDED" || true'

