version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - COMMIT_MESSAGE
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
      - TEST_FAILED=":x:"
      - TEST_PASSED=":approved:"

  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
      - echo "$TEST_CONFIG" > test/config.json
      - echo $TEST_TYPES

  build:
    on-failure: CONTINUE
    commands:
      - S3_URL_DEPLOY_REPORT_DIR=s3://$REPORT_BUCKET_NAME/$ENV
      - RELATIVE_REPORT_PATH=/$PROJECT_NAME/$DEPLOYMENT_ID
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR$RELATIVE_REPORT_PATH
      - TEST_OUTPUT_DIRECTORY=/output/test
      - mkdir -p $TEST_OUTPUT_DIRECTORY
      - AWESOME_REPORTER_OUTPUT=mochawesome-report

      - |
        if [ -n "$SRC_PATH"  ]; then
            cd $SRC_PATH
        fi

      - echo "getting codeartifact auth token"
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --region ap-southeast-2 --query authorizationToken --output text`
      - npm install --include=dev

      - |
        if [[ "$TEST_TYPES" =~ lint ]]; then
          echo "Running lint tests..."
          mkdir $TEST_OUTPUT_DIRECTORY/lint
          npm run lint > $TEST_OUTPUT_DIRECTORY/lint/linter.txt || true
        else
          echo "Skipping lint tasks."
        fi

      - |
        if [[ "$TEST_TYPES" =~ unit ]]; then
          echo "Running unit tests..."
          CMD="$UNIT_TEST || UNIT_PASSED=$TEST_FAILED"
          eval $CMD
          mkdir $TEST_OUTPUT_DIRECTORY/unit -p
          if [ -d "$AWESOME_REPORTER_OUTPUT" ]; then
            mv $AWESOME_REPORTER_OUTPUT/*  $TEST_OUTPUT_DIRECTORY/unit/
            rm -rf $AWESOME_REPORTER_OUTPUT
          else
            echo "$AWESOME_REPORTER_OUTPUT does not exist."
          fi
        else
          echo "Skipping unit tests."
        fi

      - |
        if [[ "$TEST_TYPES" =~ smoke ]]; then
          echo "Running smoke tests..."
          CMD="$SMOKE_TEST || SMOKE_PASSED=$TEST_FAILED"
          eval $CMD
          mkdir $TEST_OUTPUT_DIRECTORY/smoke -p
          if [ -d "$AWESOME_REPORTER_OUTPUT" ]; then
            mv $AWESOME_REPORTER_OUTPUT/*  $TEST_OUTPUT_DIRECTORY/smoke/
            rm -rf $AWESOME_REPORTER_OUTPUT
          else
            echo "$AWESOME_REPORTER_OUTPUT does not exist."
          fi
        else
          echo "Skipping unit tests."
        fi  

      - |
        if [[ "$TEST_TYPES" =~ integration ]]; then
          echo "Running integration tests..."
          CMD="$INTEGRATION_TEST || INTEGRATION_PASSED=$TEST_FAILED"
          eval $CMD
          mkdir $TEST_OUTPUT_DIRECTORY/integration -p
          if [ -d "$AWESOME_REPORTER_OUTPUT" ]; then
            mv $AWESOME_REPORTER_OUTPUT/*  $TEST_OUTPUT_DIRECTORY/integration/
            rm -rf $AWESOME_REPORTER_OUTPUT
          else
            echo "$AWESOME_REPORTER_OUTPUT does not exist."
          fi
          
        else
          echo "Skipping integration tests."
        fi  

      - |
        if [[ "$TEST_TYPES" =~ coverage ]]; then
          echo "Running coverage..."
          mkdir coverage
          npm run cover || true
          mv coverage $TEST_OUTPUT_DIRECTORY/coverage
        else
          echo "Skipping coverage tests."
        fi  

      - cd $TEST_OUTPUT_DIRECTORY
      - ls -al
      - echo "uploading test output to $TEST_REPORT_UPLOAD_PATH"
      - aws s3 cp ./ $TEST_REPORT_UPLOAD_PATH --recursive || true
  post_build:
    commands:
      - echo Build $BUILD_VERSION completed on `date`
