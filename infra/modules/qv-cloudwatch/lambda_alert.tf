resource "aws_cloudwatch_log_metric_filter" "lambda_error_metric_filter" {
  name           = "${var.env}-lambda-error-filter"
  log_group_name = aws_cloudwatch_log_group.lambda_log_group.name
  pattern        = "{ $.severity = \"ERROR\" }"

  metric_transformation {
    name      = "${var.env}-lambda-ErrorCount"
    namespace = "${var.env}-lambda-LogMetrics"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "error_alarm" {
  alarm_name          = "${var.env}-lambda-errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = aws_cloudwatch_log_metric_filter.lambda_error_metric_filter.metric_transformation[0].name
  namespace           = aws_cloudwatch_log_metric_filter.lambda_error_metric_filter.metric_transformation[0].namespace
  period              = var.alert_period
  statistic           = "Sum"
  threshold           = var.alert_threshold
  dimensions = {
  }
  alarm_description = "Alert for ${var.env}: Lambda service errors detected in logs."
  alarm_actions = [aws_sns_topic.service_errors_alert_topic.arn]

  tags = {
    costCentre = var.env
  }

  depends_on = [aws_cloudwatch_log_metric_filter.lambda_error_metric_filter]
}


