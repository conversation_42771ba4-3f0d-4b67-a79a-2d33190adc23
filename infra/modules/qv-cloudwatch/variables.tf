variable "env" {
  description = "The environment name."
  validation {
    condition     = length(var.env) > 0
    error_message = "You must provide a valid environment name."
  }
  type = string
}

variable "aws_account" {
  type    = string
  default = "************"
}

variable "alert_function" {
  type    = string
  default = "qv-utils-dev-errorAlerts"
}

variable "qvconz_cloudwatch_group" {
  type    = string
  default = ""
}

variable "alert_period" {
  type    = number
  default = 60
}

variable "alert_threshold" {
  type    = number
  default = 5
}

variable "lambda_services" {
  type = list(string)
  default = [
    "api-ta",
    "api-stream",
    "api-objection",
    "api-picklist",
    "api-report-generator",
    "api-sales",
    "api-stats",
    "api-floor-plan",
    "api-maps",
    "api-pdf-report-generator",
    "api-property",
    "api-rtv",
    "api-sale-analysis",
    "api-search",
    "api-worksheet",
    "api-public-property-details",
    "api-public-reports",
    "api-testfactory",
    "api-consent",
    "api-load"
  ]
}

variable "java_services" {
  type = list(string)
  default = [
    "monarch-web",
    "property",
    "classification",
    "home-valuation",
    "media",
    "reporting",
    "ereporting",
    "roll-maintenance",
    "sale",
    "sale-analysis",
    "user-profile"
  ]
}

variable "enable_qvconz" {
  type    = bool
  default = true
}
