locals {
  lambda_cloudwatch_groups_source = join(" | ", [
    for group in [aws_cloudwatch_log_group.lambda_log_group.name] :"SOURCE '${group}'"
  ])

  java_cloudwatch_group         = "/aws/containerinsights/kubernetes-${var.env}/application"
  java_cloudwatch_groups_source = "SOURCE '${local.java_cloudwatch_group}'"

  qvconz_cloudwatch_group         = var.qvconz_cloudwatch_group!="" ? var.qvconz_cloudwatch_group : "/ecs/${var.env}qvconz"
  qvconz_cloudwatch_groups_source = "SOURCE '${local.qvconz_cloudwatch_group}'"

  lambda_variables_values = concat(
    [
      {
        value = "!= ''"
        label = "all"
      }
    ],
    [
      for service in var.lambda_services : {
      value = "= '${service}'"
      label = service
    }
    ]
  )
  java_variables_values = concat(
    [
      {
        value = "!= ''"
        label = "all"
      }
    ],
    [
      for service in var.java_services : {
      value = "= '${service}'"
      label = service
    }
    ]
  )

  widget_lambda_error_logs = {
    type   = "log"
    x      = 0
    y      = 0
    width  = 24
    height = 6
    properties = {
      query   = <<EOF
${local.lambda_cloudwatch_groups_source}
| filter severity = "ERROR"
| filter service = 'lambda_service'
| display @timestamp, service, @entity.Attributes.Lambda.Function, errorCode, message
| sort @timestamp desc
EOF
      title   = "${var.env} lambda Error Log Events"
      stacked = false
      view    = "table"
    }
  }

  widget_lambda_error_count_by_code = {
    type   = "log"
    x      = 0
    y      = 12
    width  = 12
    height = 6
    properties = {
      query   = <<EOF
${local.lambda_cloudwatch_groups_source}
| filter severity = "ERROR"
| filter service = 'lambda_service'
| stats count(*) as error_count by errorCode
| sort error_count desc
EOF
      title   = "Lambda Error Count by ErrorCode"
      stacked = false
      view    = "table"
    }
  }

  widget_lambda_error_count_by_service = {
    type   = "log"
    x      = 12
    y      = 12
    width  = 12
    height = 6
    properties = {
      query   = <<EOF
${local.lambda_cloudwatch_groups_source}
| filter severity = "ERROR"
| stats count(*) as error_count by service
| sort service desc
EOF
      title   = "Lambda Error Count by Service"
      stacked = false
      view    = "table"
    }
  }

  widget_java_error_logs = {
    type   = "log"
    x      = 0
    y      = 18
    width  = 24
    height = 6
    properties = {
      query   = <<EOF
${local.java_cloudwatch_groups_source}
| fields kubernetes.container_name as service
| parse log '"errorCode":"*"' as errorCode
| parse log '"message":"*"' as message
| parse log '"level":"*"' as level
| filter service = 'java_service'
| filter level = "ERROR"
| display service, errorCode, message, @timestamp
| sort @timestamp desc
EOF
      title   = "${var.env} k8s Error Log Events"
      stacked = false
      view    = "table"
    }
  }

  widget_java_error_count_by_code = {
    type   = "log"
    x      = 0
    y      = 24
    width  = 12
    height = 6
    properties = {
      query   = <<EOF
${local.java_cloudwatch_groups_source}
| fields kubernetes.container_name as service
| parse log '"errorCode":"*"' as errorCode
| parse log '"message":"*"' as message
| parse log '"level":"*"' as level
| filter service = 'java_service'
| filter level = "ERROR"
| stats count(*) as error_count by errorCode
| sort error_count desc
EOF
      title   = "Java Error Count by ErrorCode"
      stacked = false
      view    = "table"
    }
  }

  widget_java_error_count_by_service = {
    type   = "log"
    x      = 12
    y      = 24
    width  = 12
    height = 6
    properties = {
      query   = <<EOF
${local.java_cloudwatch_groups_source}
| fields kubernetes.container_name as service
| parse log '"errorCode":"*"' as errorCode
| parse log '"message":"*"' as message
| parse log '"level":"*"' as level
| filter level = "ERROR"
| stats count(*) as error_count by service
| sort error_count desc
EOF
      title   = "Java Error Count by Service"
      stacked = false
      view    = "table"
    }
  }

  widget_qvconz_error_logs = {
    type   = "log"
    x      = 0
    y      = 30
    width  = 24
    height = 6
    properties = {
      query   = <<EOF
${local.qvconz_cloudwatch_groups_source}
| fields @message
| display message, code, stacktrace, @timestamp
| filter level = 'ERROR'
| sort @timestamp desc
EOF
      title   = "${var.env} qvconz Error Log Events"
      stacked = false
      view    = "table"
    }
  }

  widget_qvconz_error_count_by_code = {
    type   = "log"
    x      = 0
    y      = 36
    width  = 24
    height = 6
    properties = {
      query   = <<EOF
${local.qvconz_cloudwatch_groups_source}
| fields @message
| display message, code, stacktrace
| filter level = 'ERROR'
| stats count(*) as error_count by code
| sort error_count desc
EOF
      title   = "Qvconz Error Count by ErrorCode"
      stacked = false
      view    = "table"
    }
  }

}

resource "aws_cloudwatch_dashboard" "log_dashboard" {
  dashboard_name = "${var.env}-service-Error-Logs"

  dashboard_body = jsonencode({
    variables = [
      {
        type      = "pattern"
        pattern   = "= 'lambda_service'"
        inputType = "select"
        id        = "lambda_service"
        label     = "lambda service"
        visible   = true
        values    = local.lambda_variables_values
      },
      {
        type      = "pattern"
        pattern   = "= 'java_service'"
        inputType = "select"
        id        = "java_service"
        label     = "java service"
        visible   = true
        values    = local.java_variables_values
      }
    ]
    widgets = concat(
      [
        local.widget_lambda_error_logs,
        local.widget_lambda_error_count_by_code,
        local.widget_lambda_error_count_by_service,
        local.widget_java_error_logs,
        local.widget_java_error_count_by_code,
        local.widget_java_error_count_by_service,
      ],
        var.enable_qvconz ? [
        local.widget_qvconz_error_logs,
        local.widget_qvconz_error_count_by_code
      ] : []
    )

  })
}