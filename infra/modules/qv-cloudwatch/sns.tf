resource "aws_sns_topic" "service_errors_alert_topic" {
  name = "${var.env}-service-errors"
  tags = {
    costCentre = var.env
  }
}

resource "aws_sns_topic_subscription" "lambda_subscription" {
  topic_arn = aws_sns_topic.service_errors_alert_topic.arn
  protocol  = "lambda"
  endpoint  = "arn:aws:lambda:ap-southeast-2:${var.aws_account}:function:${var.alert_function}"
}

resource "aws_lambda_permission" "allow_sns_invoke" {
  statement_id  = "AllowSNSToInvoke-${var.env}"
  action        = "lambda:InvokeFunction"
  function_name = var.alert_function
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.service_errors_alert_topic.arn
}