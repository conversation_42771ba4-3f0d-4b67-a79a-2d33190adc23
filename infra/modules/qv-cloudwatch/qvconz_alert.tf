resource "aws_cloudwatch_log_metric_filter" "qvconz_error_metric_filter" {
  count          = var.enable_qvconz ? 1 : 0
  name           = "${var.env}-qvconz-error-filter"
  log_group_name = local.qvconz_cloudwatch_group
  pattern        = "{ $.level = \"ERROR\" }"

  metric_transformation {
    name      = "${var.env}-qvconz-ErrorCount"
    namespace = "${var.env}-qvconz-LogMetrics"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "qvconz_error_alarm" {
  count               = var.enable_qvconz ? 1 : 0
  alarm_name          = "${var.env}-qvconz-errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = aws_cloudwatch_log_metric_filter.qvconz_error_metric_filter[0].metric_transformation[0].name
  namespace           = aws_cloudwatch_log_metric_filter.qvconz_error_metric_filter[0].metric_transformation[0].namespace
  period              = var.alert_period
  statistic           = "Sum"
  threshold           = var.alert_threshold
  dimensions = {
  }
  alarm_description = "Alert for ${var.env}: qvconz errors detected in logs."
  alarm_actions = [aws_sns_topic.service_errors_alert_topic.arn]

  tags = {
    costCentre = var.env
  }

  depends_on = [aws_cloudwatch_log_metric_filter.qvconz_error_metric_filter]
}


