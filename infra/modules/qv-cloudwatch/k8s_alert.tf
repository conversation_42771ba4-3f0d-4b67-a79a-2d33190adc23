locals {
  pattern = "\"level\\\":\\\"ERROR\""
}
resource "aws_cloudwatch_log_metric_filter" "java_error_metric_filter" {
  name           = "${var.env}-java-error-filter"
  log_group_name = local.java_cloudwatch_group
  pattern = replace(local.pattern, "\\", "\\\\\\")


  metric_transformation {
    name      = "${var.env}-Java-ErrorCount"
    namespace = "${var.env}-Java-LogMetrics"
    value     = "1"
  }
}
resource "aws_cloudwatch_metric_alarm" "java_error_alarm" {
  alarm_name          = "${var.env}-java-errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = aws_cloudwatch_log_metric_filter.java_error_metric_filter.metric_transformation[0].name
  namespace           = aws_cloudwatch_log_metric_filter.java_error_metric_filter.metric_transformation[0].namespace
  period              = var.alert_period
  statistic           = "Sum"
  threshold           = var.alert_threshold
  dimensions = {
  }
  alarm_description = "Alert for ${var.env}: Java service errors detected in logs."
  alarm_actions = [aws_sns_topic.service_errors_alert_topic.arn]

  tags = {
    costCentre = var.env
  }

  depends_on = [aws_cloudwatch_log_metric_filter.java_error_metric_filter]
}


