variable "project_name" {
  description = "Base name for the project that this notification rule will be attached to"
  type = string
}

variable "codebuild_arn" {
  description = "Codebuild ARN to attach the notifications rule to."
  type = string
}

data "aws_sns_topic" "cicd_notification" {
  name = "build-notification"
}

resource "aws_codestarnotifications_notification_rule" "notify" {
  detail_type = "FULL"
  event_type_ids = [
    "codebuild-project-build-state-failed",
    "codebuild-project-build-state-succeeded",
  ]
  name     = "${var.project_name}-author-notification"
  resource = var.codebuild_arn
  status   = "ENABLED"

  target {
    address = data.aws_sns_topic.cicd_notification.arn
    type    = "SNS"
  }
}