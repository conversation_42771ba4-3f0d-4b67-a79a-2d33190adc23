module "kaba-ui-deploy" {
  source = "../aws-codedeploy-group"

  ecs_cluster_name = aws_ecs_cluster.kaba-app-cluster.name
  ecs_service_name = module.kaba-ui-service.service_name

  deployment_app_name    = "kaba-app"
  deployment_group_name  = "kaba-ui-${var.environment}"
  deployment_config_name = "CodeDeployDefault.ECSAllAtOnce"
  service_role_arn       = data.aws_iam_role.service_role.arn
  lb_listeners           = [aws_lb_listener.kaba-ui-listener.arn]
  target_groups = {
    blue  = aws_lb_target_group.kaba-ui-tg-blue.name
    green = aws_lb_target_group.kaba-ui-tg-green.name
  }
}

module "kaba-api-deploy" {
  source = "../aws-codedeploy-group"

  ecs_cluster_name = aws_ecs_cluster.kaba-app-cluster.name
  ecs_service_name = module.kaba-api-service.service_name

  deployment_app_name    = "kaba-app"
  deployment_group_name  = "kaba-api-${var.environment}"
  deployment_config_name = "CodeDeployDefault.ECSAllAtOnce"
  service_role_arn       = data.aws_iam_role.service_role.arn
  lb_listeners           = [aws_lb_listener.kaba-api-listener.arn]
  target_groups = {
    blue  = aws_lb_target_group.kaba-api-tg-blue.name
    green = aws_lb_target_group.kaba-api-tg-green.name
  }
}
