resource "aws_security_group" "lb-security-group" {
  name        = "${local.cluster_name}-${var.environment}-lb-security-group"
  description = "Allow HTTP inbound traffic"
  vpc_id      = data.aws_vpc.vpc.id

  ingress {
    description = "QV Wired - UI"
    from_port   = 80
    to_port     = 80
    protocol    = "TCP"
    cidr_blocks = ["10.0.0.0/8", "*********/8"]
  }

  ingress {
    description = "QV Wired - API"
    from_port   = 8080
    to_port     = 8080
    protocol    = "TCP"
    cidr_blocks = ["10.0.0.0/8", "*********/8"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_security_group" "ecs-security-group" {
  name        = "${local.cluster_name}-${var.environment}-ecs-security-group"
  description = "Allow inbound traffic from LB"
  vpc_id      = data.aws_vpc.vpc.id

  ingress {
    description     = "Load balancer"
    from_port       = 80
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.lb-security-group.id]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}
