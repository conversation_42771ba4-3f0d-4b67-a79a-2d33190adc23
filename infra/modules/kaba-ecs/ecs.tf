resource "aws_ecs_cluster" "kaba-app-cluster" {
  name               = "${local.cluster_name}-${var.environment}"
  capacity_providers = ["FARGATE", "FARGATE_SPOT"]
}

###### KABA UI ######
module "kaba-ui-service" {
  source = "../aws-ecs-service"

  cluster_name = aws_ecs_cluster.kaba-app-cluster.name
  service_name = "kaba-ui"
  environment  = var.environment

  max_instances  = 2
  min_instances  = 1
  container_name = "kaba-ui"
  container_port = 80
  task_definition = jsonencode([
    {
      name  = "kaba-ui"
      image = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/analytics/kaba-ui:4.6-28-g251d5f1-SNAPSHOT"
      portMappings = [
        {
          containerPort = 80
          hostPort      = 80
        }
      ]
      environment = [
        {
          name  = "ASPNETCORE_URLS"
          value = "http://+:80"
        },
        {
          name  = "KABA_ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "KABA_API_URL"
          value = "http://${aws_lb.lb.dns_name}:8080/api"
        }
      ]
    }
  ])

  security_groups   = [aws_security_group.ecs-security-group.id]
  subnets           = var.subnets
  target_group_name = "kaba-ui-${var.environment}-green"

  depends_on = [
    aws_ecs_cluster.kaba-app-cluster,
    aws_lb.lb,
    aws_lb_target_group.kaba-ui-tg-green
  ]
}

###### KABA API ######
module "kaba-api-service" {
  source = "../aws-ecs-service"

  cluster_name = aws_ecs_cluster.kaba-app-cluster.name
  service_name = "kaba-api"
  environment  = var.environment

  max_instances  = 2
  min_instances  = 1
  container_name = "kaba-api"
  container_port = 8080
  task_definition = jsonencode([
    {
      name  = "kaba-api",
      image = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/analytics/kaba-api:4.6-31-g2048b52-SNAPSHOT"
      portMappings = [
        {
          containerPort = 8080
          hostPort      = 8080
        }
      ]
      environment = [
        {
          name  = "ASPNETCORE_URLS"
          value = "http://+:8080"
        },
        {
          name  = "KABA_CONTEXT_STRING"
          value = "Server=tableau-poc-03.c2unr0sd7noa.ap-southeast-2.redshift.amazonaws.com;Port=5439;Database=dw_test;Username=qvadmin;Password=**************;Command Timeout=120"
        },
        {
          name  = "QIVS_CONTEXT_STRING"
          value = "server=DEVAWSDB01;database=qvnz_monarch_test;user=kaba_services_test;password=$V9E#G7reMNqmrb"
        },
        {
          name  = "QIVS_URL"
          value = "http://testawsinweb01-qivs/default.asp?"
        }
      ]
    }
  ])

  security_groups   = [aws_security_group.ecs-security-group.id]
  subnets           = var.subnets
  target_group_name = "kaba-api-${var.environment}-green"

  depends_on = [
    aws_ecs_cluster.kaba-app-cluster,
    aws_lb.lb,
    aws_lb_target_group.kaba-api-tg-green
  ]
}


