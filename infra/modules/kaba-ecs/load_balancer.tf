resource "aws_lb" "lb" {
  name               = "${local.cluster_name}-${var.environment}-lb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.lb-security-group.id]
  subnets            = var.subnets

  idle_timeout = 30

  enable_deletion_protection = false
}

resource "aws_lb_target_group" "kaba-ui-tg-blue" {
  name        = "kaba-ui-${var.environment}-blue"
  port        = 80
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.aws_vpc.vpc.id

  health_check {
    timeout  = "5"
    interval = "30"
    matcher  = "200"
    path     = "/app/index.html"
  }
}

resource "aws_lb_target_group" "kaba-ui-tg-green" {
  name        = "kaba-ui-${var.environment}-green"
  port        = 80
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.aws_vpc.vpc.id

  health_check {
    timeout  = "5"
    interval = "30"
    matcher  = "200"
    path     = "/app/index.html"
  }
}

resource "aws_lb_listener" "kaba-ui-listener" {
  load_balancer_arn = aws_lb.lb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kaba-ui-tg-green.arn
  }

  lifecycle {
    ignore_changes = [default_action]
  }
}

resource "aws_lb_target_group" "kaba-api-tg-blue" {
  name        = "kaba-api-${var.environment}-blue"
  port        = 8080
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.aws_vpc.vpc.id

  health_check {
    timeout  = "5"
    interval = "30"
    matcher  = "200"
    path     = "/api/locations/territorial-authorities"
  }
}

resource "aws_lb_target_group" "kaba-api-tg-green" {
  name        = "kaba-api-${var.environment}-green"
  port        = 8080
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.aws_vpc.vpc.id

  health_check {
    timeout  = "5"
    interval = "30"
    matcher  = "200"
    path     = "/api/locations/territorial-authorities"
  }
}

resource "aws_lb_listener" "kaba-api-listener" {
  load_balancer_arn = aws_lb.lb.arn
  port              = "8080"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kaba-api-tg-green.arn
  }

  lifecycle {
    ignore_changes = [default_action]
  }
}

