locals {
  default_testFactoryHost = var.testFactoryHost != "" ? var.testFactoryHost : "https://${var.app_env}.qvapi.co.nz/testfactory"
}


resource "aws_ssm_parameter" "infra_yml_param" {
  name        = "/${var.app_env}/${var.app_name}/infra"
  description = "infra.yml for ${var.app_env} ${var.app_name}"
  type        = "String"
  value       = var.infra_yml
}


resource "aws_ssm_parameter" "test_config_param" {
  name        = "/${var.app_env}/${var.app_name}-test-config"
  description = "config required for running tests for ${var.app_env} ${var.app_name}"
  type        = "String"
  value       = jsonencode({
    apiKey            = var.api_key
    host              = var.host
    testFactoryApiKey = var.api_key
    testFactoryHost   = local.default_testFactoryHost
  })
}
