resource "aws_ssm_parameter" "infra_parameter" {
  name        = "/${var.env}/${var.app_name}/infra"
  description = "infra yml for ${var.app_name}."
  tier        = "Advanced"
  type        = "String"
  data_type   = "text"
  value       = <<EOF
${var.infra_content}
domainName: "${var.env}.qvapi.co.nz"
certificateName: "${var.env}.qvapi.co.nz"
basePath: "${replace(var.app_name, "api-", "")}"
costCentre: ${var.env}
EOF
}