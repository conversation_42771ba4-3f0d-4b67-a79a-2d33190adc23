locals {
  build_project_name = "${var.app_name}-build"
  build_base_s3_url = "s3://${var.build_bucket_name}/${var.bucket_path_override != "" ? var.bucket_path_override : var.app_name}/build"
}

resource "aws_codebuild_project" "build" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 3
  name                   = local.build_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    type                   = "NO_ARTIFACTS"
    encryption_disabled    = false
    override_artifact_name = false
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }


  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = var.image
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = var.privileged_mode
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "BUILD_BASE_S3_URL"
      value = local.build_base_s3_url
      type  = "PLAINTEXT"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = var.app_name
      status      = "ENABLED"
      stream_name = var.app_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec           = var.buildspec_path
    git_clone_depth     = 0
    insecure_ssl        = false
    location            = var.github_repo
    report_build_status = true
    type                = "GITHUB"

    git_submodules_config {
      fetch_submodules = false
    }
  }
}

resource "aws_codebuild_webhook" "build" {
  depends_on   = [aws_codebuild_project.build]
  build_type   = "BUILD"
  project_name = local.build_project_name

  filter_group {
    filter {
      exclude_matched_pattern = false
      pattern                 = "PUSH, PULL_REQUEST_CREATED, PULL_REQUEST_MERGED"
      type                    = "EVENT"
    }
    filter {
      exclude_matched_pattern = false
      pattern                 = var.source_file_path_regex
      type                    = "FILE_PATH"
    }
    filter {
      exclude_matched_pattern = true
      pattern                 = ".*\\bWIP\\b.*"
      type                    = "COMMIT_MESSAGE"
    }
  }
}

module "codebuild-notification" {
  source        = "../../codebuild-sns"
  codebuild_arn = aws_codebuild_project.build.arn
  project_name  = var.app_name
}
