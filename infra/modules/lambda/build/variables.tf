variable "app_name" {
  type = string
}

variable "bucket_path_override" {
  type = string
  default = ""
}

variable "github_repo" {
  description = "URL to clone the repo from"
  type        = string
}

variable "source_file_path_regex" {
  description = "Builds will only trigger if newer commits have changed a file name/path that matches this regex."
  type        = string
  default     = ".*"
}

variable "buildspec_path" {
  description = "Path to the buildspec file in the project."
  type        = string
  default     = "buildspec.yml"
}

variable "build_bucket_name" {
  description = "Bucket to store deploy artifacts in."
  type        = string
  default     = "qv-deployment"
}

variable "service_role_arn" {
  description = "Service role ARN."
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-access-role"
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type        = string
  default     = "Pacific/Auckland"
}

variable "privileged_mode" {
    description = "Enable privileged mode for the build container."
    type        = bool
    default     = false
}

variable "image" {
    description = "Codebuild docker image"
    type        = string
    default     = "aws/codebuild/standard:7.0"
}
