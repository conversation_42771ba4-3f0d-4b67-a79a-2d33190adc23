version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - ls -al
      - |
        if [[ $TERRAFORM_ENABLED == true ]]; then
           mv  tmp/artifacts.tar.gz ./
           tar -xzvf artifacts.tar.gz -C ./
           cd artifacts
        fi
      - ls -al
      - |
        HAS_TERRAFORM=false
        if [[ -d 'infra' ]]; then
          HAS_TERRAFORM=true
        fi
      - |
        if [[ $TERRAFORM_ENABLED == true && $HAS_TERRAFORM == true ]]; then
          mv config.tfvars infra/
          mv plan_out infra/
          cd infra
          echo "terraform state s3 path $TF_STATE_PATH"
          terraform init -backend-config="key=$TF_STATE_PATH" --reconfigure
          terraform apply "plan_out"
          terraform output -json > ../terraform_output.json
          cd ../
          rm -rf infra
        fi
      - BUILD_VERSION=$(cat version.txt)
      - ENV_DIR=$(pwd)
      - sts=$(aws sts assume-role --role-arn $DEPLOY_ROLE_ARN --role-session-name lambda-deploy)
      - aws configure set aws_access_key_id "$(echo $sts | jq -r '.Credentials.AccessKeyId')" --profile deploy
      - aws configure set aws_secret_access_key "$(echo $sts | jq -r '.Credentials.SecretAccessKey')" --profile deploy
      - aws configure set aws_session_token "$(echo $sts | jq -r '.Credentials.SessionToken')" --profile deploy
      - aws sts get-caller-identity --profile deploy
      - echo "getting codeartifact auth token"
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --region ap-southeast-2 --query authorizationToken --output text`
      - COMMIT_SHA=$(cat sha.txt)
      - BUILD_BRANCH=$(cat branch.txt)
      - MSG_START="\`$BUILD_VERSION\`"
      - |
        echo '
          APP=λ
          ICON=:rocket:
          CHANNEL='#test-release'
          if [ "$DATA_ENV" = "prod" ]; then
            CHANNEL='#production'
          elif [ "$DATA_ENV" = "dev" ]; then
            CHANNEL='#builds'
          fi
          curl -X POST --data-urlencode "payload={\"channel\":\"$CHANNEL\",\"username\":\"$PROJECT_NAME - $BUILD_BRANCH\",\"text\":\"$APP: $1\",\"icon_emoji\":\"$ICON\"}" $SLACK_WEBHOOK
        ' > ~/slack.sh
        chmod +x ~/slack.sh
        read -ra ARN_BITS <<<"$(echo $CODEBUILD_BUILD_ARN | sed 's/[:|\/]/ /g')"
        LINK="https://$AWS_REGION.console.aws.amazon.com/codesuite/codebuild/$${ARN_BITS[4]}/projects/$${ARN_BITS[6]}/build/$CODEBUILD_BUILD_ID/?region=$AWS_REGION"
        ~/slack.sh ":checkered_flag: $MSG_START Deploy starting. \n\t- *Env*: $ENV\n\t- *Data*: $DATA_ENV\n\t- <$LINK|*Codebuild*>" || true

      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts

      - echo "$INFRA_CONFIG" > infra.yml
      - if [ -d "common" ]; then cp -r common/ ../; fi
      - npm install --include=dev

      - RELATIVE_REPORT_PATH=$ENV/$BUILD_VERSION
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR/$RELATIVE_REPORT_PATH/
      - TEST_OUTPUT_DIRECTORY=/output/test
      - TEST_FAILED=":x:"
      - TEST_PASSED=":approved:"
      - mkdir -p $TEST_OUTPUT_DIRECTORY
  build:
    commands:
      - echo deploying $ENV
      - cd $ENV_DIR

      - |
        if [ -d "node_modules/@quotable-value/serverless" ]; then
           echo "forked serverless exist"
           rm -rf node_modules/serverless
           mv node_modules/@quotable-value/serverless node_modules/serverless
           ln -sf ../serverless/bin/serverless.js node_modules/.bin/serverless
           ln -sf ../serverless/bin/serverless.js node_modules/.bin/sls
        else
           echo "forked serverless does not exist"
        fi

      - npx serverless deploy --stage $ENV --verbose -r $AWS_REGION --aws-profile deploy
      - '~/slack.sh ":testing: $MSG_START Deploy complete, running tests " || true'
      - echo Lambda deployed, running tests...

      - ALL_TESTS_PASSED=1
      - SMOKE_PASSED=$TEST_PASSED
      - INTEGRATION_PASSED=$TEST_PASSED
      - NODE_ENV=$ENV
      - INTEGRATION_OUTPUT=""
      - SMOKE_OUTPUT=""

      - npm run test:smoke > $TEST_OUTPUT_DIRECTORY/smoke.txt || SMOKE_PASSED=$TEST_FAILED
      - '[ $SMOKE_PASSED -eq $TEST_FAILED ] && cat $TEST_OUTPUT_DIRECTORY/smoke.txt || true'

      - echo "uploading smoke test report started"
      - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
      - . ./create_test_report.sh "$PROJECT_NAME" "$ENV" "smoke" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
      - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
      - aws s3 cp mochawesome-report/ s3://$REPORT_FOLDER --recursive || true
      - echo "uploading smoke test report completed"
      - SMOKE_OUTPUT=$REPORT_VIEW_URL
      - echo "clean up smoke test results"
      - rm -rf  mochawesome-report
      - REPORT_FOLDER=""
      - |
        if [[ $ALL_TESTS_PASSED -eq 1 && ! $DATA_ENV =~ prod|preprod ]]; then
             npm run test:integration > $TEST_OUTPUT_DIRECTORY/integration.txt || INTEGRATION_PASSED=$TEST_FAILED;
             echo "uploading integration test report started"
             aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
             . ./create_test_report.sh "$PROJECT_NAME" "$ENV" "integration" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
             if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
             aws s3 cp mochawesome-report/ s3://$REPORT_FOLDER --recursive || true
             echo "uploading integration test report completed"
             INTEGRATION_OUTPUT=$REPORT_VIEW_URL
        fi;
      - '[ $INTEGRATION_PASSED -eq $TEST_FAILED ] && cat $TEST_OUTPUT_DIRECTORY/integration.txt || true'
      - |
        if [[ $SMOKE_PASSED == $TEST_FAILED || $INTEGRATION_PASSED == $TEST_FAILED ]]; then
            echo "Tests failed"
        fi
      - 'echo "Integration Test Output: $INTEGRATION_OUTPUT"'
      - 'echo "Smoke Test Output: $SMOKE_OUTPUT"'
      - '~/slack.sh ":rotating_light: $MSG_START Test Results\n\t- $SMOKE_PASSED <$SMOKE_OUTPUT|*Smoke Tests*>\n\t- $INTEGRATION_PASSED <$INTEGRATION_OUTPUT|*Integration Tests*>" || true'
      - '~/slack.sh ":white_check_mark: $MSG_START Deploy SUCCEEDED" || true'
      - cd $ENV_DIR

cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'
