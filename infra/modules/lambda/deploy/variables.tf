variable "app_name" {
  type = string
}

variable "app_env" {
  type = string
}

variable "data_env" {
  type = string
}

variable "env_vars" {
  description = "Extra env vars for deploy step"
  type = list(object({
    name  = string
    value = string
    type  = string
  }))
  default = []
}

variable "privileged_mode" {
  description = "Whether to enable running the Docker daemon inside a Docker container"
  type        = bool
  default     = false
}

variable "build_bucket_name" {
  description = "Bucket to store deploy artifacts in."
  type        = string
  default     = "qv-deployment"
}

variable "report_bucket_name" {
  description = "Bucket to store reports artifacts in."
  type        = string
  default     = "qv-deployment-reports"
}

variable "service_role_arn" {
  description = "Service role ARN."
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-access-role"
}

variable "pipeline_bucket_name" {
  description = "The bucket the pipeline uses to store artifacts between stages"
  type        = string
  default     = "codepipeline-ap-southeast-2-***********"
}

variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-southeast-2"
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type        = string
  default     = "Pacific/Auckland"
}

variable "node_runtime_version" {
  description = "Node runtime version for deploy buildspecs"
  type        = string
  default     = "20"
}

variable "preprod" {
  description = "Whether to include an approval step in the prod"
  type        = bool
  default     = false
}

variable "terraform_enabled" {
  description = "Whether to run terraform before deploy"
  type        = bool
  default     = false
}

variable "tf_state_path" {
  description = "Path to store terraform state"
  type        = string
  default     = ""
}

variable "lambda_deploy_role_arn" {
  description = "Lambda deploy role ARN"
  type        = string
  default     = ""
}

variable "use_vpc" {
  description = "Whether to run the build in a VPC"
  type        = bool
  default     = false
}

locals {
  deploy_project_name            = "${var.app_name}-deploy-${var.app_env}"
  preprod_deploy_project_name    = "${var.app_name}-deploy-${var.app_env}-preprod"
  terraform_project_name         = "${var.app_name}-terraform-${var.app_env}"
  preprod_terraform_project_name = "${var.app_name}-terraform-${var.app_env}-preprod"
}
