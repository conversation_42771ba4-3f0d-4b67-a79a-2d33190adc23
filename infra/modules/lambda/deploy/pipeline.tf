locals {
  deploy_file_key = "${var.app_name}/deploy/${var.app_env}/deploy.zip"
  deployments     = var.app_env == "prod" && var.preprod == true ? [
    {
      name          = "preprod"
      approval_step = true
    },
    {
      name          = "prod"
      approval_step = true
    }
  ] : [
    {
      name          = var.app_env
      approval_step = var.app_env == "prod"
    }
  ]
}

data "aws_iam_role" "code_pipeline_execution_role" {
  name = "CodePipelineExecutionRole"
}

resource "aws_codepipeline" "pipeline" {
  name     = "${var.app_name}-pipeline-${var.app_env}"
  role_arn = var.service_role_arn
  tags     = {}
  tags_all = {}

  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      category      = "Source"
      configuration = {
        "PollForSourceChanges" = "false"
        "S3Bucket"             = var.build_bucket_name
        "S3ObjectKey"          = local.deploy_file_key
      }
      input_artifacts  = []
      name             = "Source"
      namespace        = "SourceVariables"
      output_artifacts = [
        "SourceArtifact",
      ]
      owner     = "AWS"
      provider  = "S3"
      region    = var.region
      run_order = 1
      version   = "1"
    }
  }

  dynamic "stage" {
    for_each = local.deployments
    content {
      name = "${title(stage.value["name"])}-Deployment"

      dynamic "action" {
        for_each = var.terraform_enabled ? [1] : []
        content {
          name          = "Terraform-Plan"
          category      = "Build"
          configuration = {
            "EnvironmentVariables" = jsonencode([
              {
                name  = "TF_STATE_PATH",
                type  = "PLAINTEXT",
                value = "${stage.value["name"]}/${var.app_name}",
              },
              {
                name  = "TERRAFORM_ENABLED",
                type  = "PLAINTEXT",
                value = var.terraform_enabled
              }
            ])
            "ProjectName" = "${var.app_name}-terraform-${stage.value["name"]}"
          }
          input_artifacts  = ["SourceArtifact"]
          output_artifacts = ["DeployArtifact-Terraform-${stage.value["name"]}"]
          owner            = "AWS"
          provider         = "CodeBuild"
          region           = var.region
          run_order        = 1
          version          = "1"
          namespace        = "TerraformVariables-${stage.value["name"]}" # Unique namespace per stage
        }
      }

      dynamic "action" {
        for_each = var.terraform_enabled && var.app_env == "prod" ? [1] : []
        content {
          name          = "Apply-Terraform-Plan"
          category      = "Approval"
          owner         = "AWS"
          provider      = "Manual"
          version       = "1"
          configuration = {
            "CustomData"         = "Please review the deployment plan and approve or reject the deployment."
            "ExternalEntityLink" = "https://s3.console.aws.amazon.com/s3/object/qv-deployment?region=ap-southeast-2&bucketType=general&prefix=${var.app_name}/deploy/${stage.value["name"]}/plan_details.txt"
          }
          input_artifacts  = []
          output_artifacts = []
          run_order        = 2
        }
      }

      dynamic "action" {
        for_each = stage.value["approval_step"] ? [1] : []
        content {
          name          = "Approve-${title(stage.value["name"])}-Deployment"
          category      = "Approval"
          owner         = "AWS"
          provider      = "Manual"
          version       = "1"
          configuration = {
            "CustomData"         = "Please approve the deployment for ${stage.value["name"]}."
          }
          input_artifacts  = []
          output_artifacts = []
          run_order        = 3
        }
      }

      action {
        name          = "Deploy"
        category      = "Build"
        configuration = {
          "EnvironmentVariables" = jsonencode([
            {
              name  = "ENV",
              type  = "PLAINTEXT",
              value = stage.value["name"],
            },
            {
              name = "TEST_CONFIG",
              type = "PLAINTEXT",
              value = "/${stage.value["name"]}/${var.app_name}-test-config"
            },
            {
              name  = "DATA_ENV",
              type  = "PLAINTEXT",
              value = stage.value["name"],
            },
            {
              name  = "INFRA_CONFIG",
              type  = "PARAMETER_STORE",
              value = "/${stage.value["name"]}/${var.app_name}/infra",
            },
            {
              name  = "DEPLOY_ROLE_ARN",
              type  = "PARAMETER_STORE",
              value = var.lambda_deploy_role_arn != "" ? "/${var.lambda_deploy_role_arn}-lambda-deploy-role-arn" : "/${stage.value["name"]}-lambda-deploy-role-arn"
            },
            {
              name  = "SLACK_WEBHOOK",
              type  = "PARAMETER_STORE",
              value = "/${stage.value["name"]}/deploy/slack-webhook",
            },
            {
              name  = "S3_URL_DEPLOY_REPORT_DIR",
              type  = "PLAINTEXT",
              value = "s3://qv-deployment-reports/${var.app_name}-deploy-${stage.value["name"]}",
            },
            {
              name  = "TF_STATE_PATH",
              type  = "PLAINTEXT",
              value = "${stage.value["name"]}/${var.app_name}",
            },
            {
              name = "TERRAFORM_ENABLED"
              type = "PLAINTEXT"
              value = var.terraform_enabled
            }
          ])
          "ProjectName" = "${var.app_name}-deploy-${stage.value["name"]}"
        }
        input_artifacts = var.terraform_enabled ? ["DeployArtifact-Terraform-${stage.value["name"]}"] : ["SourceArtifact"]
        namespace        = "DeployVariables-${title(stage.value["name"])}" # Unique namespace per stage
        output_artifacts = ["DeployArtifact-Final-${title(stage.value["name"])}"]
        owner     = "AWS"
        provider  = "CodeBuild"
        region    = var.region
        run_order = 4
        version   = "1"
      }
    }
  }
}

resource "aws_cloudwatch_event_rule" "pipeline_event_rule" {
  name        = "${var.app_name}-pipeline-event-rule-${var.app_env}"
  description = "Event rule for triggering the incredible ${var.app_env} ${var.app_name} pipeline!"

  event_pattern = <<EOF
{
  "source": ["aws.s3"],
  "detail-type": ["AWS API Call via CloudTrail"],
  "detail": {
    "eventSource": ["s3.amazonaws.com"],
    "eventName": ["PutObject", "CompleteMultipartUpload", "CopyObject"],
    "requestParameters": {
      "bucketName": ["${var.build_bucket_name}"],
      "key": ["${local.deploy_file_key}"]
    }
  }
}
EOF
}

resource "aws_cloudwatch_event_target" "pipeline_event_target" {
  rule       = aws_cloudwatch_event_rule.pipeline_event_rule.name
  target_id  = "${var.app_name}-pipeline-target-${var.app_env}"
  arn        = aws_codepipeline.pipeline.arn
  role_arn   = data.aws_iam_role.code_pipeline_execution_role.arn
  depends_on = [aws_codepipeline.pipeline]
}
