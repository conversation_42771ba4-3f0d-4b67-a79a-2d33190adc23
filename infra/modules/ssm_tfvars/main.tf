locals {
  target_account_role_arns = {
    dev     = "arn:aws:iam::************:role/ci-cd-account-service-role"
    test    = "arn:aws:iam::************:role/ci-cd-account-service-role"
    uat     = "arn:aws:iam::************:role/ci-cd-account-service-role"
    preprod = "arn:aws:iam::************:role/ci-cd-account-service-role"
    prod    = "arn:aws:iam::************:role/ci-cd-account-service-role"
  }

  base_value = <<EOF
env                            = "${var.app_env}"
app_name                       = "${var.app_name}"
cost_center                    = "${var.app_env}"
target_account_role_arn        = "${lookup(local.target_account_role_arns, var.app_env)}"
EOF
}
resource "aws_ssm_parameter" "tfvars_config_parameter" {
  name        = "/${var.app_env}/${var.app_name}/tfvars"
  description = "tfvars for ${var.app_name}"
  type        = "String"
  data_type   = "text"
  value = join("\n", [local.base_value, var.additional_value])
  overwrite   = true
}