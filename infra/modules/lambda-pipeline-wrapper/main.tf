variable "project_name" {
  type = string
}

variable "repo_dir" {
  type = string
}

variable "github_repo" {
  type = string
}

variable "region" {
  type = string
}

variable "env_deploy_stages" {
  description = "Deploy config for each environment"
  type        = list(object({
    env      = string, // Dev, Test, Prod, CICD, Stage (use caps)
    env_vars = list(object({
      name  = string
      value = string
      type  = string
    }))
  }))
  default     = [
    {
      env      = "Dev"
      env_vars = []
    },
    {
      env      = "Test"
      env_vars = []
    },
    {
      env      = "Prod"
      env_vars = []
    }
  ]
}

variable "node_runtime_version" {
  description = "Node runtime version for deploy buildspecs"
  type        = string
  default     = "16"
}

locals {
  deploy_bucket          = "qv-deployment"
  report_bucket          = "qv-deployment-reports"
  pipeline_name          = "${var.project_name}-pipeline"
  buildspec_path         = var.repo_dir != "" ? "${var.repo_dir}/buildspec.yml" : "buildspec.yml"
  source_file_path_regex = var.repo_dir != "" ? "${var.repo_dir}/.*" : ".*"
}

module "lambda-pipeline" {
  source                    = "../lambda-pipeline"
  project_name              = var.project_name
  github_repo               = var.github_repo
  buildspec_path            = local.buildspec_path
  source_file_path_regex    = local.source_file_path_regex
  build_bucket_name         = local.deploy_bucket
  deploy_artifact_base_path = var.project_name
  deploy_artifact_key       = "${var.project_name}/deploy.zip"
  report_bucket_name        = local.report_bucket
  report_bucket_base_path   = var.project_name
  log_group_name            = local.pipeline_name
  pipeline_name             = local.pipeline_name
  region                    = var.region
  service_role_arn          = "arn:aws:iam::************:role/ci-cd-account-access-role"
  pipeline_bucket_name      = "codepipeline-ap-southeast-2-***********"
  env_deploy_stages         = var.env_deploy_stages
  node_runtime_version      = var.node_runtime_version
}
