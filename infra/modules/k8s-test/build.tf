locals {
  build_project_name = "${var.service_name}-tests"
}

resource "aws_codebuild_project" "build" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 3
  name                   = local.build_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    encryption_disabled    = true
    location               = var.report_bucket_name
    name                   = var.service_name
    namespace_type         = "NONE"
    override_artifact_name = false
    packaging              = "NONE"
    path                   = "/"
    type                   = "S3"
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }


  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:7.0"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "REPORT_BUCKET_NAME"
      value = var.report_bucket_name
      type  = "PLAINTEXT"
    }
    environment_variable {
      name  = "PROJECT_NAME"
      value = var.service_name
      type  = "PLAINTEXT"
    }


    environment_variable {
      name  = "ENV"
      value = "PLACEHOLDER"
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "DEPLOYMENT_ID"
      value = "PLACEHOLDER"
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "TEST_TYPES"
      value = join("_", var.test_types)
    }
    environment_variable {
      name  = "TEST_CONFIG"
      value = "PLACEHOLDER"
      type  = "PLAINTEXT"
    }

  }

  logs_config {
    cloudwatch_logs {
      group_name  = var.service_name
      status      = "ENABLED"
      stream_name = var.service_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec           = file("${path.module}/buildspec.yml")
    git_clone_depth     = 0
    insecure_ssl        = false
    location            = local.full_github_url
    report_build_status = true
    type                = "GITHUB"

    git_submodules_config {
      fetch_submodules = false
    }
  }
  vpc_config {
    security_group_ids = ["sg-0b5a83ef94043e0d1"]
    subnets            = ["subnet-0dd620c23992d88ae"]
    vpc_id             = "vpc-06474f6d3a210028b"
  }
}

resource "aws_codebuild_webhook" "build" {
  depends_on   = [aws_codebuild_project.build]
  build_type   = "BUILD"
  project_name = local.build_project_name

  filter_group {
    filter {
      exclude_matched_pattern = false
      pattern                 = "PUSH, PULL_REQUEST_CREATED, PULL_REQUEST_MERGED"
      type                    = "EVENT"
    }
    filter {
      exclude_matched_pattern = false
      pattern                 = var.source_file_path_regex
      type                    = "FILE_PATH"
    }
    filter {
      exclude_matched_pattern = true
      pattern                 = ".*\\bWIP\\b.*"
      type                    = "COMMIT_MESSAGE"
    }
  }
}