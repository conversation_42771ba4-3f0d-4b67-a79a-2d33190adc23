variable "service_name" {
  type = string
  default = ""
}

variable "github_repo_name" {
  type    = string
  default = ""
}

locals {
  github_repo_name = var.github_repo_name != "" ? var.github_repo_name : var.service_name
  full_github_url = "https://github.com/Quotable-Value/${local.github_repo_name}"
}
variable "source_file_path_regex" {
  description = "Builds will only trigger if newer commits have changed a file name/path that matches this regex."
  type        = string
  default     = ".*"
}



variable "test_types" {
  type    = list(string)
  default = ["integration"]
}


variable "build_bucket_name" {
  description = "Bucket to store deploy artifacts in."
  type        = string
  default     = "qv-deployment"
}

variable "report_bucket_name" {
  description = "Bucket to store reports artifacts in."
  type        = string
  default     = "qv-deployment-reports"
}

variable "service_role_arn" {
  description = "Service role ARN."
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-access-role"
}
