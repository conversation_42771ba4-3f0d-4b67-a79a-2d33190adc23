version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - COMMIT_MESSAGE
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - mkdir -p ~/.ssh
      - echo "$build_ssh_key" > ~/.ssh/id_rsa
      - chmod 600 ~/.ssh/id_rsa
      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
      - TEST_FAILED=":x:"
      - TEST_PASSED=":approved:"
  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
      - TEST_CONFIG_PATH=test/config.$ENV.json
      - |
        if [ -f test/package.json ]; then
           TEST_CONFIG_FROM_SSM=$(aws ssm get-parameters --names $TEST_CONFIG --with-decryption --query Parameters[0].Value --output text)
           echo "TEST_CONFIG_FROM_SSM: $TEST_CONFIG_FROM_SSM"
           echo "$TEST_CONFIG_FROM_SSM" > $TEST_CONFIG_PATH
           echo "check test config content"
           cat test/config.$ENV.json
        else
           echo "'no test/package.json' found, no need to write test config"
        fi

  build:
    on-failure: CONTINUE
    commands:
      - S3_URL_DEPLOY_REPORT_DIR=s3://$REPORT_BUCKET_NAME/$ENV
      - RELATIVE_REPORT_PATH=/$PROJECT_NAME/$DEPLOYMENT_ID
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR$RELATIVE_REPORT_PATH
      - TEST_OUTPUT_DIRECTORY=/output/test
      - AWESOME_REPORTER_OUTPUT=mochawesome-report
      - mkdir -p $TEST_OUTPUT_DIRECTORY
      - |
        if [ -f test/package.json ]; then
          echo "'test/package.json' found, so running tests."
          cd test
          npm install
          ls -al
          NODE_ENV=$ENV ./node_modules/.bin/mocha **/*.test.js --timeout 60000  --reporter mochawesome
          mkdir $TEST_OUTPUT_DIRECTORY/integration -p
          if [ -d "$AWESOME_REPORTER_OUTPUT" ]; then
            echo "$AWESOME_REPORTER_OUTPUT exists."
            ls $AWESOME_REPORTER_OUTPUT
            mv $AWESOME_REPORTER_OUTPUT/*  $TEST_OUTPUT_DIRECTORY/integration/
            rm -rf $AWESOME_REPORTER_OUTPUT
          else
            echo "$AWESOME_REPORTER_OUTPUT does not exist."
          fi
        else
           echo "'no test/package.json' found, so no tests to run."
        fi
      - cd $TEST_OUTPUT_DIRECTORY
      - ls -al
      - echo "uploading test output to $TEST_REPORT_UPLOAD_PATH"
      - aws s3 cp ./ $TEST_REPORT_UPLOAD_PATH --recursive || true
  post_build:
    commands:
      - echo Build $BUILD_VERSION completed on `date`
