<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_codebuild_project.codebuild_project](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codebuild_project) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_path"></a> [artifact\_path](#input\_artifact\_path) | S3 bucket path for build output. | `string` | n/a | yes |
| <a name="input_cache_path"></a> [cache\_path](#input\_cache\_path) | S3 Bucket path for build cache. | `string` | n/a | yes |
| <a name="input_github_repo"></a> [github\_repo](#input\_github\_repo) | Github repo link. | `string` | n/a | yes |
| <a name="input_s3_bucket"></a> [s3\_bucket](#input\_s3\_bucket) | Deployment S3 bucket. | `string` | n/a | yes |
| <a name="input_service_name"></a> [service\_name](#input\_service\_name) | Name of the related service. | `string` | n/a | yes |
| <a name="input_service_role"></a> [service\_role](#input\_service\_role) | ARN of the codebuild service role. | `string` | n/a | yes |
| <a name="input_source_version"></a> [source\_version](#input\_source\_version) | Branch to trigger builds. | `string` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->