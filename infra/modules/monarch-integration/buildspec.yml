version: 0.2
env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH
phases:
  install:
    runtime-versions:
      nodejs: 12
    commands:
      - cd test
      - npm install
      - npm install mochawesome
      - NODE_ENV=dev
      - mkdir /reports/integration/
  build:
    commands:
      - npx mocha **/*.test.js --timeout 10000 --colors --reporter mochawesome
      - cp ./mochawesome-reports/. /reports/integration/
artifacts:
    secondary-artifacts:
        reports:
            files:
                - '**/*'
            name: reports
            base-directory: /reports