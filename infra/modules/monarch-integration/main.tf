resource "aws_codebuild_project" "codebuild_project" {
  name          = "${var.service_name}-integration"
  description   = "${var.service_name}-integration"
  build_timeout = "30"

  service_role = var.service_role

  artifacts {
    type = "NO_ARTIFACTS"
  }

  cache {
    type     = "S3"
    location = "${var.artifact_bucket}/${var.cache_path}"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:4.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
  }

  source {
    type            = "GITHUB"
    location        = var.github_repo
    git_clone_depth = 0
    buildspec       = file("${path.module}/buildspec.yml")
  }

  source_version = var.source_version
}
