variable "app_name" {
  type = string
}

variable "bucket_path_override" {
  type = string
  default = ""
}

variable "github_repo" {
  description = "URL to clone the repo from"
  type        = string
}

variable "buildspec_path" {
  description = "Path to the buildspec file in the project."
  type        = string
  default     = "test/cypress-buildspec.yml"
}

variable "report_bucket_name" {
  description = "Bucket to store reports artifacts in."
  type        = string
  default     = "qv-deployment-reports"
}

variable "service_role_arn" {
  description = "Service role ARN."
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-access-role"
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type        = string
  default     = "Pacific/Auckland"
}

variable "privileged_mode" {
    description = "Enable privileged mode for the build container."
    type        = bool
    default     = true
}
