locals {
  build_project_name = "${var.app_name}-background-tests"
}

resource "aws_codebuild_project" "build" {
  badge_enabled          = false
  build_timeout          = 480
  concurrent_build_limit = 6
  name                   = local.build_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  build_batch_config {
    service_role = var.service_role_arn
    combine_artifacts = true
    timeout_in_mins = 480

    restrictions {
      compute_types_allowed = ["BUILD_GENERAL1_MEDIUM"]
      maximum_builds_allowed = 8
    }
  }

  artifacts {
    type                   = "NO_ARTIFACTS"
    encryption_disabled    = false
    override_artifact_name = false
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_MEDIUM"
    image                       = "aws/codebuild/standard:7.0"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = var.privileged_mode
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = var.app_name
      status      = "ENABLED"
      stream_name = var.app_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec           = var.buildspec_path
    git_clone_depth     = 0
    insecure_ssl        = false
    location            = var.github_repo
    report_build_status = true
    type                = "GITHUB"

    git_submodules_config {
      fetch_submodules = false
    }
  }
}

#module "codebuild-notification" {
#  source        = "../codebuild-sns"
#  codebuild_arn = aws_codebuild_project.build.arn
#  project_name  = var.app_name
#}
