locals {
  region = "ap-southeast-2"
}

data "aws_iam_role" "ecs-execution-role" {
  name = "ecsTaskExecutionRole"
}
data "aws_iam_role" "ecs-task-role" {
  name = "dynamic_environments_ecs_task_role"
}

data "aws_ecs_cluster" "cicd-cluster" {
  cluster_name = "dynamic-environments"
}

resource "aws_cloudwatch_log_group" "cmd_task_logs_group" {
  name = "/aws/ecs/task/${var.cmd_app_name}"
}

resource "aws_ecs_task_definition" "task" {
  family                   = "${var.cmd_app_name}-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.cpu
  memory                   = var.memory

  execution_role_arn    = data.aws_iam_role.ecs-execution-role.arn
  task_role_arn         = data.aws_iam_role.ecs-task-role.arn
  container_definitions = jsonencode([
    {
      name             = "${var.cmd_app_name}-task"
      image            = "948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/cicd/dynamic-environments-cmd:${var.image_tag}"
      command          = ["node", "/index.js", "--name", var.cmd_app_name, "--env", "placeholder"]
      logConfiguration = {
        logDriver = "awslogs"
        options   = {
          "awslogs-region" = local.region
          "awslogs-group"  = aws_cloudwatch_log_group.cmd_task_logs_group.name
          "awslogs-stream-prefix" : "${var.cmd_app_name}-task"
        }
      }
    }
  ])
  depends_on = [
    data.aws_ecs_cluster.cicd-cluster,
  ]
}