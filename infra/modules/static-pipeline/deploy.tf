locals {
  deploy_project_name = "${var.project_name}-deploy"
}

resource "aws_codebuild_project" "deploy" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 1
  name                   = local.deploy_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    encryption_disabled    = false
    name                   = var.project_name
    override_artifact_name = false
    packaging              = "NONE"
    type                   = "CODEPIPELINE"
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:6.0"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = false
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.project_name
      type  = "PLAINTEXT"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = var.log_group_name
      status      = "ENABLED"
      stream_name = var.log_group_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec           = templatefile("${path.module}/deploy-buildspec.yml", {
      node_runtime_version = var.node_runtime_version
    })
    insecure_ssl        = false
    report_build_status = false
    type                = "CODEPIPELINE"
  }
}
