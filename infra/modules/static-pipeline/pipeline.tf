resource "aws_codepipeline" "pipeline" {
  name     = var.pipeline_name
  role_arn = var.service_role_arn
  tags     = {}
  tags_all = {}

  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      category         = "Source"
      configuration    = {
        "PollForSourceChanges" = "true"
        "S3Bucket"             = var.build_bucket_name
        "S3ObjectKey"          = var.build_artifact_key
      }
      input_artifacts  = []
      name             = "Source"
      namespace        = "SourceVariables"
      output_artifacts = [
        "SourceArtifact",
      ]
      owner            = "AWS"
      provider         = "S3"
      region           = var.region
      run_order        = 1
      version          = "1"
    }
  }
  dynamic stage {
    for_each = var.env_deploy_stages
    content {
      name = "Deploy-${stage.value.env}"
      dynamic action {
        for_each = lower(stage.value.env) == "dev" || length(var.env_deploy_stages) == 1 ? [] : [1]
        content {
          name             = "Approve-${stage.value.env}"
          category         = "Approval"
          configuration    = {
#           NotificationArn = "arn:aws:sns:us-east-2:80398EXAMPLE:MyApprovalTopic",
            ExternalEntityLink = "https://ap-southeast-2.console.aws.amazon.com/s3/buckets/qv-deployment-reports/${var.project_name}/${lower(stage.value.env) == "prod" ? "test" : "dev"}/#{DevDeployVariables.BUILD_VERSION}/",
            CustomData = "Branch #{DevDeployVariables.BUILD_BRANCH}, version #{DevDeployVariables.BUILD_VERSION}"
          }
          input_artifacts  = []
          output_artifacts = []
          owner            = "AWS"
          provider         = "Manual"
          region           = var.region
          run_order        = 1
          version          = "1"
        }
      }
      action {
        name             = "Deploy-${stage.value.env}"
        category         = "Build"
        configuration    = {
          "EnvironmentVariables" = jsonencode(concat([
            {
              name  = "ENV"
              value = lower(stage.value.env)
              type  = "PLAINTEXT"
            },
            {
              name  = "S3_URL_BUILD_ARTIFACT_DIR",
              value = "s3://${var.build_bucket_name}/${var.build_artifact_base_path}",
              type  = "PLAINTEXT"
            },
            {
              name  = "S3_URL_DEPLOY_REPORT_DIR",
              value = "s3://${var.report_bucket_name}/${var.report_bucket_base_path}",
              type  = "PLAINTEXT"
            },
            {
              name  = "S3_URL_DEPLOY_TARGET_DIR",
              value = "s3://${var.deploy_bucket_name}",
              type  = "PLAINTEXT"
            },
            {
              name  = "SLACK_WEBHOOK"
              value = "/${lower(stage.value.env)}/deploy/slack-webhook"
              type  = "PARAMETER_STORE"
            },
          ], stage.value.env_vars))
          "ProjectName"          = local.deploy_project_name
        }
        input_artifacts  = [
          "SourceArtifact",
        ]
        namespace        = "${stage.value.env}DeployVariables"
        output_artifacts = [
          "${stage.value.env}DeployArtifact",
        ]
        owner            = "AWS"
        provider         = "CodeBuild"
        region           = var.region
        run_order        = lower(stage.value.env) == "dev" || length(var.env_deploy_stages) == 1 ? 1 : 2
        version          = "1"
      }
    }
  }
}