variable "repo_dir" {
  type = string
}

variable "project_name" {
  type = string
}

variable "deploy_bucket_name" {
  description = "Target bucket for deploy artifacts."
  type = string
}

variable "deploy_artifact_base_path" {
  description = "Deploy directory for this project."
  type = string
}

variable "build_bucket_name" {
  description = "Target bucket for deploy artifacts."
  type = string
}

variable "build_artifact_base_path" {
  description = "Artifact directory for this project."
  type = string
}

variable "build_artifact_key" {
  description = "Build artifact key, which is watched and triggers the build pipeline when it changes"
  type = string
}

variable "report_bucket_name" {
  description = "Bucket to store reports artifacts in."
  type = string
}

variable "report_bucket_base_path" {
  description = "Report bucket directory for this project."
  type = string
}

variable "buildspec_path" {
  description = "Path to the buildspec file in the project."
  type = string
  default = "buildspec.yml"
}

variable "github_repo" {
  description = "URL to clone the repo from"
  type = string
}

variable "source_file_path_regex" {
  description = "Builds will only trigger if newer commits have changed a file name/path that matches this regex."
  type = string
  default = ".*"
}

variable "log_group_name" {
  description = "Cloudwatch log group name"
  type = string
}

variable "pipeline_name" {
  description = "pipeline name."
  type = string
}

variable "service_role_arn" {
  description = "Service role ARN."
  type = string
}

variable "pipeline_bucket_name" {
  description = "The bucket the pipeline uses to store artifacts between stages"
  type = string
}

variable "region" {
  description = "AWS region"
  type = string
  default = "ap-southeast-2"
}

variable "env_deploy_stages" {
  description = "Deploy config for each environment"
  type = list(object({
    env = string, // Dev, Test, Prod, CICD, Stage (use caps)
    env_vars = list(object({
      name = string
      value = string
      type = string
    }))
  }))
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type = string
  default = "Pacific/Auckland"
}

variable "node_runtime_version" {
  description = "Node runtime version for deploy buildspecs"
  type = string
  default = "12"
}