version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - cd $ENV
      - BUILD_VERSION=$(cat version.txt)
      - ENV_DIR=$(pwd)
      - mkdir ../deploy_pipeline_tests
      - cd ../deploy_pipeline_tests
      - unzip ../deploy_pipeline_tests.zip
      - TEST_DIR=$(pwd)
      - COMMIT_SHA=$(cat sha.txt)
      - BUILD_BRANCH=$(cat branch.txt)
      - npm install --include=dev
      - ROLLBACK_PACKAGE_DIR=/rollback
      - RELATIVE_REPORT_PATH=$ENV/$BUILD_VERSION
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR/$RELATIVE_REPORT_PATH/
      - TEST_OUTPUT_DIRECTORY=/output/test
      - mkdir -p $TEST_OUTPUT_DIRECTORY
      - mkdir -p $ROLLBACK_PACKAGE_DIR
  build:
    commands:
      - cd $ENV_DIR
      - echo deploying $ENV
      - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy starting: $PROJECT_NAME $BUILD_VERSION $BUILD_BRANCH\"}" $SLACK_WEBHOOK || true;'
      - aws s3 cp . $S3_URL_DEPLOY_TARGET_DIR/ --recursive
      - cd $TEST_DIR
      - TESTS_PASSED=1
      - npm run test:smoke > $TEST_OUTPUT_DIRECTORY/smoke.txt || TESTS_PASSED=0
      - if [[ $ENV =~ dev|test ]]; then npm run test:integration > $TEST_OUTPUT_DIRECTORY/integration.txt || TESTS_PASSED=0; fi;
      - echo uploading test output to $TEST_REPORT_UPLOAD_PATH
      - cd $TEST_OUTPUT_DIRECTORY
      - aws s3 cp ./ $TEST_REPORT_UPLOAD_PATH --recursive || true
      - cd $ENV_DIR
      - 'if (( $TESTS_PASSED == 0 )); then
          curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy FAILED: $PROJECT_NAME $BUILD_VERSION $BUILD_BRANCH\"}" $SLACK_WEBHOOK || true;
          echo "Rolling back";
          mkdir rollback;
          cd rollback;
          aws s3 cp $S3_URL_BUILD_ARTIFACT_DIR/$ENV.zip .;
          unzip $ENV.zip;
          cp -r ./* $ROLLBACK_PACKAGE_DIR;
          aws s3 cp $ROLLBACK_PACKAGE_DIR/* $S3_URL_DEPLOY_TARGET_DIR/ --recursive;
          echo "Integration tests failed, rollback complete, failing pipeline..." && false;
        fi;'
      - echo 'Backing up deploy artifact'
      - cd $ENV_DIR
      - zip -r ../$ENV.zip .
      - aws s3 cp ../$ENV.zip $S3_URL_BUILD_ARTIFACT_DIR/
      - 'curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy SUCCEEDED: $PROJECT_NAME $BUILD_VERSION $BUILD_BRANCH\"}" $SLACK_WEBHOOK || true;'

# cache:
#     paths:
#         - '/usr/local/lib/node_modules/**/*'