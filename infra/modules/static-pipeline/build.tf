locals {
  build_project_name = "${var.project_name}-build"
}

resource "aws_codebuild_project" "build" {
  badge_enabled          = false
  build_timeout          = 60
  concurrent_build_limit = 3
  name                   = local.build_project_name
  queued_timeout         = 480
  service_role           = var.service_role_arn
  tags                   = {}
  tags_all               = {}

  artifacts {
    encryption_disabled    = true
    location               = var.report_bucket_name
    name                   = var.report_bucket_base_path
    namespace_type         = "NONE"
    override_artifact_name = false
    packaging              = "NONE"
    path                   = "/"
    type                   = "S3"
  }

  cache {
    modes = []
    type  = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:6.0"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = false
    type                        = "LINUX_CONTAINER"

    environment_variable {
      name  = "TZ"
      value = var.timezone
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.project_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "BUILD_BASE_S3_URL"
      value = "s3://${var.build_bucket_name}/${var.project_name}"
      type  = "PLAINTEXT"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = var.project_name
      status      = "ENABLED"
      stream_name = var.project_name
    }

    s3_logs {
      encryption_disabled = false
      status              = "DISABLED"
    }
  }

  source {
    buildspec           = var.buildspec_path
    git_clone_depth     = 0
    insecure_ssl        = false
    location            = var.github_repo
    report_build_status = true
    type                = "GITHUB"

    git_submodules_config {
      fetch_submodules = false
    }
  }
}

resource "aws_codebuild_webhook" "build" {
  depends_on   = [aws_codebuild_project.build]
  build_type   = "BUILD"
  project_name = local.build_project_name

  filter_group {
    filter {
      exclude_matched_pattern = false
      pattern                 = "PUSH, PULL_REQUEST_CREATED, PULL_REQUEST_MERGED"
      type                    = "EVENT"
    }
    filter {
      exclude_matched_pattern = false
      pattern                 = var.source_file_path_regex
      type                    = "FILE_PATH"
    }
    filter {
      exclude_matched_pattern = true
      pattern                 = ".*\\bWIP\\b.*"
      type                    = "COMMIT_MESSAGE"
    }
  }
}

module "codebuild-notification" {
  source        = "../codebuild-sns"
  codebuild_arn = aws_codebuild_project.build.arn
  project_name  = var.project_name
}
