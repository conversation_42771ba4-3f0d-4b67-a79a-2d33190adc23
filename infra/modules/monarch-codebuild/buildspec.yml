version: 0.2
env:
    shell: bash
    exported-variables:
        - COMMIT_EMAIL
        - COMMIT_SHA
        - SLACK_USER
        - BUILD_VERSION
        - BUILD_BRANCH
        - TAGGED_BUILD
        - RELATIVE_REPORT_PATH
        - OUTPUT_PATH
phases:
    install:
        runtime-versions:
            java: corretto8
    pre_build:
        commands:
            - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
            - '[ -n "$COMMIT_SHA" ] || (echo "COMMIT_SHA IS NOT SET, EXITING..." && false)'
            - echo Logging in to Amazon ECR...
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 373100629421.dkr.ecr.$AWS_REGION.amazonaws.com
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 948396734470.dkr.ecr.$AWS_REGION.amazonaws.com
            - |
                echo Pulling base image from ECR...
                docker pull 948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/monarch/openjdk:8-jre-alpine
                docker tag 948396734470.dkr.ecr.ap-southeast-2.amazonaws.com/monarch/openjdk:8-jre-alpine openjdk:8-jre-alpine
            - BUILD_VERSION=$(git describe --abbrev=7 --tags)
            - |
                if [ -n "$(git describe --exact-match --tags 2>/dev/null)" ]; then
                  echo "Build is tagged"
                  TAGGED_BUILD=true
                  OUTPUT_PATH=v$BUILD_VERSION
                else
                  echo "Build is not tagged"
                  TAGGED_BUILD=false
                  OUTPUT_PATH=$CLEAN_BRANCH/${COMMIT_SHA:0:-33}
                fi
            - |
                ARTIFACT_PATH=/artifacts/$RELATIVE_REPORT_PATH
                ARTIFACT_NAME=$SERVICE_NAME-$BUILD_VERSION
                METADATA=$(printf '{"codepipeline-artifact-revision-summary":"%s","commit-sha":"%s","branch":"%s"}' "$SERVICE_NAME $BUILD_VERSION" "$COMMIT_SHA" "$BUILD_BRANCH")
                if [[ $BUILD_VERSION = *"-"* ]]; then
                  BUILD_VERSION=$BUILD_VERSION"-SNAPSHOT"
                fi

            - export CODEARTIFACT_TOKEN=$(aws codeartifact get-authorization-token --region ap-southeast-2 --domain quotable-value --domain-owner 948396734470 --query authorizationToken --output text)
            - sbt jacoco
            - echo "uploading jacoco coverage report started"
            - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
            - . ./create_test_report.sh "$PROJECT_NAME" "build" "coverage" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
            - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
            - cp $PROJECT_NAME-impl/target/scala-2.12/jacoco/report/html/index.html $PROJECT_NAME-impl/target/scala-2.12/jacoco/report/html/stats.txt
            - aws s3 cp $PROJECT_NAME-impl/target/scala-2.12/jacoco/report/html s3://$REPORT_FOLDER --recursive || true
            - echo "uploading jacoco coverage report completed"

            - |
                echo $METADATA > $REPORT_OUTPUT/metadata.json
                echo $BUILD_VERSION > $REPORT_OUTPUT/version.txt
                echo $COMMIT_SHA > $REPORT_OUTPUT/sha.txt
                echo $COMMIT_EMAIL > $REPORT_OUTPUT/author.txt
                cp -r ./$SERVICE_NAME-impl/target/scala-2.12/jacoco/report $REPORT_OUTPUT
            - mkdir -p $ARTIFACT_PATH
    build:
        commands:
            - echo Build started
            - sbt -DbuildTarget=kubernetes docker:publishLocal
    post_build:
        commands:
            - echo Build completed on `date`
            - echo Build version $BUILD_VERSION
            - echo Pushing images to ECR
            - docker image push 373100629421.dkr.ecr.ap-southeast-2.amazonaws.com/monarch/$SERVICE_NAME-impl:$BUILD_VERSION
            - echo Creating Deployment Configuration
            - cd $SERVICE_NAME-impl/kubernetes-configuration
            - |
                for file in *.json; do
                  sed -i "s/{version}/${BUILD_VERSION}/g" $file
                done
            - zip -r ./$ARTIFACT_NAME.zip *
            - mv ./$ARTIFACT_NAME.zip $ARTIFACT_PATH
            - |
                if [ -d "../../infra" ]; then
                  echo "infra directory exists"
                  mv ../../infra $ARTIFACT_PATH
                fi
            - cd $ARTIFACT_PATH
            - zip -r ./deploy.zip *
            - echo "Uploading deploy.zip to tagged build bucket"
            - aws s3 cp $ARTIFACT_PATH/deploy.zip $BUILD_BASE_S3_URL/$OUTPUT_PATH/deploy.zip --metadata="$METADATA"
        finally:
            # Clean up lock dependency lock files
            - rm -fv /root/.ivy2/.sbt.ivy.lock
            - find /root/.ivy2/cache -name "ivydata-*.properties" -delete
            - find /root/.sbt -name "*.lock" -delete
artifacts:
    secondary-artifacts:
        reports:
            files:
                - '**/*'
            name: reports
            base-directory: /reports
cache:
    paths:
        - '/root/.cache/coursier/**/*'
        - '/root/.ivy2/cache/**/*'
        - '/root/.sbt/**/*'
