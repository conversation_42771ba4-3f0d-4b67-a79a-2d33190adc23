variable "service_name" {
  type        = string
  description = "Name of the related service."
}

variable "service_role" {
  type        = string
  description = "ARN of the codebuild service role."
}

variable "artifact_bucket" {
  type        = string
  description = "Deployment S3 bucket."
}

variable "artifact_path" {
  type        = string
  description = "S3 bucket path for build output."
}

variable "report_bucket" {
  type        = string
  description = "Reporting S3 bucket."
}

variable "report_path" {
  type        = string
  description = "S3 bucket path for report output."
}

variable "build_bucket_name" {
  description = "Bucket to store build artifacts in."
  type        = string
  default     = "qv-deployment"
}

variable "build_bucket_path" {
  description = "Path to store build artifacts in."
  type        = string
}

variable "cache_path" {
  type        = string
  description = "S3 Bucket path for build cache."
}

variable "source_version" {
  type        = string
  description = "Branch to trigger builds."
}

variable "github_repo" {
  type        = string
  description = "Github repo link."
}

variable "custom_buildspec" {
  type        = string
  description = "Custom buildspec if used"
  default     = ""
}

variable "timezone" {
  description = "Timezone for buildspecs"
  type = string
  default = "Pacific/Auckland"
}

