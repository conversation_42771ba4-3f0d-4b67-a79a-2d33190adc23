<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_codebuild_project.codebuild_project](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codebuild_project) | resource |
| [aws_codebuild_webhook.webhook](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codebuild_webhook) | resource |
| [aws_codestarnotifications_notification_rule.notify](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codestarnotifications_notification_rule) | resource |
| [aws_sns_topic.cicd_notification](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/sns_topic) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_artifact_bucket"></a> [artifact\_bucket](#input\_artifact\_bucket) | Deployment S3 bucket. | `string` | n/a | yes |
| <a name="input_artifact_path"></a> [artifact\_path](#input\_artifact\_path) | S3 bucket path for build output. | `string` | n/a | yes |
| <a name="input_cache_path"></a> [cache\_path](#input\_cache\_path) | S3 Bucket path for build cache. | `string` | n/a | yes |
| <a name="input_github_repo"></a> [github\_repo](#input\_github\_repo) | Github repo link. | `string` | n/a | yes |
| <a name="input_report_bucket"></a> [report\_bucket](#input\_report\_bucket) | Reporting S3 bucket. | `string` | n/a | yes |
| <a name="input_report_path"></a> [report\_path](#input\_report\_path) | S3 bucket path for report output. | `string` | n/a | yes |
| <a name="input_service_name"></a> [service\_name](#input\_service\_name) | Name of the related service. | `string` | n/a | yes |
| <a name="input_service_role"></a> [service\_role](#input\_service\_role) | ARN of the codebuild service role. | `string` | n/a | yes |
| <a name="input_source_version"></a> [source\_version](#input\_source\_version) | Branch to trigger builds. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_codebuild_project_name"></a> [codebuild\_project\_name](#output\_codebuild\_project\_name) | n/a |
<!-- END_TF_DOCS -->