locals {
  build_base_s3_url = "s3://${var.build_bucket_name}/${var.build_bucket_path}/build"
}

resource "aws_codebuild_project" "codebuild_project" {
  name          = "${(startswith(var.service_name, "monarch-") ? "" : "monarch-")}${var.service_name}-build"
  description   = "${(startswith(var.service_name, "monarch-") ? "" : "monarch-")}${var.service_name}-build"
  build_timeout = "60"

  service_role = var.service_role

  artifacts {
    type = "NO_ARTIFACTS"
  }

  secondary_artifacts {
    artifact_identifier    = "reports"
    encryption_disabled    = true
    location               = var.report_bucket
    name                   = var.report_path
    namespace_type         = "NONE"
    override_artifact_name = false
    packaging              = "NONE"
    path                   = "/"
    type                   = "S3"
  }

  cache {
    type     = "S3"
    location = "${var.artifact_bucket}/${var.cache_path}"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:4.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "SERVICE_NAME"
      value = var.service_name
    }

    environment_variable {
      name  = "PROJECT_NAME"
      value = var.service_name
    }

    environment_variable {
      name  = "TZ"
      value = var.timezone
    }

    environment_variable {
      name  = "BUILD_BASE_S3_URL"
      value = local.build_base_s3_url
      type  = "PLAINTEXT"
    }
  }

  source {
    type                = "GITHUB"
    location            = var.github_repo
    report_build_status = true
    git_clone_depth     = 0
    buildspec           = var.custom_buildspec != "" ? var.custom_buildspec : file("${path.module}/buildspec.yml")
  }

  source_version = var.source_version
}

resource "aws_codebuild_webhook" "webhook" {
  project_name = aws_codebuild_project.codebuild_project.name
  build_type   = "BUILD"

  filter_group {
    filter {
      type    = "EVENT"
      pattern = "PULL_REQUEST_CREATED,PULL_REQUEST_MERGED,PUSH"
    }
  }
}

data "aws_sns_topic" "cicd_notification" {
  name = "build-notification"
}

# aws_codestarnotifications_notification_rule.notify:
resource "aws_codestarnotifications_notification_rule" "notify" {
  detail_type = "FULL"
  event_type_ids = [
    "codebuild-project-build-state-failed",
    "codebuild-project-build-state-succeeded",
  ]
  name     = "${var.service_name}-author-notification"
  resource = aws_codebuild_project.codebuild_project.arn
  status   = "ENABLED"

  target {
    address = data.aws_sns_topic.cicd_notification.arn
    type    = "SNS"
  }
}
