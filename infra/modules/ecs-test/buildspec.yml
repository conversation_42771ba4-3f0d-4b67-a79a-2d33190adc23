version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - COMMIT_MESSAGE
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH
phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - mkdir -p ~/.ssh
      - TEST_FAILED=":x:"
      - TEST_PASSED=":approved:"
  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
      - TEST_CONFIG_PATH=test/$PROJECT_NAME/config.$ENV.json
      - echo $TEST_CONFIG_PATH
      - echo $TEST_CONFIG
      - echo "$TEST_CONFIG" > TEST_CONFIG_PATH

  build:
    on-failure: CONTINUE
    commands:
      - S3_URL_DEPLOY_REPORT_DIR=s3://$REPORT_BUCKET_NAME/$ENV
      - RELATIVE_REPORT_PATH=/$PROJECT_NAME/$BUILD_VERSION
      - TEST_REPORT_UPLOAD_PATH=$S3_URL_DEPLOY_REPORT_DIR$RELATIVE_REPORT_PATH
      - TEST_OUTPUT_DIRECTORY=/output/test
      - mkdir -p $TEST_OUTPUT_DIRECTORY

      - echo 'run tests'
      - cd $TEST_OUTPUT_DIRECTORY
      - ls -al
      - echo "uploading test output to $TEST_REPORT_UPLOAD_PATH"
      - aws s3 cp ./ $TEST_REPORT_UPLOAD_PATH --recursive || true


  post_build:
    commands:
      - echo Build $BUILD_VERSION completed on `date`
