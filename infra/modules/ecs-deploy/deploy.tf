resource "aws_codebuild_project" "deploy" {
  name                   = local.deploy_project_name
  build_timeout          = 10
  concurrent_build_limit = 1
  queued_timeout         = 30
  service_role           = var.service_role_arn

  source {
    type = "CODEPIPELINE"
    buildspec = templatefile(coalesce(var.buildspec_path, "${path.module}/deploy-buildspec.yml"), {
      node_runtime_version = var.node_runtime_version
    })
    insecure_ssl        = false
    report_build_status = false
  }
  dynamic "vpc_config" {
    for_each = var.use_vpc ? [1] : []
    content {
      vpc_id             = var.vpc_id
      subnets            = var.vpc_subnets
      security_group_ids = var.vpc_security_group_ids
    }
  }

  artifacts {
    name = var.app_name
    type = "CODEPIPELINE"
  }

  cache {
    type = "NO_CACHE"
  }

  logs_config {
    cloudwatch_logs {
      status      = "ENABLED"
      group_name  = local.deploy_project_name
      stream_name = local.deploy_project_name
    }
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:7.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "APP_NAME"
      value = var.app_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "ECR_REPO_NAME"
      value = var.ecr_repo_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "ACCOUNT_ID"
      value = var.account_id
      type  = "PLAINTEXT"
    }

    environment_variable {
      name = "ENV"
      value = lower(var.app_env)
      type = "PLAINTEXT"
    }

    environment_variable {
      name = "DATA_ENV"
      value = lower(var.data_env)
      type = "PLAINTEXT"
    }

    environment_variable {
      name  = "DEPLOY_ROLE_ARN"
      value = var.deploy_role_arn
    }

    environment_variable {
      name  = "ECS_CLUSTER_NAME"
      value = var.ecs_cluster_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "ECS_SERVICE_NAME"
      value = var.ecs_service_name
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "ECR_TAG"
      value = var.ecr_tag
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "S3_DEPLOY_ARTIFACT_DIR_URI"
      value = "s3://${var.deploy_artifact_bucket}/${local.deploy_project_name}"
      type  = "PLAINTEXT"
    }

    environment_variable {
      name  = "TERRAFORM_ENABLED"
      type  = "PLAINTEXT"
      value = var.terraform_enabled
    }

    environment_variable {
      name  = "TF_STATE_PATH"
      type  = "PLAINTEXT"
      value = local.tf_state_path
    }

    environment_variable {
      name  = "MIGRATION"
      type  = "PLAINTEXT"
      value = var.migration
    }

    environment_variable {
      name  = "SLACK_WEBHOOK"
      value = "/${lower(var.data_env)}/deploy/slack-webhook"
      type  = "PARAMETER_STORE"
    }
  }
}
