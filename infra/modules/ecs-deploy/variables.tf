// AWS region
variable "region" {
  type    = string
  default = "ap-southeast-2"
}

// AWS account ID
variable "account_id" {
  type    = string
  default = "************"
}

// Application name, e.g. 'report-generator'
variable "app_name" {
  type = string
}

// Application environment, e.g. 'dev', 'test', 'prod'
variable "app_env" {
  type = string
}

// Database environment, e.g. 'dev', 'test', 'prod'
variable "data_env" {
  type = string
}

// Service role ARN that grants AWS CodePipeline permission to make calls to AWS services on your behalf
variable "service_role_arn" {
  type    = string
  default = "arn:aws:iam::************:role/ci-cd-account-access-role"
}

// ARN of role for performing deployment, e.g. pushing to ECR, updating ECS service
variable deploy_role_arn {
  type = string
}

// Name of the docker image
variable "ecr_repo_name" {
  type = string
}

variable "ecr_tag" {
  type    = string
  default = "latest"
}

// ECS cluster name
variable "ecs_cluster_name" {
  type = string
}

// ECS service name
variable "ecs_service_name" {
  type = string
}

// S3 bucket for storing artifacts
variable "deploy_artifact_bucket" {
  type    = string
  default = "qv-deployment"
}

// Extra env vars for deploy stage
variable "env_vars" {
  type = list(object({
    name  = string
    value = string
    type  = string
  }))
  default = []
}

// Node runtime version for deploy buildspec
variable "node_runtime_version" {
  type    = string
  default = "18"
}

variable "buildspec_path" {
  type    = string
  default = null
}

variable "terraform_enabled" {
  description = "Whether to run terraform before deploy"
  type        = bool
  default     = false
}

variable "tf_state_path" {
  description = "Path to store terraform state"
  type        = string
  default     = ""
}
variable "use_vpc" {
  description = "Whether to use VPC for CodeBuild"
  type        = bool
  default     = false
}

variable "vpc_security_group_ids" {
  type = list(string)
  default = []
}

variable "vpc_subnets" {
  type = list(string)
  default = []

}

variable "vpc_id" {
  type    = string
  default = ""
}

variable "migration" {
  type    = bool
  default = false
}

locals {
  deploy_project_name    = "${var.app_name}-deploy-${var.app_env}"
  terraform_project_name = "${var.app_name}-terraform-${var.app_env}"
  tf_state_path          = var.tf_state_path != "" ? var.tf_state_path : "${var.app_env}/${var.app_name}"
}
