version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      # Debug
      - echo ENV = $ENV
      - pwd
      - ls -la
      - aws sts get-caller-identity

      - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
      - |
        if [[ $TERRAFORM_ENABLED == true ]]; then
           echo "terraform enabled"
           mv  tmp/artifacts.tar.gz ./
           tar -xzvf artifacts.tar.gz -C ./
           cd artifacts
        fi
      - |
        HAS_TERRAFORM=false
        if [[ -d 'infra' ]]; then
          HAS_TERRAFORM=true
        fi
      - |
        if [[ $TERRAFORM_ENABLED == true && $HAS_TERRAFORM == true ]]; then
          mv config.tfvars infra/
          mv plan_out infra/
          cd infra
      
          cd ../
          rm -rf infra
        fi


      # Send slack message
      - BUILD_VERSION=$(cat version.txt)
      - echo BUILD_VERSION = $BUILD_VERSION
      - COMMIT_SHA=$(cat sha.txt)
      - echo COMMIT_SHA = $COMMIT_SHA
      - BUILD_BRANCH=$(cat branch.txt)
      - echo BUILD_BRANCH = $BUILD_BRANCH
      - DOCKER_IMAGE=$(cat image.txt)
      - MSG_START="$BUILD_VERSION $BUILD_BRANCH"
      - |
        echo '
          ICON=:rocket:
          CHANNEL='#test-release'
          if [ "$DATA_ENV" = "prod" ]; then
            CHANNEL='#production'
          elif [ "$DATA_ENV" = "dev" ]; then
            CHANNEL='#builds'
          fi
          curl -X POST --data-urlencode "payload={\"channel\":\"$CHANNEL\",\"username\":\"$APP_NAME $ENV\",\"text\":\"$APP_NAME: $1\",\"icon_emoji\":\"$ICON\"}" $SLACK_WEBHOOK
        ' > ~/slack.sh
        chmod +x ~/slack.sh
        echo CODEBUILD_BUILD_ARN = $CODEBUILD_BUILD_ARN
        read -ra ARN_BITS <<<"$(echo $CODEBUILD_BUILD_ARN | sed 's/[:|\/]/ /g')"
        LINK="https://$AWS_REGION.console.aws.amazon.com/codesuite/codebuild/$${ARN_BITS[4]}/projects/$${ARN_BITS[6]}/build/$CODEBUILD_BUILD_ID/?region=$AWS_REGION"
        echo LINK = $LINK
        ~/slack.sh ":checkered_flag: $MSG_START Deploy starting. Env: $ENV, data: $DATA_ENV \\n$LINK" || true

      # Login to ECR
      - ECR_SERVER=$ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
      - echo ECR_SERVER = $ECR_SERVER
      - aws ecr get-login-password  --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_SERVER
      # Switch to deploy role
      - sts=$(aws sts assume-role --role-arn $DEPLOY_ROLE_ARN --role-session-name ecs-deploy)
      - aws configure set aws_access_key_id "$(echo $sts | jq -r '.Credentials.AccessKeyId')" --profile deploy
      - aws configure set aws_secret_access_key "$(echo $sts | jq -r '.Credentials.SecretAccessKey')" --profile deploy
      - aws configure set aws_session_token "$(echo $sts | jq -r '.Credentials.SessionToken')" --profile deploy
      - aws sts get-caller-identity --profile deploy

  build:
    commands:
      # Load docker image and push it to ECR
      - docker load -i docker_image.tar
      - docker images -a
      - IMAGE_ID=$(docker images -a '--format={{.CreatedAt}}___{{.ID}}' | sort | tail -n1 | sed 's/.*___//')
      - echo IMAGE_ID = $IMAGE_ID
      - docker tag $IMAGE_ID $ECR_SERVER/$ECR_REPO_NAME:$ECR_TAG
      - docker push $ECR_SERVER/$ECR_REPO_NAME:$ECR_TAG

      - |
        if [ "$MIGRATION" = "true" ]; then
        # Fetch configuration from AWS SSM and save to config.json
        aws ssm get-parameter --name "/$ENV/public-website" --query 'Parameter.Value' --output text --profile deploy > config.json  
        cat config.json 
        # Export environment variables based on the config.json content
        export $(jq -r '
        . |
        to_entries |
        map(select(.key == "DATABASE_HOST" or .key == "DATABASE_SCHEMA" or .key == "DATABASE_NAME" or .key == "DATABASE_USER" or .key == "DATABASE_PASSWORD" or .key=="DJANGO_SECRET_KEY")) |
        .[] |
        "export \(.key)=\(.value)"' config.json)

        echo Running migrations...
        docker run -e DJANGO_SECRET_KEY=$DJANGO_SECRET_KEY \
        -e DJANGO_SETTINGS_MODULE=qv.settings.dev \
        -e DATABASE_HOST="$DATABASE_HOST" \
        -e DATABASE_SCHEMA="$DATABASE_SCHEMA" \
        -e DATABASE_NAME="$DATABASE_NAME" \
        -e DATABASE_USER="$DATABASE_USER" \
        -e DATABASE_PASSWORD="$DATABASE_PASSWORD" \
        --entrypoint /usr/local/bin/migrations.sh \
        $DOCKER_IMAGE
        else
          echo "Migration is not enabled. Skipping scripts."
        fi

      # Update ECS service
      - echo ECS_CLUSTER_NAME = $ECS_CLUSTER_NAME
      - echo ECS_SERVICE_NAME = $ECS_SERVICE_NAME
      - aws ecs update-service --force-new-deployment --cluster $ECS_CLUSTER_NAME --service $ECS_SERVICE_NAME --profile deploy
  post_build:
    commands:
      - TIMEOUT=600
      - INTERVAL=10
      - START_TIME=$(date +%s)
      - |
        while true; do
          CURRENT_TIME=$(date +%s)
          ELAPSED_TIME=$((CURRENT_TIME - START_TIME))
          if [[ "$ELAPSED_TIME" -ge "$TIMEOUT" ]]; then
              echo Timeout reached without successful deployment
              'curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy failed: timeout\"}" $SLACK_WEBHOOK;'
              break
          fi
          SERVICE_DESC=$(aws ecs describe-services --services $ECS_SERVICE_NAME --cluster $ECS_CLUSTER_NAME --profile deploy)
          PRIMARY_DEPLOYMENT=$(echo $SERVICE_DESC | jq -r '.services[0].deployments | .[] | select(.status == "PRIMARY")')
          DESIRED_COUNT=$(echo $PRIMARY_DEPLOYMENT | jq -r '.desiredCount')
          RUNNING_COUNT=$(echo $PRIMARY_DEPLOYMENT | jq -r '.runningCount')
          ROLLOUT_STATE=$(echo $PRIMARY_DEPLOYMENT | jq -r '.rolloutState')
          if [[ "$ROLLOUT_STATE" == "COMPLETED" ]]; then
              echo Deployment complete. $RUNNING_COUNT/$DESIRED_COUNT instances are running
              curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy complete: $PROJECT_NAME $BUILD_VERSION $BUILD_BRANCH\"}" $SLACK_WEBHOOK;
              break
          elif [[ "$ROLLOUT_STATE" == "FAILED" ]]; then
              echo Deployment failed.
              curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$ENV deploy failed: $PROJECT_NAME $BUILD_VERSION $BUILD_BRANCH\"}" $SLACK_WEBHOOK;
              break
          else
              echo Waiting... $RUNNING_COUNT/$DESIRED_COUNT instances are running. Elapsed time $(($ELAPSED_TIME / 60)) minutes.
          fi
          sleep $INTERVAL
        done
