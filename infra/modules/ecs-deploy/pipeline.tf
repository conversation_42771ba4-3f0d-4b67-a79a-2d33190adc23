locals {
  deploy_file_key = "${var.app_name}/deploy/${var.app_env}/deploy.zip"
}

resource "aws_codepipeline" "pipeline" {
  name     = "${var.app_name}-pipeline-${var.app_env}"
  role_arn = var.service_role_arn
  artifact_store {
    location = var.deploy_artifact_bucket
    type     = "S3"
  }

  stage {
    name = "Source"
    action {
      category = "Source"
      owner    = "AWS"
      name     = "Source"
      provider = "S3"
      version  = "1"
      configuration = {
        "PollForSourceChanges" = "false"
        "S3Bucket"             = var.deploy_artifact_bucket
        "S3ObjectKey"          = local.deploy_file_key
      }
      output_artifacts = ["SourceArtifact"]
      run_order = 1
      region    = var.region
      namespace = "SourceVariables"
    }
  }
  dynamic "stage" {
    for_each = var.terraform_enabled ? ["Terraform"] : []
    content {
      name = "Terraform"
      action {
        name     = "Terraform-Plan"
        category = "Build"
        configuration = {
          "EnvironmentVariables" = jsonencode([
            {
              name  = "TF_STATE_PATH",
              type  = "PLAINTEXT",
              value = local.tf_state_path,
            },
            {
              name  = "TERRAFORM_ENABLED",
              type  = "PLAINTEXT",
              value = var.terraform_enabled
            }
          ])
          "ProjectName" = local.terraform_project_name
        }
        input_artifacts = ["SourceArtifact"]
        output_artifacts = ["DeployArtifact"]
        owner     = "AWS"
        provider  = "CodeBuild"
        region    = var.region
        run_order = 1
        version   = "1"
        namespace = "TerraformVariables"
      }
    }
  }
  dynamic "stage" {
    for_each = var.terraform_enabled && var.app_env == "prod" ? ["Approval"] : []
    content {
      name = "Approval"
      action {
        name     = "Approval"
        category = "Approval"
        owner    = "AWS"
        provider = "Manual"
        version  = "1"
        configuration = {
          "CustomData"         = "Please review the deployment plan and approve or reject the deployment."
          "ExternalEntityLink" = "https://s3.console.aws.amazon.com/s3/object/qv-deployment?region=ap-southeast-2&bucketType=general&prefix=${var.app_name}/deploy/${var.app_env}/plan_details.txt"
        }
        input_artifacts = []
        output_artifacts = []
        run_order = 1
      }
    }
  }
  stage {
    name = "Deploy"
    dynamic action {
      for_each = var.app_env != "prod"? [] : [1]
      content {
        category  = "Approval"
        owner     = "AWS"
        name      = "Approve"
        provider  = "Manual"
        version   = "1"
        run_order = 1
        region    = var.region
      }
    }
    action {
      name     = "Deploy"
      category = "Build"
      configuration = {

        "EnvironmentVariables" = jsonencode(var.env_vars)
        "EnvironmentVariables" = jsonencode(concat([
          {
            name  = "TF_STATE_PATH",
            type  = "PLAINTEXT",
            value = local.tf_state_path,
          }
        ], var.env_vars))
        "ProjectName" = local.deploy_project_name
      }
      input_artifacts = var.terraform_enabled ? ["DeployArtifact"] : ["SourceArtifact"]
      output_artifacts = ["DeployOutputArtifact"]
      owner           = "AWS"
      provider        = "CodeBuild"
      region          = var.region
      run_order       = var.app_env != "prod" ? 1 : 2
      version         = "1"
      namespace       = "DeployVariables"
    }
  }
}

data "aws_iam_role" "code_pipeline_execution_role" {
  name = "CodePipelineExecutionRole"
}

resource "aws_cloudwatch_event_rule" "pipeline_event_rule" {
  name          = "${var.app_name}-pipeline-event-rule-${var.app_env}"
  description   = "Event rule for triggering the incredible ${var.app_env} ${var.app_name} pipeline!"
  event_pattern = <<EOF
{
  "source": ["aws.s3"],
  "detail-type": ["AWS API Call via CloudTrail"],
  "detail": {
    "eventSource": ["s3.amazonaws.com"],
    "eventName": ["PutObject", "CompleteMultipartUpload", "CopyObject"],
    "requestParameters": {
      "bucketName": ["${var.deploy_artifact_bucket}"],
      "key": ["${local.deploy_file_key}"]
    }
  }
}
EOF
}

resource "aws_cloudwatch_event_target" "pipeline_event_target" {
  rule      = aws_cloudwatch_event_rule.pipeline_event_rule.name
  target_id = "${var.app_name}-pipeline-target-${var.app_env}"
  arn       = aws_codepipeline.pipeline.arn
  role_arn  = data.aws_iam_role.code_pipeline_execution_role.arn
  depends_on = [aws_codepipeline.pipeline]
}