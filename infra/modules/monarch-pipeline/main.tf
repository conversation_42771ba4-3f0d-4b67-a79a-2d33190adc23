resource "aws_codepipeline" "this" {
  name     = "${(startswith(var.service_name, "monarch-") ? "" : "monarch-")}${var.service_name}-pipeline"
  role_arn = var.pipeline_role_arn

  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"

    encryption_key {
      id   = var.s3_kms_key_arn
      type = "KMS"
    }
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["build_output"]

      configuration = {
        S3Bucket             = var.artifact_bucket_name
        S3ObjectKey          = "${var.artifact_bucket_path}/deploy.zip"
        PollForSourceChanges = "true"
      }
    }
  }

  dynamic "stage" {
    for_each = var.deployments
    content {
      name = "Deploy-${stage.value["name"]}"

      dynamic "action" {
        for_each = stage.value["approval_step"] ? [1] : []

        content {
          category         = "Approval"
          name             = "Approve-${stage.value["name"]}"
          owner            = "AWS"
          provider         = "Manual"
          input_artifacts  = []
          output_artifacts = []
          run_order        = 1
          version          = "1"

          configuration = {
            CustomData = "Approve deployment of ${var.service_name} to ${stage.value["name"]} environment"
          }
        }
      }
      action {
        category         = "Deploy"
        name             = "Deploy-${stage.value["name"]}"
        owner            = "AWS"
        provider         = "S3"
        input_artifacts  = ["build_output"]
        output_artifacts = []
        run_order        = 2
        role_arn         = stage.value["action_role"]
        version          = "1"

        configuration = {
          BucketName = stage.value["bucket_name"]
          Extract    = "true"
          ObjectKey  = stage.value["bucket_path"]
        }
      }
    }
  }
}
