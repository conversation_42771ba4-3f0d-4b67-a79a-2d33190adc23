<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_codepipeline.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codepipeline) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_codebuild_project_name"></a> [codebuild\_project\_name](#input\_codebuild\_project\_name) | Name of the codebuild project for the related service. | `string` | n/a | yes |
| <a name="input_codestar_connection_arn"></a> [codestar\_connection\_arn](#input\_codestar\_connection\_arn) | ARN of the codestar connection to be used. | `string` | n/a | yes |
| <a name="input_deployments"></a> [deployments](#input\_deployments) | Map of deployment configurations. | <pre>map(object({<br>    name        = string<br>    bucket_name = string<br>    bucket_path = string<br>    action_role = string<br>  }))</pre> | n/a | yes |
| <a name="input_github_repo"></a> [github\_repo](#input\_github\_repo) | Github repo for the related service. | `string` | n/a | yes |
| <a name="input_pipeline_bucket_name"></a> [pipeline\_bucket\_name](#input\_pipeline\_bucket\_name) | Name of the s3 bucket for storing pipeline artifacts. | `string` | n/a | yes |
| <a name="input_pipeline_role_arn"></a> [pipeline\_role\_arn](#input\_pipeline\_role\_arn) | Role used by codepipeline project. | `string` | n/a | yes |
| <a name="input_s3_kms_key_arn"></a> [s3\_kms\_key\_arn](#input\_s3\_kms\_key\_arn) | ARN of the s3 kms key. | `string` | n/a | yes |
| <a name="input_service_name"></a> [service\_name](#input\_service\_name) | Name of the related service. | `string` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->