variable "service_name" {
  type        = string
  description = "Name of the related service."
}

variable "s3_kms_key_arn" {
  type        = string
  description = "ARN of the s3 kms key."
}

variable "artifact_bucket_name" {
  type        = string
  description = "Name of the s3 bucket the build output is stored."
}

variable "artifact_bucket_path" {
  type        = string
  description = "Path where the build output is stored."
}

variable "pipeline_bucket_name" {
  type        = string
  description = "Name of the s3 bucket for storing pipeline artifacts."
}

variable "pipeline_role_arn" {
  type        = string
  description = "Role used by codepipeline project."
}

variable "deployments" {
  type = list(object({
    name        = string
    bucket_name = string
    bucket_path = string
    action_role = string
    approval_step = bool
  }))
  description = "Map of deployment configurations."
}

variable "codestar_connection_arn" {
  type        = string
  description = "ARN of the codestar connection to be used."
}

variable "github_repo" {
  type        = string
  description = "Github repo for the related service."
}

variable "codebuild_project_name" {
  type        = string
  description = "Name of the codebuild project for the related service."
}
