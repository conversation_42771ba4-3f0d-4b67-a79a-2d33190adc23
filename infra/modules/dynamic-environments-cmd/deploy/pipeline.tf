locals {
  deploy_file_key = "${var.app_name}/deploy/${var.app_env}/deploy.zip"
}

data "aws_iam_role" "code_pipeline_execution_role" {
  name = "CodePipelineExecutionRole"
}

resource "aws_codepipeline" "pipeline" {
  name     = "${var.app_name}-pipeline-${var.app_env}"
  role_arn = var.service_role_arn
  tags     = {}
  tags_all = {}

  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      category      = "Source"
      configuration = {
        "PollForSourceChanges" = "false"
        "S3Bucket"             = var.build_bucket_name
        "S3ObjectKey"          = local.deploy_file_key
      }
      input_artifacts  = []
      name             = "Source"
      namespace        = "SourceVariables"
      output_artifacts = [
        "SourceArtifact",
      ]
      owner     = "AWS"
      provider  = "S3"
      region    = var.region
      run_order = 1
      version   = "1"
    }
  }
  stage {
    name = "Deploy"
    action {
      name          = "Deploy"
      category      = "Build"
      configuration = {
        "EnvironmentVariables" = jsonencode(var.env_vars)
        "ProjectName"          = local.deploy_project_name
      }
      input_artifacts = [
        "SourceArtifact",
      ]
      namespace        = "DeployVariables"
      output_artifacts = [
        "DeployArtifact",
      ]
      owner     = "AWS"
      provider  = "CodeBuild"
      region    = var.region
      run_order = 1
      version   = "1"
    }
  }
}

resource "aws_cloudwatch_event_rule" "pipeline_event_rule" {
  name        = "${var.app_name}-pipeline-event-rule-${var.app_env}"
  description = "Event rule for triggering the incredible ${var.app_env} ${var.app_name} pipeline!"

  event_pattern = <<EOF
{
  "source": ["aws.s3"],
  "detail-type": ["AWS API Call via CloudTrail"],
  "detail": {
    "eventSource": ["s3.amazonaws.com"],
    "eventName": ["PutObject", "CompleteMultipartUpload", "CopyObject"],
    "requestParameters": {
      "bucketName": ["${var.build_bucket_name}"],
      "key": ["${local.deploy_file_key}"]
    }
  }
}
EOF
}

resource "aws_cloudwatch_event_target" "pipeline_event_target" {
  rule       = aws_cloudwatch_event_rule.pipeline_event_rule.name
  target_id  = "${var.app_name}-pipeline-target-${var.app_env}"
  arn        = aws_codepipeline.pipeline.arn
  role_arn   = data.aws_iam_role.code_pipeline_execution_role.arn
  depends_on = [aws_codepipeline.pipeline]
}

