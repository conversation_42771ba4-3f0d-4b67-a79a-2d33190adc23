version: 0.2
env:
  shell: bash
  exported-variables:
    - RELATIVE_REPORT_PATH
    - BUILD_VERSION
    - COMMIT_SHA
    - BUILD_BRANCH
  parameter-store:
    build_ssh_key: dev-git-ssh-key

phases:
  install:
    runtime-versions:
      nodejs: ${node_runtime_version}
    commands:
      - BUILD_VERSION=$(cat version.txt)
      - ENV_DIR=$(pwd)
      - COMMIT_SHA=$(cat sha.txt)
      - BUILD_BRANCH=$(cat branch.txt)
      - MSG_START="$BUILD_VERSION $BUILD_BRANCH"
      - ECR_ACCOUNT_ID=************
      - ECR_REGION=ap-southeast-2
      - ECR_URL=$ECR_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/cicd
      - DOCKER_BASE_IMAGE=node:18-alpine
      - DOCKER_IMAGE_FILENAME=docker_image.tar
      - echo Logging in to ECR...
      - aws ecr get-login-password  | docker login --username AWS --password-stdin $ECR_URL$/IMAGE_NAME
      - echo Pulling base docker image...
      - docker pull $ECR_URL/$DOCKER_BASE_IMAGE
      - docker tag $ECR_URL/$DOCKER_BASE_IMAGE $DOCKER_BASE_IMAGE

  build:
    commands:
      - cd $ENV_DIR
      - echo deploying $ENV
      - cd cmd
      - echo "$ENV_CONFIG" > .env
      - ls -la
      - echo Building docker image...
      - cd ../
      - echo "$PRIVATE_GIT_SSH_KEY" > id_rsa
      - echo "$PRIVATE_CICD_MANAGED_PEM" > cicd-managed.pem
      - docker build -t $IMAGE_NAME -f cmd/Dockerfile .
      - docker tag dynamic-environments-cmd $ECR_URL/$IMAGE_NAME:$IMAGE_TAG
      - docker push $ECR_URL/$IMAGE_NAME:$IMAGE_TAG

cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'
