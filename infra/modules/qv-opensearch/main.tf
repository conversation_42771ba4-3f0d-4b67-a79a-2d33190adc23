locals {
  domain_name = "qv-${var.environment}-${var.private ? "private" : "public"}-os"
}

data "aws_iam_policy_document" "opensearch_policy" {
  statement {
    effect = "Allow"
    principals {
      identifiers = ["*"]
      type        = "AWS"
    }
    actions = [
      "es:*",
    ]
    resources = [
      "arn:aws:es:ap-southeast-2:${var.aws_target_account_number}:domain/${local.domain_name}/*",
    ]
  }
  dynamic "statement" {
    for_each = var.private ? [1] : []
    content {
      effect = "Allow"
      principals {
        identifiers = ["*"]
        type        = "AWS"
      }
      actions = [
        "es:ESCrossClusterGet",
      ]
      resources = [
        "arn:aws:es:ap-southeast-2:${var.aws_target_account_number}:domain/${local.domain_name}",
      ]
    }
  }
}

resource "aws_cloudwatch_log_group" "cluster_logs" {
  name              = "/aws/OpenSearchService/domains/${local.domain_name}"
  retention_in_days = 14
}

resource "aws_opensearch_domain" "cluster" {
  domain_name    = local.domain_name
  engine_version = var.engine_version

  cluster_config {
    instance_type          = var.instance_type
    instance_count         = var.instance_count
    zone_awareness_enabled = true
    zone_awareness_config {
      availability_zone_count = 2
    }
  }

  encrypt_at_rest {
    enabled = true
  }

  domain_endpoint_options {
    enforce_https       = true
    tls_security_policy = "Policy-Min-TLS-1-2-2019-07"
  }

  advanced_security_options {
    enabled                        = true
    internal_user_database_enabled = true
    master_user_options {
      master_user_name     = "master"
      master_user_password = var.master_user_password
    }
  }

  node_to_node_encryption {
    enabled = true
  }

  ebs_options {
    ebs_enabled = true
    volume_size = var.ebs_volume_size
    volume_type = "gp2"
  }

  vpc_options {
    subnet_ids         = var.vpc_options.subnet_ids
    security_group_ids = var.vpc_options.security_group_ids
  }

  log_publishing_options {
    cloudwatch_log_group_arn = aws_cloudwatch_log_group.cluster_logs.arn
    log_type                 = "INDEX_SLOW_LOGS"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_opensearch_domain_policy" "opensearch_policy" {
  domain_name     = local.domain_name
  access_policies = data.aws_iam_policy_document.opensearch_policy.json

  depends_on = [aws_opensearch_domain.cluster]
}
