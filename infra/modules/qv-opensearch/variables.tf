variable "private" {
  description = "Whether to create a private OpenSearch domain"
  validation {
    condition     = can(var.private)
    error_message = "The private variable must be a boolean"
  }
}

variable "environment" {
  description = "The environment name"
  validation {
    condition     = length(var.environment) > 0
    error_message = "The environment name must not be empty"
  }
}

variable "master_user_password" {
  description = "Opensearch internal database master user password"
  validation {
    condition     = length(var.master_user_password) > 0
    error_message = "Must not be empty"
  }
}

variable "aws_target_account_number" {
  description = "The target aws account"
  validation {
    condition     = length(var.aws_target_account_number) > 0
    error_message = "Must not be empty"
  }
}

variable "engine_version" {
  description = "The OpenSearch version"
  validation {
    condition     = length(var.engine_version) > 0
    error_message = "The engine version must not be empty"
  }
}

variable "instance_type" {
  description = "The instance type for the OpenSearch domain"
  default     = "m5.large.search"
}

variable "instance_count" {
  description = "The number of instances in the OpenSearch domain"
  default     = 2
}

variable "ebs_volume_size" {
  description = "The size of the EBS volumes attached to data nodes (in GB)"
  validation {
    condition     = var.ebs_volume_size > 0
    error_message = "The EBS volume size must be greater than 0"
  }
}

variable "vpc_options" {
  description = "The VPC options for the OpenSearch domain"
  type        = object({
    subnet_ids         = list(string)
    security_group_ids = list(string)
  })
  validation {
    condition     = length(var.vpc_options.subnet_ids) > 0
    error_message = "At least one subnet must supplied"
  }
  validation {
    condition     = length(var.vpc_options.security_group_ids) > 0
    error_message = "At least one security group must supplied"
  }
}