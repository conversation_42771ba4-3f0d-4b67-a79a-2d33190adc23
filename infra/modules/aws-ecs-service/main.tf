data "aws_iam_role" "ecs_role" {
  name = "ecsTaskExecutionRole"
}

data "aws_lb_target_group" "target_group" {
  name = var.target_group_name
}

data "aws_ecs_cluster" "cluster" {
  cluster_name = var.cluster_name
}

resource "aws_ecs_service" "service" {
  name            = "${var.service_name}-${var.environment}"
  cluster         = data.aws_ecs_cluster.cluster.id
  task_definition = aws_ecs_task_definition.task.id
  desired_count   = var.min_instances
  launch_type     = "FARGATE"

  deployment_controller {
    type = "CODE_DEPLOY"
  }

  load_balancer {
    target_group_arn = data.aws_lb_target_group.target_group.arn
    container_name   = var.container_name
    container_port   = var.container_port
  }

  network_configuration {
    assign_public_ip = false
    security_groups  = var.security_groups
    subnets          = var.subnets
  }

  lifecycle {
    ignore_changes = [
      desired_count,
      load_balancer,
      task_definition
    ]
  }
}

resource "aws_ecs_task_definition" "task" {
  family                   = "${var.service_name}-${var.environment}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = 512
  memory                   = 1024

  execution_role_arn    = data.aws_iam_role.ecs_role.arn
  task_role_arn         = var.task_role_arn
  container_definitions = var.task_definition
}

# resource "aws_appautoscaling_target" "scaling" {
#   max_capacity       = var.max_instances
#   min_capacity       = var.min_instances
#   resource_id        = "services/${var.cluster_name}/${aws_ecs_service.service.name}"
#   scalable_dimension = "ecs:service:DesiredCount"
#   service_namespace  = "ecs"
# }

# resource "aws_appautoscaling_policy" "policy" {
#   name               = "${var.service_name}-ecs-policy"
#   policy_type        = "TargetTrackingScaling"
#   resource_id        = "services/${var.cluster_name}/${aws_ecs_service.service.name}"
#   scalable_dimension = "ecs:service:DesiredCount"
#   service_namespace  = "ecs"

#   target_tracking_scaling_policy_configuration {
#     predefined_metric_specification {
#       predefined_metric_type = "ECSServiceAverageCPUUtilization"
#     }

#     target_value = 75
#   }

#   depends_on = [aws_appautoscaling_target.scaling]
# }
