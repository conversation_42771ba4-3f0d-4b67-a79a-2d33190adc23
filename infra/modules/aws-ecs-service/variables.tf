variable "cluster_name" {
  type = string
}

variable "service_name" {
  type = string
}

variable "environment" {
  type = string
}

variable "target_group_name" {
  type = string
}

variable "max_instances" {
  type = string
}

variable "min_instances" {
  type = string
}

variable "container_name" {
  type = string
}

variable "container_port" {
  type = string
}

variable "task_definition" {
  type = string
}

variable "security_groups" {
  type = list(string)
}

variable "subnets" {
  type = list(string)
}

variable "task_role_arn" {
  type = string
  default = data.aws_iam_role.ecs_role.arn
}
