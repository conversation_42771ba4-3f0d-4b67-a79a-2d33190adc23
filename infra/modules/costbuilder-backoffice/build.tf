resource "aws_codebuild_project" "ui" {
  name          = "${var.project_name}_ui_${var.env}"
  description   = "${var.project_description} Build UI for ${var.branch_name}"
  build_timeout = "10"
  service_role  = aws_iam_role.codebuild_role.arn

  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = var.codebuild_image
    type         = "LINUX_CONTAINER"

    environment_variable {
      name  = "ENV"
      value = var.env
    }

    environment_variable {
      name  = "VITE_API_BASE_URL"
      value = "https://${var.api_domain}/"
    }

    environment_variable {
      name  = "VITE_API_BASE_URL_SOCKET"
      value = "ws.${var.api_domain}"
    }

    environment_variable {
      name  = "VITE_AUTH0_DOMAIN"
      value = var.vite_auth0_domain
    }

    environment_variable {
      name  = "VITE_AUTH0_PUBLIC_KEY"
      value = var.vite_auth0_client_id
    }

    environment_variable {
      name  = "CLOUDFRONT_DIST"
      value = var.cloudfront_distribution
    }

    environment_variable {
      name  = "DEPLOY_ROLE_ARN"
      value = var.deploy_role_arn
    }

    environment_variable {
      name  = "UI_SITE_BUCKET"
      value = var.ui_site_bucket
    }

  }

  source {
    type      = "CODEPIPELINE"
    buildspec = "ui/buildspec.yml"
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = var.common_tags
}

resource "aws_codebuild_project" "lambda" {
  name          = "${var.project_name}_lambda_${var.env}"
  description   = "${var.project_description} Build Lambda for ${var.branch_name}"
  build_timeout = "20"
  service_role  = aws_iam_role.codebuild_role.arn

  environment {
    compute_type    = "BUILD_GENERAL1_MEDIUM"
    image           = var.codebuild_image
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "ENV"
      value = var.env
    }

    environment_variable {
      name  = "API_DOMAIN"
      value = var.api_domain
    }

    environment_variable {
      name  = "API_CONFIG"
      value = var.api_config_key
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "DEPLOY_ROLE_ARN"
      value = var.deploy_role_arn
    }
  }

  source {
    type      = "CODEPIPELINE"
    buildspec = "lambda/buildspec.yml"
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = var.common_tags
}
