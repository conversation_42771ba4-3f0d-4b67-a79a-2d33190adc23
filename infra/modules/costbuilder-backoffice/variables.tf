variable "env" {
  type        = string
  description = "Environment name"
}

variable "product_name" {
  type        = string
  description = "Name of the product group."
}

variable "application_name" {
  type        = string
  description = "Name of the application within the product group."
}

variable "project_name" {
  type        = string
  description = "Name of the product group."
}

variable "project_description" {
  type        = string
  description = "Name of the application within the product group."
}

variable "codestar_connection_arn" {
  type        = string
  description = "ARN of the codestar connection to be used."
  default     = "arn:aws:codestar-connections:ap-southeast-2:************:connection/5d595882-9c5c-4ae7-a448-2218e8be47a2"
}

variable "github_repo" {
  type        = string
  description = "Github repo for the related service."
}

variable "branch_name" {
  type        = string
  description = "Branch to watch for activity"
}

variable "api_config_key" {
  type        = string
  description = "Parameter store key in cicd for the lambda/infra.yml config"
}

variable "api_domain" {
  type        = string
  description = "API domain"
}

variable "vite_auth0_domain" {
  type        = string
  description = "Auth0 domain for sign in"
}

variable "vite_auth0_client_id" {
  type        = string
  description = "Auth0 public key for sign in"
}

variable "ui_site_bucket" {
  type        = string
  description = "Bucket name for the static site files"
}

variable "deploy_role_arn" {
  type = string
  description = "Role that deploys the UI to its bucket and runs serverless deploy"
}

variable "cloudfront_distribution" {
  type        = string
  description = "Distribution ID to clear cache"
}

variable "region" {
  type        = string
  description = "Region to operate in"
  default     = "ap-southeast-2"
}

variable "common_tags" {
  type        = map
  description = "Common tags you want applied to all components."
}

variable "pipeline_bucket_name" {
  type        = string
  description = "Bucket used for pipeline storage"
  default     = "qv-deployment"
}

variable "codebuild_image" {
  type        = string
  description = "Image to build node projects"
  default     = "aws/codebuild/standard:6.0"
}

variable "node_runtime_version" {
  type        = string
  description = "Version of node to use"
  default     = 16
}

variable "account_id" {
  type        = string
  description = "Account to operate in"
  default     = "************"
}
