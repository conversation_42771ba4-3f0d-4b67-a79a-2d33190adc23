resource "aws_codepipeline" "cbbo_codepipeline" {
  name     = "${var.product_name}-${var.application_name}-${var.env}-pipeline"
  role_arn = "arn:aws:iam::948396734470:role/costbuilder-backoffice-${var.env}-pipeline-role"
#  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = var.pipeline_bucket_name
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeStarSourceConnection"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        ConnectionArn    = var.codestar_connection_arn
        FullRepositoryId = var.github_repo
        BranchName       = var.branch_name
      }
    }
  }

  stage {
    name = "Build"

    dynamic action {
      for_each = lower(var.env) == "prod" ? [1] : []
      content {
        name          = "Approve-${var.env}"
        category      = "Approval"
        # configuration = {
          # NotificationArn    = "arn:aws:sns:us-east-2:80398EXAMPLE:MyApprovalTopic",
          # ExternalEntityLink = "",
          # CustomData         = ""
        # }
        input_artifacts  = []
        output_artifacts = []
        owner            = "AWS"
        provider         = "Manual"
        region           = var.region
        run_order        = 1
        version          = "1"
      }
    }

    action {
      name            = "Build_UI_${var.env}"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      run_order       = 2
      version         = "1"

      configuration = {
        ProjectName = aws_codebuild_project.ui.name
      }
    }

    action {
      name            = "Build_Lambda_${var.env}"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      run_order       = 2
      version         = "1"

      configuration = {
        ProjectName = aws_codebuild_project.lambda.name
      }
    }
  }

}
