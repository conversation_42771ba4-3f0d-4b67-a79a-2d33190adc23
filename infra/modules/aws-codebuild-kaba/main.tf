data "aws_iam_role" "codebuild-role" {
  name = var.service_role
}

resource "aws_codebuild_project" "this" {
  name          = "${var.service_name}-codebuild"
  description   = "${var.service_name}-codebuild"
  build_timeout = "15"

  service_role = data.aws_iam_role.codebuild-role.arn

  artifacts {
    type           = "S3"
    location       = var.artifact_location
    namespace_type = "NONE"
    name           = var.artifact_name
    path           = "/"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:4.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    dynamic "environment_variable" {
      for_each = var.environment_variables
      content {
        name  = environment_variable.value["name"]
        value = environment_variable.value["value"]
      }
    }
  }

  source_version = var.source_branch
  source {
    type                = "GITHUB"
    location            = "https://github.com/${var.source_repo}.git"
    report_build_status = true
    git_clone_depth     = 0

    buildspec = var.buildspec
  }
}

resource "aws_codebuild_webhook" "this" {
  project_name = aws_codebuild_project.this.name
  build_type   = "BUILD"

  filter_group {
    filter {
      type    = "EVENT"
      pattern = var.webhook_events #"PULL_REQUEST_CREATED,PULL_REQUEST_MERGED,PUSH"
    }
  }
}
