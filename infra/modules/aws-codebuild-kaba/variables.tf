variable "service_name" {
  description = "Name of service being built."
  type        = string
}

variable "service_role" {
  description = "Name of role to be used by codebuild."
  type        = string
}

variable "source_repo" {
  description = "Path of github repo e.g 'Quotable-Value/reporting'."
  type        = string
}

variable "source_branch" {
  description = "Repo branch to watch."
  type        = string
}

variable "artifact_location" {
  description = "Artifact bucket name."
  type        = string
  default     = ""
}

variable "artifact_name" {
  type    = string
  default = ""
}

variable "environment_variables" {
  description = "Code build environment variables."
  type = list(object({
    name  = string
    value = string
  }))
}

variable "buildspec" {
  description = "Build spec file."
}

variable "webhook_events" {
  description = "Github webhook events."
  default     = ""
  type        = string
}
