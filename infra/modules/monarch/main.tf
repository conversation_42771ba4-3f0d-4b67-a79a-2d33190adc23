locals {
  codebuild_bucket_name  = "qv-deployment"
  report_bucket_name     = "qv-deployment-reports"
}

data "aws_iam_role" "monarch-codebuild" {
  name = "codebuild-monarch-role"
}

module "codebuild" {
  source = "../monarch-codebuild"

  service_name = var.service_name
  service_role = data.aws_iam_role.monarch-codebuild.arn

  artifact_bucket = local.codebuild_bucket_name
  artifact_path   = var.s3_bucket_prefix
  cache_path      = "${var.s3_bucket_prefix}/cache"

  report_bucket = local.report_bucket_name
  report_path   = var.s3_bucket_prefix

  custom_buildspec = var.custom_buildspec

  build_bucket_path = var.s3_bucket_prefix

  source_version = "master"
  github_repo    = "https://github.com/${var.github_repo}.git"
}