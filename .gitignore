# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*
*.tfvars

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*
.idea/*
**/*.local.*

**/.nyc_output
**/.serverless

## MAC
**/.DS_Store

*.iml
api/node_modules
cmd/node_modules
scripts/node_modules
ui/node_modules
lambda/node_modules
common/node_modules
pgdump/node_modules
./ui/coverage

lambda/infra.yml
lambda/config.json
ui/dist
cmd/dist
**/local
**/*.env
./cmd/db-scripts
id_rsa
cicd-managed.pem

cmd/config.json

cmd/tf/*
!cmd/tf/app_template/
!cmd/tf/data_template/
.terraform.lock.hcl
.terraform
locals.tf

data.json

mochawesome-report/

db-scripts
db-scripts-test

pgdump/config.json

coverage
