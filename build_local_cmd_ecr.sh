export ECR_ACCOUNT_ID=************
export AWS_REGION=ap-southeast-2
export ECR_URL=$ECR_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/cicd
export IMAGE_NAME=dynamic-environments-cmd

aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_URL$/IMAGE_NAME

## platform=linux/amd64 may differ depending on your local machine

docker build -t $IMAGE_NAME -f cmd/Dockerfile . --platform=linux/amd64
docker tag dynamic-environments-cmd $ECR_URL/$IMAGE_NAME:local
docker push $ECR_URL/$IMAGE_NAME:local
