export const PERMISSIONS = Object.freeze({
  CREATE_APP_ENV: 'create:app-env',
  LIST_APP_ENV: 'list:app-env',
  READ_APP_ENV: 'read:app-env',
  UPDATE_APP_ENV: 'update:app-env',
  DELETE_APP_ENV: 'delete:app-env',

  CREATE_DATA_ENV: 'create:data-env',
  LIST_DATA_ENV: 'list:data-env',
  READ_DATA_ENV: 'read:data-env',
  UPDATE_DATA_ENV: 'update:data-env',
  DELETE_DATA_ENV: 'delete:data-env',

  DEPLOY_DEV_AWS: 'deploy:dev-aws-account',
  DEPLOY_PROD_AWS: 'deploy:prod-aws-account',

  CREATE_TESTDATA: 'create:testdata',

  CREATE_BACKGROUND_TEST: 'create:background-test',
});

export default PERMISSIONS;
