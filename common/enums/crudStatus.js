export const STATUS_ADDED = 'ADDED';
export const STATUS_UPDATED = 'UPDATED';
export const STATUS_DELETED = 'DELETED';
export const STATUS_SUCCESS = 'SUCCESS';

export const STATUS_INVALID = 'INVALID';
export const STATUS_MISSING = 'MISSING';
export const STATUS_DUPLICATE = 'DUPLICATE';
export const STATUS_LOCKED = 'LOCKED';
export const STATUS_FAILED = 'FAILED';

export function crudHttpStatus(status) {
  switch (status) {
    case STATUS_ADDED:
      return 201;
    case STATUS_UPDATED:
    case STATUS_DELETED:
    case STATUS_SUCCESS:
      return 200;
    case STATUS_INVALID:
    case STATUS_MISSING:
      return 400;
    case STATUS_LOCKED:
      return 403;
    case STATUS_DUPLICATE:
      return 409;
    case STATUS_FAILED:
      return 500;
  }

  return 200;
}

export function isSuccessfulStatus(status) {
  status = status?.hasOwnProperty('status') ? status.status : status;
  switch (status) {
    case STATUS_ADDED:
    case STATUS_UPDATED:
    case STATUS_DELETED:
    case STATUS_SUCCESS:
      return true;
  }
  return false;
}
