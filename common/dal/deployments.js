import {
  PutItemCommand, QueryCommand, ScanCommand, UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { DEPLOYMENTS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';
import { getAllRecords } from './common.js';
import { formatTestReport } from '../service/testReports.js';
import { DeploymentStatus } from '../enums/deploymentStatus.js';

export async function saveDeployment(env, type, numberOfServices, userName) {
  const deploymentId = Date.now();
  await saveDeployments(env, type, deploymentId, numberOfServices, userName);
  return deploymentId;
}

export async function saveDeployments(env, type, deploymentId, numberOfServices, userName) {
  const deploymentItem = {
    DeploymentId: { S: deploymentId.toString() },
    Env: { S: env },
    Type: { S: type },
    NumberOfServices: { S: numberOfServices.toString() },
    CreatedAt: { S: Date.now().toString() },
    Status: { S: DeploymentStatus.IN_PROGRESS },
    UserName: { S: userName },
  };

  const params = {
    TableName: getTableName(DEPLOYMENTS),
    Item: deploymentItem,
  };
  await dynamoDbClient.send(new PutItemCommand(params));
}

export async function getIncompleteDeployments(appEnv, type) {
  let deployments = await getDeploymentsByEnv(appEnv);
  logger.info('deployments', { deployments });
  deployments = deployments.filter((deployment) => deployment.Type.S === type);
  const completedStatuses = [DeploymentStatus.TESTS_COMPLETED, DeploymentStatus.ERROR];
  return deployments.filter((deployment) => !completedStatuses.includes(deployment.Status.S));
}

export async function getDeploymentsByEnv(appEnv) {
  const params = {
    TableName: getTableName(DEPLOYMENTS),
    IndexName: 'EnvIndex',
    KeyConditionExpression: '#Env = :Env',
    ExpressionAttributeNames: {
      '#Env': 'Env',
    },
    ExpressionAttributeValues: {
      ':Env': { S: appEnv },
    },
  };
  logger.info(`getDeploymentsByEnv ${appEnv}`, { params });
  const data = await dynamoDbClient.send(new QueryCommand(params));
  return data.Items;
}

export async function getDeploymentByEnvironmentKeyword(keyword) {
  const params = {
    TableName: getTableName(DEPLOYMENTS),
    FilterExpression: 'contains(#env, :envValue)',
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: {
      ':envValue': { S: keyword },
    },
  };

  return (await dynamoDbClient.send(new ScanCommand(params))).Items;
}

export async function getAllDeployments() {
  return getAllRecords(getTableName(DEPLOYMENTS));
}

export function formatDeployments(deployments) {
  return deployments?.map((deployment) => {
    const {
      Env,
      Type,
      DeploymentId,
      NumberOfServices,
      TestResults,
      CreatedAt,
      Status,
    } = deployment;

    return {
      ...formatTestReport(TestResults?.S),
      env: Env?.S,
      type: Type?.S,
      deploymentId: DeploymentId?.S,
      numberOfServices: NumberOfServices?.S,
      createdAt: CreatedAt?.S,
      status: Status?.S,
    };
  });
}

export async function getDeploymentByDeploymentId(deploymentId) {
  const params = {
    TableName: getTableName(DEPLOYMENTS),
    KeyConditionExpression: '#DeploymentId = :DeploymentId',
    ExpressionAttributeNames: {
      '#DeploymentId': 'DeploymentId',
    },
    ExpressionAttributeValues: {
      ':DeploymentId': { S: deploymentId },
    },
  };

  const data = await dynamoDbClient.send(new QueryCommand(params));
  if (data.Items.length === 0) {
    return null;
  }
  return data.Items[0];
}

export async function updateTestResultsForDeployment(deploymentId, testResults) {
  const params = {
    TableName: getTableName(DEPLOYMENTS),
    Key: {
      DeploymentId: { S: deploymentId },
    },
    UpdateExpression: 'SET  #TestResults = :TestResults',
    ExpressionAttributeNames: {
      '#TestResults': 'TestResults',
    },
    ExpressionAttributeValues: {
      ':TestResults': { S: JSON.stringify(testResults) },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateDeploymentStatus(deploymentId, status) {
  const params = {
    TableName: getTableName(DEPLOYMENTS),
    Key: {
      DeploymentId: { S: deploymentId },
    },
    UpdateExpression: 'SET  #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}
