import { UpdateItemCommand } from '@aws-sdk/client-dynamodb';

import { TEST_REPORTS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';

export async function updateTestReportByReportId(reportId, result) {
  const params = {
    TableName: getTableName(TEST_REPORTS),
    Key: {
      ReportId: { S: reportId },
    },
    UpdateExpression: 'set TestResults = :TestResults',
    ExpressionAttributeValues: {
      ':TestResults': { S: JSON.stringify(result) },
    },
  };
  await dynamoDbClient.send(new UpdateItemCommand(params));
}
