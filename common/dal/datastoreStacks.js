import { getAllRecords } from './common.js';
import { getTableName } from './getResourceName.js';
import { DATASTORE_STACKS, DYNAMIC_DB_BAK_FILE_LOCATION } from '../util/const.js';
import { getDataStoresByEnv } from './datastores.js';

export async function getEnabledDatastoreStacks() {
  return (await getAllDatastoreStacks()).filter((appStack) => appStack.Enabled?.BOOL);
}

export async function getAllDatastoreStacks() {
  return getAllRecords(getTableName(DATASTORE_STACKS));
}

export function formatDatastoreStacks(podsConfigurations) {
  return podsConfigurations.map((item) => {
    const {
      Name,
      Type,
      Stacks,
      Enabled,
    } = item;
    return {
      name: Name.S,
      type: Type.S,
      stacks: Stacks.L.map((stack) => stack.S).join(','),
      enabled: Enabled.BOOL,
    };
  });
}

export async function getDatastoresByStacks(stacks) {
  return (await getEnabledDatastoreStacks()).filter((datastore) => {
    const datastoreStacks = datastore.Stacks.L.map((stack) => stack.S);
    return datastoreStacks.some((stack) => stacks.includes(stack));
  });
}
export async function getDatabaseNames(dataEnv, type) {
  const datastores = (await getDataStoresByEnv(dataEnv)).filter((stack) => stack.Type.S === type);
  return datastores.map((stack) => stack.Name.S);
}

export async function getDynamicDbFile(dataEnv, dataStore) {
  logger.info(`getDynamicDbFile ${dataEnv} ${dataStore}`);
  return [...DYNAMIC_DB_BAK_FILE_LOCATION].filter(([key, value]) => key === dataStore);
}
