import {
  DeleteI<PERSON><PERSON>ommand,
  <PERSON><PERSON><PERSON><PERSON>ommand, <PERSON><PERSON><PERSON>ommand, <PERSON>an<PERSON>ommand, UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';

import { APP_ENVIRONMENTS, DATA_ENVIRONMENTS } from '../util/const.js';
import { INITIALISING_INFRA, TEARING_DOWN, UPDATING } from '../enums/environmentStatus.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getDataStoreByEnvironments } from './datastores.js';
import { getTableName } from './getResourceName.js';
import { formatApplications } from './applications.js';
import { formatTestReport } from '../service/testReports.js';

export async function getDataEnvironmentsByStatus(statuses) {
  const expressionAttributeValues = {};
  const filterExpressions = [];

  statuses.forEach((status, index) => {
    const key = `:status${index + 1}`;
    expressionAttributeValues[key] = { S: status };
    filterExpressions.push(`#status = ${key}`);
  });

  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    FilterExpression: filterExpressions.join(' OR '),
    ExpressionAttributeNames: {
      '#status': 'Status',
    },
    ExpressionAttributeValues: expressionAttributeValues,
  };

  return (await dynamoDbClient.send(new ScanCommand(params))).Items;
}

export async function getDataEnvironmentByName(name) {
  const input = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    KeyConditionExpression: '#name = :value',
    ExpressionAttributeNames: {
      '#name': 'Name',
    },
    ExpressionAttributeValues: {
      ':value': { S: name },
    },
  };
  logger.info(`getDataEnvironmentByName ${name}`, { input });
  return (await dynamoDbClient.send(new QueryCommand(input))).Items;
}

export async function saveDataEnvironment(name, stage, branch, defaultBranch, drive, costcentre, size, stacks, logs) {
  const currentTimeMillis = Date.now().toString();
  const logsArray = logs
    .map((item) => ({
      M: {
        Task: { S: item.task },
        Url: { S: item.url },
      },
    }));
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Item: {
      Name: { S: name },
      Stage: { S: stage },
      Branch: { S: branch },
      DefaultBranch: { S: defaultBranch },
      StartTime: { N: currentTimeMillis },
      EndTime: { N: '0' },
      TeardownStartTime: { N: '0' },
      TeardownEndTime: { N: '0' },
      Status: { S: INITIALISING_INFRA },
      Drive: { S: drive },
      CostCentre: { S: costcentre },
      Size: { S: size },
      Stacks: { L: stacks.map((stack) => ({ S: stack })) },
      Message: { S: '' },
      Config: { S: '{}' },
      Logs: { L: logsArray },
    },
  };
  return dynamoDbClient.send(new PutItemCommand(params));
}

export function addCloudWatchLogs(logs, Logs) {
  logger.info('adding logs to environment', { logs });
  logs.forEach((item) => (Logs?.L.push({
    M: { Url: { S: item.url }, Task: { S: item.task } },
  })));
}

export async function deleteDataEnvironmentByName(name, logs) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status,#Logs = :Logs, #TeardownStartTime = :TeardownStartTime',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#Logs': 'Logs',
      '#TeardownStartTime': 'TeardownStartTime',
    },
    ExpressionAttributeValues: {
      ':Status': { S: TEARING_DOWN },
      ':TeardownStartTime': { N: currentTimeMillis },
      ':Logs': logs,
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateEnvironmentStatusByName(env, status, endTime) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: env },
    },
    UpdateExpression: 'SET #Status = :Status, #EndTime = :EndTime',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#EndTime': 'EndTime',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
      ':EndTime': { N: endTime },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateEnvironmentByNameDestroy(env, status, teardownEndTime) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: env },
    },
    UpdateExpression: 'SET #Status = :Status, #TeardownEndTime  = :TeardownEndTime',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#TeardownEndTime': 'TeardownEndTime',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
      ':TeardownEndTime': { N: teardownEndTime },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDataEnvironmentStatus(name, status) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export function formatLogs(Logs) {
  return Logs?.L.reverse().map((log) => {
    const {
      Url,
      Task,
    } = log.M;
    return {
      task: Task?.S,
      url: Url?.S,
    };
  });
}

export async function getDataStoreList(existingDataEnvironments) {
  if (existingDataEnvironments.length === 0) {
    return [];
  }
  const environments = existingDataEnvironments.map((item) => item.Name?.S);
  logger.info(`getting data store list for ${environments}`);
  return (await getDataStoreByEnvironments(environments)).sort((a, b) => a.Type?.S.localeCompare(b.Type?.S));
}

export async function updatePgConfigByName(env, pgConfig) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: env },
    },
    UpdateExpression: 'SET #PgConfig = :PgConfig',
    ExpressionAttributeNames: {
      '#PgConfig': 'PgConfig',
    },
    ExpressionAttributeValues: {
      ':PgConfig': { S: JSON.stringify(pgConfig) },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateSqlConfigByName(env, sqlConfig) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: env },
    },
    UpdateExpression: 'SET #SqlConfig = :SqlConfig',
    ExpressionAttributeNames: {
      '#SqlConfig': 'SqlConfig',
    },
    ExpressionAttributeValues: {
      ':SqlConfig': { S: JSON.stringify(sqlConfig) },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export function formatDataEnvironments(existingDataEnvironments, datastoreList, applicationsList = []) {
  return existingDataEnvironments.map((item) => {
    const {
      Name,
      Branch,
      StartTime,
      EndTime,
      Drive,
      CostCentre,
      DefaultBranch,
      Status,
      TeardownStartTime,
      TeardownEndTime,
      BackupVersion,
      TestResults,
      HealthCheckResult,
      Logs,
    } = item;
    const dataStores = datastoreList.filter((dataStore) => dataStore.Env.S === Name.S);
    const applications = applicationsList.filter((application) => application.Env.S === Name.S);

    return {
      name: Name?.S,
      backupVersion: BackupVersion?.S,
      branch: Branch?.S,
      startTime: StartTime?.N,
      endTime: EndTime?.N,
      drive: Drive?.S,
      costCentre: CostCentre?.S,
      defaultBranch: DefaultBranch?.S,
      teardownStartTime: TeardownStartTime?.N,
      teardownEndTime: TeardownEndTime?.N,
      status: Status?.S,
      dataStores: formatDataStores(dataStores),
      applications: formatApplications(applications),
      stacks: item.Stacks?.L.map((stack) => stack.S),
      logs: formatLogs(Logs),
      ...formatTestReport(TestResults?.S),
      healthCheckResult: HealthCheckResult?.S,

    };
  });
}

export function formatDataStores(DataStores) {
  return DataStores?.map((dataStore) => {
    const {
      Type,
      Name,
      StartTime,
      EndTime,
      TeardownStartTime,
      TeardownEndTime,
      LastUpdatedTime,
      Status,
      Message,
      Sha,
      PercentComplete,
    } = dataStore;
    return {
      type: Type?.S,
      name: Name?.S,
      startTime: StartTime?.N,
      endTime: EndTime?.N,
      teardownStartTime: TeardownStartTime?.N,
      teardownEndTime: TeardownEndTime?.N,
      lastUpdatedTime: LastUpdatedTime?.N,
      status: Status?.S,
      message: Message?.S,
      sha: Sha?.S,
      percentComplete: PercentComplete?.S,
    };
  });
}

export function startTimeCompareFn() {
  return (a, b) => {
    const aStartTime = a.StartTime.N;
    const bStartTime = b.StartTime.N;
    if (aStartTime > bStartTime) {
      return -1;
    }
    if (aStartTime < bStartTime) {
      return 1;
    }
    return 0;
  };
}

export async function updateLogsForDataEnvironment(name, logs) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Logs = :Logs',
    ExpressionAttributeNames: {
      '#Logs': 'Logs',
    },
    ExpressionAttributeValues: {
      ':Logs': logs,
    },
  };

  return (dynamoDbClient.send(new UpdateItemCommand(params)));
}

export async function addLogsToDataEnv(dataEnv, task, url) {
  logger.info(`adding logs to data environment ${dataEnv}`);
  const dataEnvironments = await getDataEnvironmentByName(dataEnv);
  const { Logs } = dataEnvironments[0];
  addCloudWatchLogs([{ task, url }], Logs);
  await updateLogsForDataEnvironment(dataEnv, Logs);
}

export async function updateNewDataEnvironment(name, defaultBranch, logs) {
  logger.info('updating new data environment', {
    name,
    defaultBranch,
    logs,
  });
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET  #DefaultBranch = :DefaultBranch, #Status = :Status, #Logs = :Logs',
    ExpressionAttributeNames: {
      '#DefaultBranch': 'DefaultBranch',
      '#Status': 'Status',
      '#Logs': 'Logs',
    },
    ExpressionAttributeValues: {
      ':DefaultBranch': { S: defaultBranch },
      ':Status': { S: UPDATING },
      ':Logs': logs,
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateDataEnvironmentJsonConfig(name, contextConfig) {
  const params = {
    TableName: DATA_ENVIRONMENTS,
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #ContextConfig = :ContextConfig',
    ExpressionAttributeNames: {
      '#ContextConfig': 'ContextConfig',
    },
    ExpressionAttributeValues: {
      ':ContextConfig': { S: JSON.stringify(contextConfig, null, 4) },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateTestResultsForDataEnvironment(dataEnv, testResults) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: dataEnv },
    },
    UpdateExpression: 'SET  #TestResults = :TestResults',
    ExpressionAttributeNames: {
      '#TestResults': 'TestResults',
    },
    ExpressionAttributeValues: {
      ':TestResults': { S: JSON.stringify(testResults) },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function deleteDataEnvironment(name) {
  const params = {
    TableName: DATA_ENVIRONMENTS,
    Key: {
      Name: { S: name },
    },
  };
  return dynamoDbClient.send(new DeleteItemCommand(params));
}
