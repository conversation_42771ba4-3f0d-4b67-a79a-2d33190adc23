import {
  PutItemCommand, ScanCommand, GetItemCommand, QueryCommand, UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';

export async function getAllRecords(tableName) {
  logger.info('Getting all records from table:', { tableName });
  const params = {
    TableName: tableName,
  };
  const command = new ScanCommand(params);
  const response = await dynamoDbClient.send(command);
  return response.Items;
}

export function paginateArray(inputArray, limit, offset) {
  const startIndex = offset;
  const endIndex = offset + limit;
  const currentPageData = inputArray.slice(startIndex, endIndex);
  const totalCount = inputArray.length;
  const totalPages = Math.ceil(totalCount / limit);
  const currentPageNumber = Math.floor(offset / limit) + 1;
  return {
    count: totalCount,
    currentPageData,
    pageNumber: currentPageNumber,
    totalPages,
  };
}

export async function saveItem(tableName, item) {
  const marshalledItem = convertKeysToUpperCase(marshall(item, { removeUndefinedValues: true }));

  const command = new PutItemCommand({
    TableName: getTableName(tableName),
    Item: marshalledItem,
  });

  logger.info('Saving item:', { command });
  return dynamoDbClient.send(command);
}

export async function updateItem(tableName, keyName, keyValue, updateData) {
  const marshalledKey = marshall(keyValue);
  const marshalledUpdateData = convertKeysToUpperCase(marshall(updateData, { removeUndefinedValues: true }));
  const updateExpressions = [];
  const expressionAttributeValues = {};

  for (const [field, value] of Object.entries(marshalledUpdateData)) {
    const attributeKey = `:${field}`;
    updateExpressions.push(`#${field} = ${attributeKey}`);
    expressionAttributeValues[attributeKey] = value;
  }

  const input = {
    TableName: getTableName(tableName),
    Key: {
      [capitalise(keyName)]: marshalledKey,
    },
    UpdateExpression: `SET ${updateExpressions.join(', ')}`,
    ExpressionAttributeNames: Object.fromEntries(Object.keys(marshalledUpdateData).map((field) => [`#${field}`, field])),
    ExpressionAttributeValues: expressionAttributeValues,
  };
  const command = new UpdateItemCommand(input);
  return dynamoDbClient.send(command);
}

export async function scanTableWithFilter(table, filter) {
  const scanParams = {
    TableName: getTableName(table),
    ...createQueryParameters(filter),
  };

  const response = await dynamoDbClient.send(new ScanCommand(scanParams));
  return response.Items.map((item) => convertKeysToLowerCase(unmarshall(item)));
}

export async function getItemById(table, filter) {
  const data = await dynamoDbClient.send(new GetItemCommand({
    TableName: getTableName(table),
    Key: marshall(filter),
  }));
  return data?.Item ? convertKeysToLowerCase(unmarshall(data.Item)) : null;
}

export async function getItemBySecondaryIndex(table, key, value) {
  const input = {
    IndexName: `${key}Index`,
    TableName: getTableName(table),
    KeyConditionExpression: '#key = :value',
    ExpressionAttributeNames: {
      '#key': key,
    },
    ExpressionAttributeValues: {
      ':value': { S: value },
    },
  };

  const data = await dynamoDbClient.send(new QueryCommand(input));
  return data?.Items ? data.Items.map((item) => convertKeysToLowerCase(unmarshall(item))) : null;
}

function convertKeysToLowerCase(obj) {
  return Object.keys(obj).reduce((acc, currentKey) => {
    const modifiedKey = currentKey.charAt(0).toLowerCase() + currentKey.slice(1);
    acc[modifiedKey] = obj[currentKey];
    return acc;
  }, {});
}

function capitalise(currentKey) {
  return currentKey.charAt(0).toUpperCase() + currentKey.slice(1);
}

function convertKeysToUpperCase(obj) {
  return Object.keys(obj).reduce((acc, currentKey) => {
    const modifiedKey = capitalise(currentKey);
    acc[modifiedKey] = obj[currentKey];
    return acc;
  }, {});
}

export function createQueryParameters(filter) {
  const FilterExpression = [];
  const ExpressionAttributeNames = {};
  const ExpressionAttributeValues = {};

  Object.entries(filter).forEach(([key, value]) => {
    if (value) {
      const attributeKey = `#${key}`;
      const attributeValueKey = `:${key}`;

      FilterExpression.push(`contains(${attributeKey}, ${attributeValueKey})`);
      ExpressionAttributeNames[attributeKey] = key.charAt(0).toUpperCase() + key.slice(1);
      ExpressionAttributeValues[attributeValueKey] = { S: value };
    }
  });

  if (FilterExpression.length === 0) {
    return {};
  }
  return {
    FilterExpression: FilterExpression.join(' and '),
    ExpressionAttributeNames,
    ExpressionAttributeValues,
  };
}
