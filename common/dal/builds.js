import { PutItemCommand, QueryCommand } from '@aws-sdk/client-dynamodb';
import { BUILDS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';
import { getAllRecords } from './common.js';

export async function getAllBuilds() {
  return getAllRecords(getTableName(BUILDS));
}

export async function saveBuilds(buildDetails, bucketKey) {
  const buildItem = {
    Key: { S: `${buildDetails.projectName}-${buildDetails.gitBranch}` },
    Sha: { S: buildDetails.gitSha },
    Application: { S: buildDetails.projectName.toString() },
    BucketKey: { S: bucketKey },
    GitRepo: { S: buildDetails.gitRepo.toString() },
    GitMessage: { S: buildDetails.gitMessage },
    GitBranch: { S: buildDetails.gitBranch },
    BuildVersion: { S: buildDetails.buildVersion },
    Status: { S: buildDetails.status },
    CreatedAt: { S: Date.now().toString() },
  };

  const params = {
    TableName: getTableName(BUILDS),
    Item: buildItem,
  };
  await dynamoDbClient.send(new PutItemCommand(params));
}

export function formatBuilds(existingAppEnvironments) {
  return existingAppEnvironments.map((item) => {
    const {
      Key,
      Sha,
      Application,
      GitRepo,
      GitMessage,
      GitBranch,
      BuildVersion,
      BucketKey,
      Status,
      CreatedAt,
    } = item;
    return {
      key: Key.S,
      sha: Sha.S,
      application: Application.S,
      gitRepo: GitRepo.S,
      gitMessage: GitMessage.S,
      gitBranch: GitBranch.S,
      buildVersion: BuildVersion.S,
      bucketKey: BucketKey?.S,
      status: Status.S,
      createdAt: CreatedAt.S,
    };
  });
}

export async function findBuildByKey(key) {
  logger.info(`findBuildKey ${key}`);
  const items = (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(BUILDS),
    KeyConditionExpression: '#key = :value',
    ExpressionAttributeNames: {
      '#key': 'Key',
    },
    ExpressionAttributeValues: {
      ':value': { S: key },
    },
  }))).Items;
  if (items.length > 0) {
    return items[0];
  }
  return null;
}
