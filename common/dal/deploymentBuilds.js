import {
  PutItemCommand, QueryCommand, UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { getTableName } from './getResourceName.js';
import { DEPLOYMENT_BUILDS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { PENDING } from '../enums/environmentStatus.js';

export async function saveDeploymentBuilds(appEnv, type, deploymentId, application, deployBranch) {
  const name = application.Name.S;
  logger.info(`saveDeploymentBuilds ${appEnv} ${type} ${deploymentId} ${name} ${deployBranch}`);
  const deploymentItem = {
    Key: { S: `${deploymentId}-${name}` },
    Application: { S: name },
    Type: { S: type },
    DeployBranch: { S: deployBranch },
    DeploymentId: { S: deploymentId.toString() },
    Env: { S: appEnv },
    Status: { S: PENDING },
    CreatedAt: { S: Date.now().toString() },
  };
  const params = {
    TableName: getTableName(DEPLOYMENT_BUILDS),
    Item: deploymentItem,
  };
  await dynamoDbClient.send(new PutItemCommand(params));
}

export async function getBuildsByDeployment(deploymentId) {
  logger.info(`getBuildsByDeployment ${deploymentId}`);
  return (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(DEPLOYMENT_BUILDS),
    IndexName: 'DeploymentIdIndex',
    KeyConditionExpression: '#DeploymentId = :value',
    ExpressionAttributeNames: {
      '#DeploymentId': 'DeploymentId',
    },
    ExpressionAttributeValues: {
      ':value': { S: deploymentId },
    },
  }))).Items;
}

export async function updateBuildIdByKey(key, buildId, status) {
  const params = {
    TableName: getTableName(DEPLOYMENT_BUILDS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #Status = :Status, #BuildId = :BuildId',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#BuildId': 'BuildId',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
      ':BuildId': { S: buildId },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateStatusByKey(key, status) {
  const params = {
    TableName: getTableName(DEPLOYMENT_BUILDS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}
