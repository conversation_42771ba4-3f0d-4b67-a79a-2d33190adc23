import { PODS_CONFIGURATION } from '../util/const.js';
import { getAllRecords } from './common.js';
import { getTableName } from './getResourceName.js';

export async function getAllPodsConfiguration() {
  return getAllRecords(getTableName(PODS_CONFIGURATION));
}

export function formatPodsConfiguration(podsConfigurations) {
  return podsConfigurations.map((item) => {
    const {
      Max,
      Min,
      Default,
      AppName,
    } = item;
    return {
      max: Max.S,
      min: Min.S,
      default: Default.S,
      appName: AppName.S,
    };
  });
}
