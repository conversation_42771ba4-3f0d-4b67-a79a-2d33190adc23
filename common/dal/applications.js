import {
  Delete<PERSON><PERSON><PERSON>ommand,
  Put<PERSON>temCommand,
  Query<PERSON>ommand,
  ScanCommand,
  UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { APPLICATIONS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';
import { findBuildByKey } from './builds.js';
import { formatTestReport } from '../service/testReports.js';
import { PipelineStatus } from '../enums/pipelineStatus.js';
import { getDeployPipelineUrl } from '../util/appNameUtli.js';
import { ApplicationType } from '../enums/applicationType.js';

async function saveApps(applications, branchExistence, defaultBranch, env, type) {
  const applicationsArray = await Promise.all(applications.map(async (application) => {
    let deployBranch = 'master';
    if (branchExistence.find((item) => item.name === application.name)?.exists === true) {
      deployBranch = defaultBranch;
    }
    deployBranch = application.branchOverride ?? deployBranch;
    const build = await findBuildByKey(`${application.name}-${deployBranch}`);
    const appName = application.name;
    const url = await getDeployPipelineUrl(appName, env);
    const { pods } = application;
    logger.info(`pods ${pods}`);
    return ({
      Key: { S: `${type}-${env}-${application.name}` },
      Env: { S: env },
      Name: { S: application.name },
      Type: { S: type },
      Pods: { S: `${pods}` },
      DeployBranch: { S: deployBranch },
      BranchOverride: { S: application.branchOverride ?? '' },
      Pipeline: { S: url },
      GitRepo: { S: application.gitRepo },
      BuildCreatedAt: { S: application.createdAt },
      Sha: { S: build?.Sha?.S ?? '' },
      BucketKey: { S: build?.BucketKey?.S ?? '' },
      Deployed: { S: '' },
      Status: { S: PipelineStatus.PENDING },
    });
  }));
  for (const item of applicationsArray) {
    const params = {
      TableName: getTableName(APPLICATIONS),
      Item: item,
    };
    await dynamoDbClient.send(new PutItemCommand(params));
  }
}

export async function saveDaeApps(env, applications, branchExistence, defaultBranch) {
  await saveApps(applications, branchExistence, defaultBranch, env, 'dae');
}

export async function saveDdeApps(env, applications, branchExistence, defaultBranch) {
  await saveApps(applications, branchExistence, defaultBranch, env, 'dde');
}

export async function updateApps(env, type, applications, branchExistence, defaultBranch) {
  const existingApplications = await getApplicationsByEnvAndType(env, type);

  for (const existingApplication of existingApplications) {
    const application = applications.find((app) => app.name === existingApplication.Name.S);
    if (application) {
      logger.info(`Updating application ${existingApplication.Key.S}`);
      let deployBranch = 'master';
      if (branchExistence.find((item) => item.name === application.name)?.exists === true) {
        deployBranch = defaultBranch;
      }
      let { branchOverride } = application;
      if (branchOverride === undefined || branchOverride === null) {
        branchOverride = '';
      }
      logger.info(`deployBranch ${deployBranch}`);
      logger.info(`application.branchOverride ${application.branchOverride}`);
      if (branchOverride !== '') {
        deployBranch = application.branchOverride;
      }
      const build = await findBuildByKey(`${application.name}-${deployBranch}`);
      logger.info(build);
      const params = {
        TableName: getTableName(APPLICATIONS),
        Key: {
          Key: { S: existingApplication.Key.S },
        },
        UpdateExpression: 'SET #DeployBranch = :DeployBranch, #BranchOverride = :BranchOverride, #Pods = :Pods, #Sha = :Sha,  #BucketKey = :BucketKey, #Status = :Status',
        ExpressionAttributeNames: {
          '#DeployBranch': 'DeployBranch',
          '#BranchOverride': 'BranchOverride',
          '#Pods': 'Pods',
          '#Sha': 'Sha',
          '#BucketKey': 'BucketKey',
          '#Status': 'Status',
        },
        ExpressionAttributeValues: {
          ':DeployBranch': { S: deployBranch },
          ':BranchOverride': { S: branchOverride },
          ':Pods': { S: application.pods?.toString() ?? '' },
          ':Sha': { S: build?.Sha?.S ?? '' },
          ':BucketKey': { S: build?.BucketKey?.S ?? '' },
          ':Status': { S: application.status ?? existingApplication.Status.S },
        },
      };
      await dynamoDbClient.send(new UpdateItemCommand(params));
    }
  }
}

async function getApplicationsByEnv(env) {
  const input = {
    TableName: getTableName(APPLICATIONS),
    IndexName: 'EnvIndex',
    KeyConditionExpression: '#env = :value',
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: {
      ':value': { S: env },
    },
  };
  logger.info(`getApplicationsByEnv ${env}`, { input });
  return (await dynamoDbClient.send(new QueryCommand(input))).Items;
}

export async function getDaeApplicationsByEnv(env) {
  const items = await getApplicationsByEnv(env);
  return items.filter((item) => item.Type.S === ApplicationType.DAE);
}

export async function getDdeApplicationsByEnv(env) {
  const items = await getApplicationsByEnv(env);
  return items.filter((item) => item.Type.S === ApplicationType.DDE);
}

export async function getApplicationsByEnvAndType(env, type) {
  const items = await getApplicationsByEnv(env);
  return items.filter((item) => item.Type.S === type);
}

export async function getApplicationByKey(key) {
  const input = {
    TableName: getTableName(APPLICATIONS),
    KeyConditionExpression: '#key = :value',
    ExpressionAttributeNames: {
      '#key': 'Key',
    },
    ExpressionAttributeValues: {
      ':value': { S: key },
    },
  };
  logger.info(`getApplicationByEnv ${key}`, { input });

  return (await dynamoDbClient.send(new QueryCommand(input))).Items;
}

export async function getAppsByEnvironments(environments) {
  const expressionAttributeValues = {};
  const filterExpressions = [];

  environments.forEach((status, index) => {
    const key = `:env${index + 1}`;
    expressionAttributeValues[key] = { S: status };
    filterExpressions.push(`#env = ${key}`);
  });

  const input = {
    TableName: getTableName(APPLICATIONS),
    FilterExpression: filterExpressions.join(' OR '),
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: expressionAttributeValues,
  };
  logger.info('getAppsByEnvironments', { input });
  return (await dynamoDbClient.send(new ScanCommand(input))).Items;
}

export function formatApplications(applications) {
  return applications?.map((application) => formatApplication(application));
}

export function formatApplication(application) {
  const {
    Key,
    Name,
    DeployBranch,
    BranchOverride,
    Deployed,
    UserName,
    BucketKey,
    BuildVersion,
    TestResults,
    Pipeline,
    Sha,
    Status,
    WarningLevel,
    WarningMessage,
    Type,
    Platform,
    Pods,
    HealthCheckResult,
    HealthCheckTime,
  } = application;

  return {
    key: Key?.S,
    name: Name?.S,
    deployBranch: DeployBranch?.S,
    userName: UserName?.S,
    branchOverride: BranchOverride?.S,
    deployed: Deployed?.S,
    bucketKey: BucketKey?.S,
    buildVersion: BuildVersion?.S,
    pipeline: Pipeline?.S,
    sha: Sha?.S,
    status: Status?.S,
    warningLevel: WarningLevel?.S,
    warningMessage: WarningMessage?.S,
    type: Type?.S,
    platform: Platform?.S,
    pods: Pods?.S,
    healthCheckResult: HealthCheckResult?.S,
    healthCheckTime: HealthCheckTime?.S,
    ...formatTestReport(TestResults?.S),
  };
}

export async function updateWarningInfoForApplication(key, warningLevel, warningMessage) {
  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #WarningLevel = :WarningLevel, #WarningMessage = :WarningMessage',
    ExpressionAttributeNames: {
      '#WarningLevel': 'WarningLevel',
      '#WarningMessage': 'WarningMessage',
    },
    ExpressionAttributeValues: {
      ':WarningLevel': { S: warningLevel.toString() },
      ':WarningMessage': { S: warningMessage.toString() },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function deleteApplicationsByAppEnv(env) {
  const recordsToDelete = await getDaeApplicationsByEnv(env);
  const promises = recordsToDelete.map((record) => {
    const params = {
      TableName: getTableName(APPLICATIONS),
      Key: {
        Key: { S: record.Key.S },
      },
    };
    return dynamoDbClient.send(new DeleteItemCommand(params));
  });
  return Promise.all(promises);
}

export async function updateApplicationDeployStatusAndUserName(key, status, userName) {
  logger.info(`updateApplicationDeployStatus ${key} ${status}`);
  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #Status = :Status, #UserName = :UserName',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#UserName': 'UserName',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
      ':UserName': { S: userName },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateApplicationDeployStatus(key, status) {
  logger.info(`updateApplicationDeployStatus ${key} ${status}`);
  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateApplicationDeployed(key) {
  logger.info(`updateApplicationDeployStatus ${key}}`);
  const currentTimeMillis = Date.now().toString();

  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #Deployed = :Deployed, #Status = :Status',
    ExpressionAttributeNames: {
      '#Deployed': 'Deployed',
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Deployed': { S: currentTimeMillis },
      ':Status': { S: PipelineStatus.DEPLOYED },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateTestResultsForApplication(key, testResults) {
  logger.info(`updateTestResultsForApplication ${key}`);
  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #TestResults = :TestResults',
    ExpressionAttributeNames: {
      '#TestResults': 'TestResults',
    },
    ExpressionAttributeValues: {
      ':TestResults': { S: JSON.stringify(testResults) },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateApplicationHealthCheck(key, result) {
  logger.info(`updateApplicationHealthCheck ${key}}`);
  const currentTimeMillis = Date.now().toString();

  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET  #HealthCheckResult = :HealthCheckResult, #HealthCheckTime = :HealthCheckTime',
    ExpressionAttributeNames: {
      '#HealthCheckResult': 'HealthCheckResult',
      '#HealthCheckTime': 'HealthCheckTime',
    },
    ExpressionAttributeValues: {
      ':HealthCheckResult': { S: result },
      ':HealthCheckTime': { S: currentTimeMillis },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}
