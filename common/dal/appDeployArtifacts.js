import { CopyObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { DEPLOY_BUCKET_NAME, REGION } from '../util/const.js';

export async function copyFile(from, toKey) {
  const s3 = new S3Client({ region: REGION });
  console.info(`Copying artifact from ${from} to ${toKey} in bucket ${DEPLOY_BUCKET_NAME}`);
  const response = await s3.send(
    new CopyObjectCommand({
      Bucket: DEPLOY_BUCKET_NAME,
      CopySource: `${DEPLOY_BUCKET_NAME}/${from}`,
      Key: toKey,
    }),
  );
  console.log('Finished copying artifact, status: ', response.$metadata.httpStatusCode);
  const success = response.$metadata.httpStatusCode === 200;
  if (!success) {
    console.error('Failed to copy build artifact', response.$metadata);
  }
  return success;
}
