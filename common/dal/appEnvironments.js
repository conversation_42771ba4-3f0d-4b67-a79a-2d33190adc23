import {
  DeleteItemCommand,
  PutI<PERSON>Command,
  Query<PERSON>ommand,
  Scan<PERSON>ommand,
  UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { APP_ENVIRONMENTS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getAllRecords } from './common.js';
import { formatApplications, getAppsByEnvironments } from './applications.js';
import {
  INDEXING, PROVISIONING, STABLE, TEARING_DOWN, UPDATING,
} from '../enums/environmentStatus.js';
import { addCloudWatchLogs, formatLogs } from './dataEnvironments.js';
import { getTableName } from './getResourceName.js';
import { formatTestReport } from '../service/testReports.js';

export async function getAppEnvironmentsByFilter(filter) {
  logger.info('getting app environments by filter', { filter });
  const { statuses, name, stage } = filter;
  if (statuses.length === 0 && name === '') {
    return getAllAppEnvironments();
  }
  const expressionAttributeValues = {};
  const expressionAttributeNames = {};
  let statusFilterExpression = '';
  let nameFilterExpression = '';
  let stageFilterExpression = '';

  if (statuses.length > 0) {
    expressionAttributeNames['#status'] = 'Status';
    const statusExpressions = statuses.map((status, index) => {
      const key = `:status${index + 1}`;
      expressionAttributeValues[key] = { S: status };
      return `#status = ${key}`;
    });
    statusFilterExpression = `(${statusExpressions.join(' OR ')})`;
  }

  if (name !== '') {
    logger.info('filtering by name', { name });
    expressionAttributeNames['#name'] = 'Name';
    expressionAttributeValues[':name'] = { S: name };
    nameFilterExpression = 'contains(#name, :name)';
  }
  if (stage !== '') {
    logger.info('filtering by stage', { stage });
    expressionAttributeNames['#stage'] = 'Stage';
    expressionAttributeValues[':stage'] = { S: stage };
    stageFilterExpression = 'contains(#stage, :stage)';
  }

  const combinedFilterExpression = [statusFilterExpression, nameFilterExpression, stageFilterExpression].filter(Boolean).join(' AND ');

  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    FilterExpression: combinedFilterExpression,
    ExpressionAttributeNames: expressionAttributeNames,
    ExpressionAttributeValues: expressionAttributeValues,
  };
  logger.info('params', { params });

  return (await dynamoDbClient.send(new ScanCommand(params))).Items;
}

export async function getAppEnvironmentsByStatus(statuses) {
  const expressionAttributeValues = {};
  const filterExpressions = [];

  statuses.forEach((status, index) => {
    const key = `:status${index + 1}`;
    expressionAttributeValues[key] = { S: status };
    filterExpressions.push(`#status = ${key}`);
  });

  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    FilterExpression: filterExpressions.join(' OR '),
    ExpressionAttributeNames: {
      '#status': 'Status',
    },
    ExpressionAttributeValues: expressionAttributeValues,
  };
  return (await dynamoDbClient.send(new ScanCommand(params))).Items;
}

export async function getAppEnvironmentByName(name) {
  const input = {
    TableName: getTableName(APP_ENVIRONMENTS),
    KeyConditionExpression: '#name = :value',
    ExpressionAttributeNames: {
      '#name': 'Name',
    },
    ExpressionAttributeValues: {
      ':value': { S: name },
    },
  };
  logger.info(`getting app environment by name ${name}`, { input });
  return (await dynamoDbClient.send(new QueryCommand(input))).Items;
}

export async function getAppEnvironmentsByDataEnv(dataEnv) {
  const queryCommandOutput = await dynamoDbClient.send(new QueryCommand({
    IndexName: 'DataEnvIndex',
    TableName: getTableName(APP_ENVIRONMENTS),
    KeyConditionExpression: '#DataEnv = :value',
    ExpressionAttributeNames: {
      '#DataEnv': 'DataEnv',
    },
    ExpressionAttributeValues: {
      ':value': { S: dataEnv },
    },
  }));
  return queryCommandOutput.Items;
}

export async function getStableAppEnvironmentByName(name) {
  return (await dynamoDbClient.send(new ScanCommand({
    TableName: getTableName(APP_ENVIRONMENTS),
    FilterExpression: '#name = :name AND #status = :status',
    ExpressionAttributeNames: {
      '#name': 'Name',
      '#status': 'Status',
    },
    ExpressionAttributeValues: {
      ':name': { S: name },
      ':status': { S: STABLE },
    },
  }))).Items;
}

export async function getAllAppEnvironments() {
  return getAllRecords(getTableName(APP_ENVIRONMENTS));
}

export async function createNewAppEnvironment(appEnvPayload) {
  const {
    appEnv,
    dataEnv,
    stage,
    defaultBranch,
    contextConfig,
    awsAccount,
    locked,
    includeQivs,
    includeQivsExternal,
    costCentre,
    stacks,
    logs,
    sleepTags,
  } = appEnvPayload;
  const logsArray = logs
    .map((item) => ({
      M: {
        Task: { S: item.task },
        Url: { S: item.url },
      },
    }));
  const newItem = {
    Name: { S: appEnv },
    DataEnv: { S: dataEnv },
    Stage: { S: stage },
    DefaultBranch: { S: defaultBranch },
    ContextConfig: { S: contextConfig },
    AwsAccount: { S: awsAccount },
    Locked: { BOOL: locked },
    IncludeQivsExternal: { BOOL: includeQivsExternal },
    IncludeQivs: { BOOL: includeQivs },
    SleepTags: { BOOL: sleepTags },
    CostCentre: { S: costCentre },
    Created: { S: Date.now().toString() },
    Status: { S: PROVISIONING },
    Stacks: { L: stacks.map((stack) => ({ S: stack })) },
    Logs: { L: logsArray },
  };
  logger.info('creating new app environment', { newItem });
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Item: newItem,
  };
  return dynamoDbClient.send(new PutItemCommand(params));
}

export async function updateNewAppEnvironment(appEnvPayload) {
  const {
    appEnv, dataEnv, defaultBranch, contextConfig, awsAccount, locked, costCentre, logs, sleepTags,
  } = appEnvPayload;
  logger.info('updating new app environment', {
    appEnvPayload,
  });
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: appEnv },
    },
    UpdateExpression: 'SET #DataEnv = :DataEnv, #DefaultBranch = :DefaultBranch, #ContextConfig = :ContextConfig, #AwsAccount = :AwsAccount, #Locked = :Locked, #CostCentre = :CostCentre, #Status = :Status, #Logs = :Logs, #SleepTags = :SleepTags',
    ExpressionAttributeNames: {
      '#DataEnv': 'DataEnv',
      '#DefaultBranch': 'DefaultBranch',
      '#ContextConfig': 'ContextConfig',
      '#AwsAccount': 'AwsAccount',
      '#Locked': 'Locked',
      '#CostCentre': 'CostCentre',
      '#Status': 'Status',
      '#Logs': 'Logs',
      '#SleepTags': 'SleepTags',
    },
    ExpressionAttributeValues: {
      ':DataEnv': { S: dataEnv },
      ':DefaultBranch': { S: defaultBranch },
      ':ContextConfig': { S: contextConfig },
      ':AwsAccount': { S: awsAccount },
      ':Locked': { BOOL: locked },
      ':CostCentre': { S: costCentre },
      ':Status': { S: UPDATING },
      ':Logs': logs,
      ':SleepTags': { BOOL: sleepTags },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function getAppsList(existingAppEnvironments, type) {
  if (existingAppEnvironments.length === 0) {
    return [];
  }
  const environments = existingAppEnvironments.map((item) => item.Name?.S);
  logger.info(`getting applications for ${environments}`);
  const compareFn = (a, b) => a.Platform?.S.localeCompare(b.Platform?.S);
  return (await getAppsByEnvironments(environments)).filter((item) => item.Type?.S === type).sort(compareFn);
}

export async function formatAppEnvironments(existingAppEnvironments, applicationsList = []) {
  return existingAppEnvironments.map((item) => {
    const {
      Name,
      Stage,
      DataEnv,
      DefaultBranch,
      TestResults,
      Locked,
      IncludeQivs,
      SleepTags,
      IncludeQivsExternal,
      CostCentre,
      Stacks,
      AwsAccount,
      Created,
      Status,
      ContextConfig,
      WarningLevel,
      WarningMessage,
      Logs,
      HealthCheckResult,
    } = item;

    const applications = applicationsList.filter((application) => application.Env.S === Name.S);
    return {
      ...formatTestReport(TestResults?.S),
      name: Name?.S,
      stage: Stage?.S,
      dataEnv: DataEnv?.S,
      defaultBranch: DefaultBranch?.S,
      locked: Locked?.BOOL,
      includeQivs: IncludeQivs?.BOOL,
      sleepTags: SleepTags?.BOOL,
      includeQivsExternal: IncludeQivsExternal?.BOOL,
      costCentre: CostCentre?.S,
      stacks: Stacks?.L.map((stack) => stack.S),
      awsAccount: AwsAccount?.S,
      created: Created?.S,
      status: Status?.S,
      contextConfig: ContextConfig?.S,
      warningLevel: WarningLevel?.S,
      warningMessage: WarningMessage?.S,
      applications: formatApplications(applications),
      logs: formatLogs(Logs),
      healthCheckResult: HealthCheckResult?.S,
    };
  });
}

export async function deleteAppEnvironment(name) {
  const params = {
    TableName: APP_ENVIRONMENTS,
    Key: {
      Name: { S: name },
    },
  };
  return dynamoDbClient.send(new DeleteItemCommand(params));
}

export async function deleteAppEnvironmentByName(name, logs) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status,#Logs = :Logs',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#Logs': 'Logs',
    },
    ExpressionAttributeValues: {
      ':Status': { S: TEARING_DOWN },
      ':Logs': logs,
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateAppEnvironmentStatus(name, status) {
  logger.info('updating app environment status', { name, status });
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateAppEnvironmentDefaultJsonConfig(name, defaultContextConfig) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #DefaultContextConfig = :DefaultContextConfig',
    ExpressionAttributeNames: {
      '#DefaultContextConfig': 'DefaultContextConfig',
    },
    ExpressionAttributeValues: {
      ':DefaultContextConfig': { S: defaultContextConfig },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateAppEnvironmentTfDeterminedConfig(name, config) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #TfDeterminedConfig = :TfDeterminedConfig',
    ExpressionAttributeNames: {
      '#TfDeterminedConfig': 'TfDeterminedConfig',
    },
    ExpressionAttributeValues: {
      ':TfDeterminedConfig': { S: JSON.stringify(config, null, 4) },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateAppEnvironmentJsonConfig(name, contextConfig) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #ContextConfig = :ContextConfig',
    ExpressionAttributeNames: {
      '#ContextConfig': 'ContextConfig',
    },
    ExpressionAttributeValues: {
      ':ContextConfig': { S: JSON.stringify(contextConfig, null, 4) },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateWarningInfoForAppEnvironment(key, warningLevel, warningMessage) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: key },
    },
    UpdateExpression: 'SET  #WarningLevel = :WarningLevel, #WarningMessage = :WarningMessage',
    ExpressionAttributeNames: {
      '#WarningLevel': 'WarningLevel',
      '#WarningMessage': 'WarningMessage',
    },
    ExpressionAttributeValues: {
      ':WarningLevel': { S: warningLevel.toString() },
      ':WarningMessage': { S: warningMessage.toString() },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateLogsForAppEnvironment(name, logs) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Logs = :Logs',
    ExpressionAttributeNames: {
      '#Logs': 'Logs',
    },
    ExpressionAttributeValues: {
      ':Logs': logs,
    },
  };
  logger.info('updating logs for app environment', { name, logs });
  return (dynamoDbClient.send(new UpdateItemCommand(params)));
}

export async function addLogsToAppEnv(appEnv, task, url) {
  logger.info(`adding logs to app env ${appEnv}`);
  const appEnvironments = await getAppEnvironmentByName(appEnv);
  const { Logs } = appEnvironments[0];
  logger.info('Logs', { Logs });
  addCloudWatchLogs([{ task, url }], Logs);
  await updateLogsForAppEnvironment(appEnv, Logs);
}

export async function updateTestResultsForAppEnvironment(appEnv, testResults) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: appEnv },
    },
    UpdateExpression: 'SET  #TestResults = :TestResults',
    ExpressionAttributeNames: {
      '#TestResults': 'TestResults',
    },
    ExpressionAttributeValues: {
      ':TestResults': { S: JSON.stringify(testResults) },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function updateSleepTagsForAppEnvironment(appEnv, sleepTags) {
  logger.info('updating sleep tags for app environment', { appEnv, sleepTags });
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: appEnv },
    },
    UpdateExpression: 'SET  #SleepTags = :SleepTags',
    ExpressionAttributeNames: {
      '#SleepTags': 'SleepTags',
    },
    ExpressionAttributeValues: {
      ':SleepTags': { BOOL: sleepTags },
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export function getStacks(appEnvironment) {
  return appEnvironment.Stacks.L.map((it) => it.S);
}

export async function reindexAppEnvironmentByName(name, logs) {
  const params = {
    TableName: getTableName(APP_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status,#Logs = :Logs',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#Logs': 'Logs',
    },
    ExpressionAttributeValues: {
      ':Status': { S: INDEXING },
      ':Logs': logs,
    },
  };

  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}
