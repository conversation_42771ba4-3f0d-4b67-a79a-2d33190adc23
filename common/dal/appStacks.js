import { getAllRecords } from './common.js';
import { getTableName } from './getResourceName.js';
import { APP_STACKS } from '../util/const.js';
import { Platform } from '../enums/platform.js';
import { ApplicationStack } from '../enums/applicationStack.js';

export async function isDaeApplication(appName) {
  const appStacks = await getAllAppStacks();
  const appStack = appStacks.find((item) => item.Pipeline.S === appName);
  if (!appStack) {
    return false;
  }
  const stacks = appStack.Stacks.L.map((stack) => stack.S);
  return !stacks.includes(ApplicationStack.DDE);
}

export async function getEnabledAppStacks() {
  return (await getAllAppStacks()).filter((appStack) => appStack.Enabled?.BOOL);
}

export async function getDefaultDeployAppStacks() {
  return (await getAllAppStacks()).filter((appStack) => appStack.DeployDefault?.BOOL);
}

export async function getAllAppStacks() {
  return getAllRecords(getTableName(APP_STACKS));
}

export async function filterK8sBuilds(deploymentBuilds) {
  const appStacks = await getAllAppStacks();
  const builds = [];

  deploymentBuilds.forEach((deploymentBuild) => {
    const name = deploymentBuild.Application.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform === Platform.K8S) {
      builds.push(deploymentBuild);
    }
  });
  return builds;
}
export async function filterEcsBuilds(deploymentBuilds) {
  const appStacks = await getAllAppStacks();
  const builds = [];

  deploymentBuilds.forEach((deploymentBuild) => {
    const name = deploymentBuild.Application.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform === Platform.ECS) {
      builds.push(deploymentBuild);
    }
  });
  return builds;
}
export async function filterLambdaApplications(applications) {
  const appStacks = await getAllAppStacks();
  const lambdaApplications = [];

  applications.forEach((application) => {
    const name = application.Name.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform === Platform.LAMBDA) {
      lambdaApplications.push(application);
    }
  });
  return lambdaApplications;
}
export async function removeEcsBuilds(deploymentBuilds) {
  const appStacks = await getAllAppStacks();
  const k8sBuilds = [];

  deploymentBuilds.forEach((deploymentBuild) => {
    const name = deploymentBuild.Application.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform !== Platform.ECS) {
      k8sBuilds.push(deploymentBuild);
    }
  });
  return k8sBuilds;
}

export async function removeEcsApplications(applications) {
  const appStacks = await getAllAppStacks();
  const ecsApplications = [];

  applications.forEach((application) => {
    const name = application.Name.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform !== Platform.ECS) {
      ecsApplications.push(application);
    }
  });
  return ecsApplications;
}

export async function groupApplicationByPlatform(existingApplications) {
  const appStacks = await getAllAppStacks();
  const lambdaApplications = [];
  const ecsApplications = [];
  const k8sApplications = [];
  existingApplications.forEach((application) => {
    const name = application.Name.S;
    const appStack = appStacks.find((item) => item.Pipeline.S === name);
    const platform = appStack?.Platform.S;
    if (platform === Platform.LAMBDA) {
      lambdaApplications.push(application);
    } else if (platform === Platform.ECS) {
      ecsApplications.push(application);
    } else if (platform === Platform.K8S) {
      k8sApplications.push(application);
    }
  });
  return {
    lambdaApplications,
    ecsApplications,
    k8sApplications,
  };
}

export async function getPlatform(applicationName) {
  const appStacks = await getAllAppStacks();
  const appStack = appStacks.find((item) => item.Pipeline.S === applicationName);
  return appStack?.Platform.S;
}

export async function getBuildPipelineNames() {
  const appStacks = await getAllAppStacks();
  return appStacks.map((appStack) => appStack.Pipeline.S);
}

export function formatAppStacks(appStacks) {
  return appStacks.map((item) => {
    const {
      Name,
      Pipeline,
      Repo,
      Platform,
      CodePath,
      TestPath,
      DeployDefault,
      Selectable,
      Enabled,
      Stacks,
    } = item;
    return {
      name: Name.S,
      pipeline: Pipeline.S,
      repo: Repo.S,
      platform: Platform.S,
      codePath: CodePath.S,
      testPath: TestPath.S,
      deployDefault: DeployDefault.BOOL,
      selectable: Selectable.BOOL,
      enabled: Enabled.BOOL,
      stacks: Stacks.L.map((stack) => stack.S).join(','),
    };
  });
}

export async function getApplicationsDependencies() {
  const appStacks = await getAllAppStacks();
  const k8sApplications = appStacks.filter((appStack) => appStack.Platform.S === Platform.K8S);
  const result = {};

  k8sApplications.forEach((appStack) => {
    const name = appStack.Name.S;
    // Use empty array as default if DependsOn is missing
    const dependsOn = appStack.DependsOn ? appStack.DependsOn.L.map((stack) => stack.S) : [];
    result[name] = { dependsOn };
  });

  return result;
}
