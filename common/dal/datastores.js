import {
  Delete<PERSON><PERSON><PERSON>ommand,
  <PERSON><PERSON><PERSON><PERSON>ommand,
  Query<PERSON>ommand,
  <PERSON>an<PERSON>ommand,
  UpdateItemCommand,
} from '@aws-sdk/client-dynamodb';
import { DATA_ENVIRONMENTS, DATASTORES } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import {
  DELETED, ERROR, PENDING, PROVISIONING, STABLE, TEARING_DOWN,
} from '../enums/environmentStatus.js';
import { getTableName } from './getResourceName.js';

export async function saveDataStores(env, dataStores) {
  for (const dataStore of dataStores) {
    const item = {
      Key: { S: `${env}-${dataStore.Type.S}-${dataStore.Name.S}` },
      Env: { S: env },
      Type: { S: dataStore.Type.S },
      Name: { S: dataStore.Name.S },
      Status: { S: PENDING },
      StartTime: { N: '0' },
      EndTime: { N: '0' },
      TeardownStartTime: { N: '0' },
      TeardownEndTime: { N: '0' },
      Message: { S: '' },
      PercentComplete: { S: '0' },
    };
    logger.info('Saving data store', { item });
    const params = {
      TableName: getTableName(DATASTORES),
      Item: item,
    };
    await dynamoDbClient.send(new PutItemCommand(params));
  }
}

export async function getDataStoresByEnv(env) {
  logger.info(`getDataStoresByEnv ${env}`);
  return (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(DATASTORES),
    IndexName: 'EnvIndex',
    KeyConditionExpression: '#env = :value',
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: {
      ':value': { S: env },
    },
  }))).Items;
}

export async function getDataStoreByEnvironments(environments) {
  const expressionAttributeValues = {};
  const filterExpressions = [];

  environments.forEach((status, index) => {
    const key = `:env${index + 1}`;
    expressionAttributeValues[key] = { S: status };
    filterExpressions.push(`#env = ${key}`);
  });

  const input = {
    TableName: getTableName(DATASTORES),
    FilterExpression: filterExpressions.join(' OR '),
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: expressionAttributeValues,
  };
  logger.info('getDataStoreByEnvironments', { environments, input });
  return (await dynamoDbClient.send(new ScanCommand(input))).Items;
}

export async function updateDataEnvironmentStatus(name, status) {
  const params = {
    TableName: getTableName(DATA_ENVIRONMENTS),
    Key: {
      Name: { S: name },
    },
    UpdateExpression: 'SET #Status = :Status',
    ExpressionAttributeNames: {
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':Status': { S: status },
    },
  };
  const command = new UpdateItemCommand(params);
  return dynamoDbClient.send(command);
}

export async function deleteDataStores(dataStores) {
  const currentTimeMillis = Date.now().toString();
  for (const item of dataStores) {
    const params = {
      TableName: getTableName(DATASTORES),
      Key: {
        Key: { S: item.Key.S },
      },
      UpdateExpression: 'SET #Status = :Status, #TeardownStartTime = :TeardownStartTime, #LastUpdatedTime = :LastUpdatedTime',
      ExpressionAttributeNames: {
        '#Status': 'Status',
        '#TeardownStartTime': 'TeardownStartTime',
        '#LastUpdatedTime': 'LastUpdatedTime',
      },
      ExpressionAttributeValues: {
        ':Status': { S: TEARING_DOWN },
        ':TeardownStartTime': { N: currentTimeMillis },
        ':LastUpdatedTime': { N: currentTimeMillis },
      },
    };
    const command = new UpdateItemCommand(params);
    await dynamoDbClient.send(command);
  }
}

export async function getDataStoresByEnvType(env, type) {
  return (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(DATASTORES),
    KeyConditionExpression: '#key = :value',
    ExpressionAttributeNames: {
      '#key': 'Key',
    },
    ExpressionAttributeValues: {
      ':value': { S: `${env}-${type}-${name}` },
    },
  }))).Items;
}

export async function getDataStoresByKey(env, type, name) {
  return (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(DATASTORES),
    KeyConditionExpression: '#key = :value',
    ExpressionAttributeNames: {
      '#key': 'Key',
    },
    ExpressionAttributeValues: {
      ':value': { S: `${env}-${type}-${name}` },
    },
  }))).Items;
}

export async function updateDatastoreLastUpdatedTime(key) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #LastUpdatedTime = :LastUpdatedTime',
    ExpressionAttributeNames: {
      '#LastUpdatedTime': 'LastUpdatedTime',
    },
    ExpressionAttributeValues: {
      ':LastUpdatedTime': { N: currentTimeMillis },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreStartTime(key) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #StartTime = :StartTime, #Status = :Status',
    ExpressionAttributeNames: {
      '#StartTime': 'StartTime',
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':StartTime': { N: currentTimeMillis },
      ':Status': { S: PROVISIONING },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreLastUpdatedTimeAndMessage(key, message) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #LastUpdatedTime = :LastUpdatedTime, #Message = :Message',
    ExpressionAttributeNames: {
      '#LastUpdatedTime': 'LastUpdatedTime',
      '#Message': 'Message',
    },
    ExpressionAttributeValues: {
      ':LastUpdatedTime': { N: currentTimeMillis },
      ':Message': { S: message },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreLastUpdatedTimeAndPercentAndMessage(key, message, percentComplete) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #LastUpdatedTime = :LastUpdatedTime, #PercentComplete = :PercentComplete, #Message = :Message',
    ExpressionAttributeNames: {
      '#PercentComplete': 'PercentComplete',
      '#LastUpdatedTime': 'LastUpdatedTime',
      '#Message': 'Message',
    },
    ExpressionAttributeValues: {
      ':PercentComplete': { S: percentComplete },
      ':LastUpdatedTime': { N: currentTimeMillis },
      ':Message': { S: message },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreToStable(key, message) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #LastUpdatedTime = :LastUpdatedTime, #PercentComplete = :PercentComplete, #Message = :Message,#Status = :Status, #EndTime = :EndTime',
    ExpressionAttributeNames: {
      '#PercentComplete': 'PercentComplete',
      '#LastUpdatedTime': 'LastUpdatedTime',
      '#Message': 'Message',
      '#Status': 'Status',
      '#EndTime': 'EndTime',
    },
    ExpressionAttributeValues: {
      ':PercentComplete': { S: '100' },
      ':LastUpdatedTime': { N: currentTimeMillis },
      ':Message': { S: message },
      ':Status': { S: STABLE },
      ':EndTime': { N: currentTimeMillis },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreToDelete(key) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #Status = :Status, #TeardownEndTime = :TeardownEndTime',
    ExpressionAttributeNames: {
      '#Status': 'Status',
      '#TeardownEndTime': 'TeardownEndTime',
    },
    ExpressionAttributeValues: {
      ':Status': { S: DELETED },
      ':TeardownEndTime': { N: currentTimeMillis },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function updateDatastoreToError(key, message) {
  const currentTimeMillis = Date.now().toString();
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #LastUpdatedTime = :LastUpdatedTime, #Message = :Message,#Status = :Status',
    ExpressionAttributeNames: {
      '#LastUpdatedTime': 'LastUpdatedTime',
      '#Message': 'Message',
      '#Status': 'Status',
    },
    ExpressionAttributeValues: {
      ':LastUpdatedTime': { N: currentTimeMillis },
      ':Message': { S: message },
      ':Status': { S: ERROR },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}

export async function deleteDatastoresByEnv(env) {
  const records = await getDataStoresByEnv(env);
  const recordsToDelete = records.filter((record) => record.Env.S === env);
  const promises = recordsToDelete.map((record) => {
    const params = {
      TableName: getTableName(DATASTORES),
      Key: {
        Key: { S: record.Key.S },
      },
    };
    return dynamoDbClient.send(new DeleteItemCommand(params));
  });
  return Promise.all(promises);
}

export async function updateDatastoreSha(key, sha) {
  const params = {
    TableName: getTableName(DATASTORES),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #Sha = :Sha',
    ExpressionAttributeNames: {
      '#Sha': 'Sha',
    },
    ExpressionAttributeValues: {
      ':Sha': { S: sha },
    },
  };
  return dynamoDbClient.send(new UpdateItemCommand(params));
}
