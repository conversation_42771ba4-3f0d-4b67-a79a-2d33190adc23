import {
  DeleteItemCommand, PutItemCommand, QueryCommand,
} from '@aws-sdk/client-dynamodb';

import { TF_SCRIPTS } from '../util/const.js';
import { dynamoDbClient } from '../util/dynamodb.js';
import { getTableName } from './getResourceName.js';

export async function createTfScript(env, path, text) {
  const params = {
    TableName: getTableName(TF_SCRIPTS),
    Item: {
      Path: { S: path },
      Env: { S: env },
      Text: { S: text },
    },
  };
  return dynamoDbClient.send(new PutItemCommand(params));
}

export async function getTfScriptByName(env) {
  return (await dynamoDbClient.send(new QueryCommand({
    TableName: getTableName(TF_SCRIPTS),
    IndexName: 'EnvIndex',
    KeyConditionExpression: '#env = :value',
    ExpressionAttributeNames: {
      '#env': 'Env',
    },
    ExpressionAttributeValues: {
      ':value': { S: env },
    },
  }))).Items;
}

export async function deleteTfScriptsByEnv(env) {
  const recordsToDelete = await getTfScriptByName(env);
  const promises = recordsToDelete.map((record) => {
    const params = {
      TableName: getTableName(TF_SCRIPTS),
      Key: {
        Path: { S: record.Path.S },
      },
    };
    return dynamoDbClient.send(new DeleteItemCommand(params));
  });
  return Promise.all(promises);
}
