env:
  es2021: true
  node: true
  jest/globals: true
extends:
  - airbnb-base
globals:
  logger: readonly
parserOptions:
  ecmaVersion: latest
  sourceType: module
plugins:
  - "jest"
rules: {
  "import/extensions": [ "error", "never", { "js": "always", "mjs": "always" } ],
  "import/no-extraneous-dependencies": [ "error", { "devDependencies": [ "**/*.test.js" ] } ],
  "max-len": [ 1, 120, 2 ],
  "no-use-before-define": [ "error", { "functions": false } ],
  "no-await-in-loop": "off",
  "no-restricted-syntax": [ "error", "ForInStatement" ],
  "no-param-reassign": [ "error", { "props": false } ],
  "no-plusplus": "off",
}
