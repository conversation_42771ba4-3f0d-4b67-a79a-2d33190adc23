import { BatchGetBuildsCommand, CodeBuildClient, StartBuildCommand } from '@aws-sdk/client-codebuild';
import { REGION } from '../util/const.js';

const client = new CodeBuildClient({ region: REGION });

export async function startBuild(application, branch, envVars) {
  const input = {
    projectName: `${application}-tests`,
    sourceVersion: branch,
    environmentVariablesOverride: envVars,
  };
  logger.info('startBuild command', input);
  const command = new StartBuildCommand(input);

  const result = await client.send(command);
  return result.build.id;
}

export async function getBuildStatus(ids) {
  const params = {
    ids,
  };
  const command = new BatchGetBuildsCommand(params);
  const data = await client.send(command);
  return data.builds;
}
