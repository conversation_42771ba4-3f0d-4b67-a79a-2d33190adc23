import {
  GetApi<PERSON>eysCommand,
  GetRestApisCommand,
  GetUsagePlanCommand,
  GetUsagePlansCommand,
  UpdateUsagePlanCommand,
} from '@aws-sdk/client-api-gateway';
import { CreateApiMappingCommand, GetApiMappingsCommand } from '@aws-sdk/client-apigatewayv2';
import { retryWithDelay } from '../util/retryUtil.js';
import { devAPIGatewayClient, devAPIGatewayV2Client } from '../util/awsConfig.js';
import { getAppEnvironmentsByDataEnv } from '../dal/appEnvironments.js';
import { getDaeApplicationsByEnv } from '../dal/applications.js';
import {
  DEPLOYING, HEALTHY, STABLE, TESTING, UNHEALTHY,
} from '../enums/environmentStatus.js';

function keyExist(result) {
  return result !== null;
}

export async function fetchApiKey(client, name) {
  const command = new GetApiKeysCommand({
    nameQuery: name,
    includeValues: true,
  });
  const response = await client.send(command);

  return response.items.find((item) => item.name === name);
}

export async function getApiKey(name) {
  const apiKey = await fetchApiKey(devAPIGatewayClient, name);
  if (!apiKey) {
    logger.info('API Key not found');
    return null;
  }
  return apiKey.value;
}

export async function getApiKeyByName(name, timeoutSeconds = 180, delaySeconds = 60) {
  const {
    timeoutReached,
    result: apiKeyValue,
  } = await retryWithDelay(() => getApiKey(name), keyExist, timeoutSeconds, delaySeconds);

  if (timeoutReached) {
    logger.error('ERR-CICD-051', `Failed to retrieve API Key ${name} after ${timeoutSeconds} seconds.`);
  }

  return apiKeyValue;
}

export async function addExistingApiToAppEnv(dataEnvName, appEnv, applicationName) {
  let appEnvironments = await getAppEnvironmentsByDataEnv(dataEnvName);
  appEnvironments = appEnvironments.filter((env) => [DEPLOYING, TESTING, STABLE, HEALTHY, UNHEALTHY].includes(env.Status.S));
  if (appEnvironments.length === 0) {
    logger.info('No app environments found');
    return;
  }
  let envWithTargetApplication;

  for (const appEnvironment of appEnvironments) {
    const applications = await getDaeApplicationsByEnv(appEnvironment.Name.S);
    for (const application of applications) {
      if (application.Name.S === applicationName) {
        envWithTargetApplication = appEnvironment.Name.S;
      }
    }
  }

  if (!envWithTargetApplication) {
    logger.info('No app environments found with target application');
    return;
  }
  const usagePlans = await getAllUsagePlans();
  const usagePlan = usagePlans.find((plan) => plan.name === `${appEnv}-dae-LambdaKey-usage-plan`);
  if (!usagePlan) return;

  const api = await findApi(`${applicationName}-${envWithTargetApplication}`);
  if (!api) {
    logger.info(`API not found for application ${applicationName} in environment ${envWithTargetApplication}`);
    return;
  }

  const usagePlanDetails = await devAPIGatewayClient.send(new GetUsagePlanCommand({ usagePlanId: usagePlan.id }));
  const existingApiStages = usagePlanDetails.apiStages?.map((stage) => stage.apiId) || [];

  if (!existingApiStages.includes(api.id)) {
    await devAPIGatewayClient.send(new UpdateUsagePlanCommand({
      usagePlanId: usagePlan.id,
      patchOperations: [{
        op: 'add',
        path: '/apiStages',
        value: `${api.id}:${envWithTargetApplication}`,
      }],
    }));
  }

  await addApiMapping(appEnv, envWithTargetApplication, api.id, applicationName.replace('api-', ''));
}

async function fetchUsagePlans(position = null) {
  const params = position ? { position } : {};
  return devAPIGatewayClient.send(new GetUsagePlansCommand(params));
}

async function getAllUsagePlans() {
  let usagePlans = [];
  let position = null;

  do {
    const response = await fetchUsagePlans(position);
    usagePlans = usagePlans.concat(response.items || []);
    position = response.position || null;
  } while (position);

  return usagePlans;
}

async function addApiMapping(appEnv, envWithTargetApplication, apiId, apiMappingKey) {
  const existingApiMappings = await devAPIGatewayV2Client.send(
    new GetApiMappingsCommand({ DomainName: `${appEnv}.dae.qvapi.co.nz` }),
  );
  const mappingExists = existingApiMappings.Items?.some(
    (item) => item.ApiId === apiId && (item.ApiMappingKey || '') === apiMappingKey,
  );
  if (!mappingExists) {
    logger.info(`Creating API mapping for ${appEnv}, adding ApiMappingKey ${apiMappingKey}`);
    await devAPIGatewayV2Client.send(
      new CreateApiMappingCommand({
        DomainName: `${appEnv}.dae.qvapi.co.nz`,
        ApiId: apiId,
        Stage: envWithTargetApplication,
        ApiMappingKey: apiMappingKey,
      }),
    );
  }
}

async function findApi(apiName) {
  let position;
  do {
    const response = await devAPIGatewayClient.send(new GetRestApisCommand(position ? { position } : {}));
    const api = response.items.find((item) => item.name === apiName);
    if (api) return api;
    position = response.position;
  } while (position);
  return null;
}
