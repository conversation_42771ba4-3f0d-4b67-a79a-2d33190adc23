import { getAppEnvironmentByName } from '../dal/appEnvironments.js';
import { getDataEnvironmentByName } from '../dal/dataEnvironments.js';
import { AWS_ACCOUNTS_MAPPING } from '../util/const.js';
import getConfig from '../config/getConfig.js';

export async function getAppEnvConfig(name, dataEnv, account, includeQivs, update) {
  const queryResults = await getAppEnvironmentByName(name);
  const defaultContextConfig = await getDefaultAppEnvConfig(name, dataEnv, account, includeQivs);
  logger.info('default config', { defaultContextConfig });
  if (queryResults.length === 0) {
    logger.info(`getDefaultAppEnvConfig: ${name} not found`);
    return defaultContextConfig;
  }
  const appEnvironment = queryResults[0];
  let contextConfig = JSON.parse(appEnvironment?.ContextConfig?.S);
  logger.info('getAppEnvConfig update', { update });
  if (update) { // for Edit App Environment
    contextConfig = { ...contextConfig, ...defaultContextConfig };
    logger.info('update default config', { contextConfig });
    return contextConfig;
  }
  return contextConfig;
}

export async function getPodsConfig(applications) {
  return applications.reduce((acc, app) => {
    acc[`${app.Name.S}_PODS`.toUpperCase()] = parseInt(app.Pods.S, 10);
    return acc;
  }, {});
}

export async function getTfDeterminedConfig(daeApiKey, kafkaHost, privateAlbDnsName) {
  const tfDeterminedConfig = {
    MONARCH_SERVICES_URL: `http://${privateAlbDnsName}`,
    'KAFKA-BOOTSTRAP-SERVERS': `${kafkaHost}:9092`,
    API_KEY: daeApiKey,
  };
  logger.info(`tfDeterminedConfig: ${JSON.stringify(tfDeterminedConfig)}`);
  return tfDeterminedConfig;
}

export async function getDefaultAppEnvConfig(appEnv, dataEnv, account, includeQivs) {
  logger.info(`getInitDefaultAppEnvConfig: ${appEnv}, ${dataEnv}, ${account}`);
  const dataEnvironment = (await getDataEnvironmentByName(dataEnv))[0];
  const accountNumber = AWS_ACCOUNTS_MAPPING.get(account);
  logger.info(`accountNumber: ${accountNumber}`);
  const {
    mssqlServer = '',
    monarch_main_db = '',
    monarch_services_username = '',
    monarch_services_password = '',
    qivs_user_username = '',
    qivs_user_password = '',
    qvapihub_host = '',
    qvapihub_db = '',
    qvapihub_user_username = '',
    qvapihub_user_password = '',
    kaba_services_host = '',
    kaba_services_db = '',
    kaba_services_username = '',
    kaba_services_password = '',
    report_generator_host = '',
    report_generator_db = '',
    report_generator_user_username = '',
    report_generator_user_password = '',
    qvcostbuilder_host = '',
    qvcostbuilder_name = '',
    qvcostbuilder_app_user_username = '',
    qvcostbuilder_app_user_password = '',
    qivs_rds_host = '',
    qivs_rds_db = '',
    qivs_rds_user = '',
    qivs_rds_password = '',
    qvms_host = '',
    qvms_db = '',
  } = JSON.parse(dataEnvironment?.SqlConfig?.S ?? '{}');

  const {
    pgIp = '',
    pgDbName = '',
    pgUsername = '',
    pgPassword = '',
    pgMigrateUsername = '',
    pgMigratePassword = '',
    qvconzIp = '',
    qvconzPgDbName = '',
    qvconzPgUsername = '',
    qvconzPgPassword = '',
  } = JSON.parse(dataEnvironment?.PgConfig?.S ?? '{}');
  logger.info(`pgIp: ${pgIp}, pgDbName: ${pgDbName}, qvconzPgDbName: ${qvconzPgDbName}, pgUsername: ${pgUsername}, pgPassword: ${pgPassword}`);
  logger.info(`includeQivs: ${includeQivs}`);
  const qivsWebUi = includeQivs === true ? `https://${appEnv}.qivs.internal.quotablevalue.co.nz` : 'http://devawsinweb01-qivs';
  const qvconzContent = `${appEnv}-daeqvconz-content`;
  const jsonConfig = {
    // static
    APPLICATION_SECRET: 'c50295ca-4c8c-4405-8056-14aec0987d9a',
    GOOGLE_API_KEY: 'AIzaSyCPpfppC19nlwWnxiBGnyz0gb1ruQIfhOM',
    'QVWEB-S3-BUCKET': `https://s3.ap-southeast-2.amazonaws.com/${qvconzContent}/media/reports/`,
    'QVWEB-GENERATE-PROPERTY-HOMEVAL-ID': 'bf874b5f-b0dd-497b-9dc5-2dc006283703',
    'GOOGLE-API-KEY': 'AIzaSyDBOFd5GsFpz2GgcUm_572oR4AikIaAqho',
    AWS_S3_CUSTOM_DOMAIN: `${qvconzContent}`,
    AWS_S3_QVCONZ_BUCKET: `${qvconzContent}/media`,
    POSTGRES_SCHEMA: 'property',
    S3_QV_ORIGINAL: 'qv-property-photos-migration-original',
    S3_QV_RESIZED: 'qv-property-photos-migration-resized',
    S3_CHART_IMAGE_BUCKET: `${qvconzContent}`,
    S3_BUCKET_ACCESS_KEY_ID: '********************',
    S3_BUCKET_ACCESS_KEY_SECRET: 'Mg5hWem2v0j8OgFgec2lPaKnhOTUatb9JoGk62Ep',
    QIVS_WEB_UI: qivsWebUi,
    QIVS_MAPPING_SERVICE_URL: `http://${appEnv}.maps.internal.quotablevalue.co.nz`,
    PLAY_SECURE_SESSION_COOKIE: 'TRUE',
    AWS_PEER_REVIEW_BUCKET: 'peerreviewform-dev',
    AWS_PEER_REVIEW_FORM: 'PeerReviewForm.docx',
    EMAIL_TO: '<EMAIL>',
    EMAIL_FROM: '<EMAIL>',
    EMAIL_FROM_NAME: 'DEV ACC',
    REVIEW_FAILED_RECIPIENT_OVERRIDE: '<EMAIL>',
    EMAIL_SMTP_PORT: '587',
    EMAIL_HOST: 'email-smtp.us-west-2.amazonaws.com',
    EMAIL_USER: '********************',
    EMAIL_PASS: 'Avf4DrFZd6Sghcaw7o2/Rma46+SGJk/QQlqvrq2X+SCP',
    MAPS_URL: `http://${appEnv}.maps.internal.quotablevalue.co.nz`,
    MAPS_PROXY_URL: '/api/maps',
    AUTH0_CLIENT_SECRET: await getConfig('TEST_PROPERTY_SERVICE_CLIENT_SECRET'),
    AUTH0_CLIENT_ID: await getConfig('TEST_PROPERTY_SERVICE_CLIENT_ID'),
    AUTH0_DOMAIN: 'login.test.qvmonarch.co.nz',
    SAMBA_PASSWORD: await getConfig('SAMBA_PASSWORD'),
    CHARGIFY_API_KEY: await getConfig('CHARGIFY_API_KEY'),
    CHARGIFY_SHARED_KEY: await getConfig('CHARGIFY_SHARED_KEY'),
    DJANGO_SECRET_KEY: await getConfig('DJANGO_SECRET_KEY'),
    RECAPTCHA_PRIVATE_KEY: await getConfig('RECAPTCHA_PRIVATE_KEY'),
    SENDGRID_API_KEY: await getConfig('SENDGRID_API_KEY'),
    WINDCAVE_API_KEY: await getConfig('WINDCAVE_API_KEY'),

    // data environment
    DATA_ENV: dataEnv,
    ENV_NAME: appEnv,

    // Postgres
    POSTGRES_HOST: pgIp,
    POSTGRES_USER: pgUsername,
    POSTGRES_PASS: pgPassword,
    POSTGRES_MIGRATION_USER: pgMigrateUsername,
    POSTGRES_MIGRATION_PASS: pgMigratePassword,
    POSTGRES_DB: pgDbName,
    POSTGRES_DB_URL: `postgresql://${pgUsername}:${pgPassword}@${pgIp}/${pgDbName}`,
    QVCONZ_HOST: qvconzIp !== '' ? qvconzIp : pgIp,
    QVCONZ_USERE: qvconzPgUsername !== '' ? qvconzPgUsername : pgUsername,
    QVCONZ_PASS: qvconzPgPassword !== '' ? qvconzPgPassword : pgPassword,
    QVCONZ_DB: qvconzPgDbName,
    MINIMUM_IDLE_SIZE: 5,
    MAXIMUM_POOL_SIZE: 10,
    // MS SQL
    DB_APIHUB_URL: `jdbc:sqlserver://${qvapihub_host || mssqlServer}:1433;databaseName=${qvapihub_db || `${dataEnv}_qv_apihub`};user=${qvapihub_user_username};password=${qvapihub_user_password};selectMethod=cursor`,
    DB_QIVS_URL: `jdbc:sqlserver://${mssqlServer}:1433;databaseName=${monarch_main_db || `${dataEnv}_qvnz`};user=${monarch_services_username};password=${monarch_services_password};selectMethod=cursor`,
    DB_QVNZ_PUBLIC_URL: `jdbc:sqlserver://${mssqlServer}:1433;databaseName=${dataEnv}_qvnz_public;user=${qvapihub_user_username};password=${qvapihub_user_password};selectMethod=cursor`,
    MSSQL_DB_HOST: mssqlServer,

    QIVS_PUBLIC_HOST: qivs_rds_host || mssqlServer,
    QIVS_PUBLIC_DB: qivs_rds_db || `${dataEnv}_qvnz_public`,
    QIVS_PUBLIC_USER: qivs_rds_user || monarch_services_username,
    QIVS_PUBLIC_PASSWORD: qivs_rds_password || monarch_services_password,

    QIVS_MAIN_DB: monarch_main_db || `${dataEnv}_qvnz`,
    QIVS_MAIN_USER: monarch_services_username,
    QIVS_MAIN_PASSWORD: monarch_services_password,

    QIVS_USER: qivs_user_username,
    QIVS_PASSWORD: qivs_user_password,

    APIHUB_HOST: qvapihub_host || mssqlServer,
    APIHUB_DB: qvapihub_db || `${dataEnv}_qv_apihub`,
    APIHUB_USER: qvapihub_user_username,
    APIHUB_PASSWORD: qvapihub_user_password,

    REPORT_HOST: report_generator_host || mssqlServer,
    REPORT_DB: report_generator_db || `${dataEnv}_report_generator`,
    REPORT_USER: report_generator_user_username,
    REPORT_PASSWORD: report_generator_user_password,

    KABA_HOST: kaba_services_host || mssqlServer,
    KABA_DB: kaba_services_db || `${dataEnv}_report_generator`,
    KABA_USER: kaba_services_username,
    KABA_PASSWORD: kaba_services_password,
    CB_DB_HOST: qvcostbuilder_host || mssqlServer,
    CB_DB_NAME: qvcostbuilder_name || `${dataEnv}_qvcostbuilder`,
    CB_DB_USER: qvcostbuilder_app_user_username,
    CB_DB_PASSWORD: qvcostbuilder_app_user_password,

    QVMS_DB: qvms_db || `${dataEnv}_qvnz_mapping`,
    QVMS_DB_HOST: qvms_host || mssqlServer,
    QVMS_USER: monarch_services_username,
    QVMS_PASSWORD: monarch_services_password,

    // Redshift
    REDSHIFT_SERVER: 'kaba-test-redshiftcluster.c2unr0sd7noa.ap-southeast-2.redshift.amazonaws.com',
    REDSHIFT_PORT: '5439',
    REDSHIFT_DATABASE: 'dw_test',
    REDSHIFT_USERNAME: 'qvadmin',
    REDSHIFT_PASSWORD: 'RedshiftM4ster',

    // monarch url
    MONARCH_WEB_URL: `https://${appEnv}.monarch.internal.quotablevalue.co.nz`,
    AUTH0_REDIRECT_URI: `https://${appEnv}.monarch.internal.quotablevalue.co.nz/callback`,
    AUTH0_RETURN_URI: `https://${appEnv}.monarch.internal.quotablevalue.co.nz/property`,

    // public website url
    PUBLIC_WEB_URL: `https://${appEnv}.qvconz.internal.quotablevalue.co.nz`,

    // DAE Lambda
    PUBLIC_REPORTS_API_URL: `https://${appEnv}.dae.qvapi.co.nz/public-reports`,
    PUBLIC_PROPERTY_DETAILS_API_URL: `https://${appEnv}.dae.qvapi.co.nz/public-property-details`,
    RURAL_SALES_LAMBDA_API_URL: `https://${appEnv}.dae.qvapi.co.nz/sale-analysis`,
    SALES_LAMBDA_API_URL: `https://${appEnv}.dae.qvapi.co.nz/sales`,
    MAPS_API_URL: `https://${appEnv}.dae.qvapi.co.nz/maps`,
    PROPERTY_API_URL: `https://${appEnv}.dae.qvapi.co.nz/property`,
    FLOOR_PLAN_MEASURE_API_URL: `https://${appEnv}.dae.qvapi.co.nz/floor-plan`,
    LINZ_SEARCH_API_URL: `https://${appEnv}.dae.qvapi.co.nz/search`,
    RURAL_WORKSHEET_API_URL: `https://${appEnv}.dae.qvapi.co.nz/worksheet`,
    SEARCH_API_HOST: `https://${appEnv}.dae.qvapi.co.nz/search`,
    PDF_GENERATOR_API_URL: `https://${appEnv}.dae.qvapi.co.nz/pdf-report-generator`,
    RTV_API_URL: `https://${appEnv}.dae.qvapi.co.nz/rtv`,
    API_PICKLIST_API_URL: `https://${appEnv}.dae.qvapi.co.nz/picklist`,
    API_OBJECTION_API_URL: `https://${appEnv}.dae.qvapi.co.nz/objection`,
    API_STATS_API_URL: `https://${appEnv}.dae.qvapi.co.nz/stats`,
    API_REPORT_GENERATOR_API_URL: `https://${appEnv}.dae.qvapi.co.nz/report-generator`,
    API_TA_API_URL: `https://${appEnv}.dae.qvapi.co.nz/ta`,
    API_CONSENT_API_URL: `https://${appEnv}.dae.qvapi.co.nz/consent`,
    API_STREAM_API_URL: `https://${appEnv}.dae.qvapi.co.nz/stream`,
    API_LOAD_API_URL: `https://${appEnv}.dde.qvapi.co.nz/load`,
    TEST_FACTORY_API_URL: `https://${appEnv}.dae.qvapi.co.nz/testfactory`,

    // Report Generator
    REPORT_GENERATOR_SQS_URL: `https://sqs.ap-southeast-2.amazonaws.com/${accountNumber}/report-generator-${appEnv}`,
    REPORT_GENERATOR_S3_BUCKET: 'qv-test-reports',
    REPORT_GENERATOR_S3_DIRECTORY: `report-generator-${appEnv}`,

    // public website
    CHARGIFY_API_URL: 'https://qvcci.chargify.com',
    CHARGIFY_PAY_URL: 'https://qvcci.chargifypay.com',
    CB_FROM_EMAIL: '<EMAIL>',
    MAILCHIMP_AUDIENCE_ID: 'b30be66161',
    MAILCHIMP_API_KEY: await getConfig('MAILCHIMP_API_KEY'),
    MAILCHIMP_SERVER_PREFIX: 'us21',
    RECAPTCHA_PUBLIC_KEY: '6LfPSdYZAAAAAOce603-ddqoCSPkvpz-bp74Hvml',
    CHARGIFYJS_PUBLIC_KEY: 'chjs_fhq8zcyhvvs8h3jcvj85s2yy',
    CHARGIFYJS_PRIVATE_KEY: await getConfig('CHARGIFYJS_PRIVATE_KEY'),

    // reinz api
    REINZ_API_AUTH_CLIENT_ID: await getConfig('REINZ_API_AUTH_CLIENT_ID'),
    REINZ_API_AUTH_CLIENT_SECRET: await getConfig('REINZ_API_AUTH_CLIENT_SECRET'),
    REINZ_API_AUTH_USERNAME: await getConfig('REINZ_API_AUTH_USERNAME'),
    REINZ_API_AUTH_PASSWORD: await getConfig('REINZ_API_AUTH_PASSWORD'),
  };

  return jsonConfig;
}
