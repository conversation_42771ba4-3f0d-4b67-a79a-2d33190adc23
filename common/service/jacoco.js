import * as cheerio from 'cheerio';

export function parseJacocoHtml(html) {
  const $ = cheerio.load(html);
  const keys = [];
  const values = [];

  $('thead td').each((i, el) => {
    if (i > 0) {
      keys.push($(el).text().trim());
    }
  });

  $('tfoot td').each((i, el) => {
    if (i > 0) {
      values.push($(el).text().trim().replace(/,/g, ''));
    }
  });

  const result = {};
  const keyCount = {};

  keys.forEach((key, index) => {
    if (keyCount[key]) {
      keyCount[key]++;
      result[`${key}${keyCount[key]}`] = values[index];
    } else {
      keyCount[key] = 1;
      result[key] = values[index];
    }
  });
  return result;
}
