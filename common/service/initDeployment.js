import { waitAndCreateNewDeployment } from './waitAndCreateNewDeployment.js';
import { CmdAppTasks } from '../enums/cmdAppTasks.js';
import { runEcsTask } from './ecsTools.js';
import { ApplicationType } from '../enums/applicationType.js';
import { addLogsToDataEnv } from '../dal/dataEnvironments.js';
import { addLogsToAppEnv } from '../dal/appEnvironments.js';
import { getApplicationByKey, getApplicationsByEnvAndType } from '../dal/applications.js';

export async function initDeployment(envName, type, applicationName, userName = '', reindex = false, sleepTags = false) {
  logger.info(`deployApplications  ${type} ${envName} ${applicationName}`);
  const resolvedUserName = userName || global.userInfo?.email?.split('@')[0] || 'Unknown';
  const applicationsToDeploy = await getApplicationsToDeploy(applicationName, type, envName);
  const deploymentId = await waitAndCreateNewDeployment(envName, applicationsToDeploy.length, type, resolvedUserName);
  const task = CmdAppTasks.APPLICATION_DEPLOY;
  const payload = JSON.stringify({
    type,
    envName,
    applicationName,
  });
  const url = await runEcsTask(task, {
    deploymentId, reindex, sleepTags, payload,
  });
  if (type === ApplicationType.DDE) {
    await addLogsToDataEnv(envName, task, url);
  }
  if (type === ApplicationType.DAE) {
    await addLogsToAppEnv(envName, task, url);
  }
  logger.info(`deploymentId ${deploymentId}`);
  return deploymentId;
}

export async function getApplicationsToDeploy(applicationName, type, envName) {
  let applicationsToDeploy = [];
  if (applicationName === '') {
    logger.info(`deployAllApplicationsByName ${type} ${envName}`);
    applicationsToDeploy = await getApplicationsByEnvAndType(envName, type);
  } else {
    logger.info(`deployApplications  ${type} ${envName} ${applicationName}`);
    for (const app of applicationName.split(',')) {
      const key = `${type}-${envName}-${app}`;
      const items = await getApplicationByKey(key);
      if (items.length) {
        applicationsToDeploy.push(items[0]);
      }
    }
  }
  return applicationsToDeploy;
}
