import { addCloudWatchLogs } from '../dal/dataEnvironments.js';
import { STATUS_INVALID, STATUS_MISSING, STATUS_SUCCESS } from '../enums/crudStatus.js';
import { deleteAppEnvironmentByName, getAppEnvironmentByName } from '../dal/appEnvironments.js';
import { PROVISIONING, TEARING_DOWN, UPDATING } from '../enums/environmentStatus.js';
import getConfig from '../config/getConfig.js';
import { runEcsTask } from './ecsTools.js';
import { CmdAppTasks } from '../enums/cmdAppTasks.js';

export default async function deleteAppEnvironment(appEnv) {
  const queryResults = await getAppEnvironmentByName(appEnv);
  logger.info(`Found ${queryResults.length} environments with name: ${appEnv}`);
  if (queryResults.length === 0) {
    return { status: STATUS_MISSING, message: `No environment found with name: ${appEnv}` };
  }
  const applicationEnvironment = queryResults[0];
  const status = applicationEnvironment.Status.S;
  if (status === PROVISIONING || status === TEARING_DOWN || status === UPDATING) {
    return {
      status: STATUS_INVALID,
      message: 'App Environment is currently in process of being provisioned or torn down',
    };
  }
  logger.info(`Found application environment ${appEnv} to delete.`);
  const { Logs } = applicationEnvironment;
  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  if (ecsTaskEnabled === 'true') {
    const cloudWatchLogs = [];
    const task = CmdAppTasks.APP_ENV_DESTROY;
    const url = await runEcsTask(task, { appEnv });
    cloudWatchLogs.push({ task, url });
    addCloudWatchLogs(cloudWatchLogs, Logs);
  }

  await deleteAppEnvironmentByName(appEnv, Logs);
  logger.info(`Environment ${appEnv} deleted.`);
  return { status: STATUS_SUCCESS, message: `Environment ${appEnv} started deleting` };
}
