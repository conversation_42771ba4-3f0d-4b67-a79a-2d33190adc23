import {
  CodePipelineClient,
  GetPipelineCommand,
  GetPipelineStateCommand,
  UpdatePipelineCommand,
} from '@aws-sdk/client-codepipeline';
import { REGION } from '../util/const.js';
import { sleep } from '../util/sleepUtil.js';
import { updateApplicationDeployed, updateApplicationDeployStatus } from '../dal/applications.js';
import { PipelineStatus } from '../enums/pipelineStatus.js';

const client = new CodePipelineClient({ REGION });

export async function fetchPipelineState(pipelineName) {
  const command = new GetPipelineStateCommand({ name: pipelineName });
  const response = await client.send(command);
  return response;
}

export function parsePipelineStatus(response, pipelineName) {
  const json = JSON.stringify(response, null, 2);
  let result = [];
  for (const stage of response.stageStates) {
    for (const actionState of stage.actionStates) {
      if (actionState.latestExecution) {
        result.push({
          actionName: actionState.actionName,
          lastStatusChange: actionState.latestExecution.lastStatusChange,
          status: actionState.latestExecution.status,
        });
      }
    }
  }

  // remove last pipeline status change
  result = result.filter((item, index, self) => {
    if (index === 0) return true;
    const previous = self[index - 1];
    return new Date(item.lastStatusChange) >= new Date(previous.lastStatusChange);
  });
  if (result.length === 0) {
    logger.info(`No results found for pipeline ${pipelineName}`);
    return false;
  }

  if (result.length === 1) {
    logger.info(`pipeline ${pipelineName} deploy not started`);
    return false;
  }

  logger.info(`pipeline ${pipelineName} deploy started`, { result });
  const anyError = result.some((item) => item.status === 'Abandoned' || item.status === 'Failed');
  if (anyError) {
    logger.info(`pipeline ${pipelineName} status: ${json}`);
    throw new Error(`pipeline ${pipelineName} deploy failed`);
  }
  const allSucceeded = result.every((item) => item.status === 'Succeeded');
  if (allSucceeded) {
    logger.info(`pipeline ${pipelineName} deploy succeeded`);
    return true;
  }
  return false;
}

export async function checkPipelineStatus(pipelineName) {
  const response = await fetchPipelineState(pipelineName);
  return parsePipelineStatus(response, pipelineName);
}

export async function monitorPipelineStatusWithTimeout(deploymentId, applicationKey, pipelineName, timeout) {
  const endTime = Date.now() + timeout;
  while (Date.now() < endTime) {
    try {
      logger.info(`checking pipeline ${pipelineName} status`);
      const success = await checkPipelineStatus(pipelineName);
      if (success) {
        await updateApplicationDeployed(applicationKey);
        logger.info(`pipeline ${pipelineName} deploy succeeded`);
        return true;
      }
    } catch (e) {
      await updateApplicationDeployStatus(applicationKey, PipelineStatus.FAILED);
      return false;
    }

    logger.info(`pipeline ${pipelineName} deploy not finished yet`);
    await sleep(30 * 1000);
  }
  logger.error('', `Timeout reached while monitoring pipeline ${pipelineName}`, null);
  return false;
}

export async function updateCustomData(pipelineName, newCustomData) {
  const getPipelineCommand = new GetPipelineCommand({
    name: pipelineName,
  });
  const currentPipeline = await client.send(getPipelineCommand);

  currentPipeline.pipeline.stages.forEach((stage) => {
    stage.actions.forEach((action) => {
      if (action.actionTypeId.category === 'Approval' && action.actionTypeId.provider === 'Manual') {
        action.configuration = action.configuration || {};
        action.configuration.CustomData = newCustomData;
      }
    });
  });

  const updatePipelineCommand = new UpdatePipelineCommand({
    pipeline: currentPipeline.pipeline,
  });
  await client.send(updatePipelineCommand);
}
