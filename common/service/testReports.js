import { DEPLOYMENT_REPORTS_BUCKET } from '../util/const.js';
import { removeDaeSuffix } from '../util/appNameUtli.js';
import { downloadFileToString, listAllFiles } from '../util/s3util.js';
import { parseJacocoHtml } from './jacoco.js';

export const PARSEABLE_FILES = [
  'lint/linter.txt',
  'unit/mochawesome.json',
  'smoke/mochawesome.json',
  'integration/mochawesome.json',
  'coverage/coverage-summary.json',
  'coverage/stats.txt',
];

export const REPORT_OUTPUT_FILES = [
  'lint/linter.txt',
  'unit/mochawesome.html',
  'smoke/mochawesome.html',
  'integration/mochawesome.html',
  'coverage/index.html',
];

export function parseLint(text) {
  const pattern = /(\d+) problems? \((\d+) errors?, (\d+) warnings?\)/;
  const match = text.match(pattern);

  if (match) {
    return {
      problems: parseInt(match[1], 10),
      errors: parseInt(match[2], 10),
      warnings: parseInt(match[3], 10),
    };
  }
  return {
    problems: 0,
    errors: 0,
    warnings: 0,
  };
}

export function parseMocha(text) {
  const stats = JSON.parse(text);
  return {
    passes: stats.stats.passes,
    tests: stats.stats.tests,
    pending: stats.stats.pending,
    failures: stats.stats.failures,
    skipped: stats.stats.skipped,
    passPercent: twoDigitsNumber(stats.stats.passPercent || 0),
  };
}

export function parseJacocoCoverage(html) {
  const output = parseJacocoHtml(html);
  const linesTotal = parseInt(output.Lines, 10);
  const linesCovered = parseInt(output.Lines, 10) - parseInt(output.Missed2, 10);
  const linesPct = twoDigitsNumber((linesCovered / linesTotal) * 100);

  const linesSkipped = 0;

  const statementsPct = 0;
  const statementsTotal = 0;
  const statementsCovered = 0;
  const statementsSkipped = 0;

  const functionsPct = 0;
  const functionsTotal = 0;
  const functionsCovered = 0;
  const functionsSkipped = 0;

  const missedBranchesArr = output['Missed Branches'].split(' ');
  const missedBranches = parseInt(missedBranchesArr[0], 10);
  const branchesTotalNum = parseInt(missedBranchesArr[2], 10);

  const branchesTotal = branchesTotalNum;
  const branchesCovered = branchesTotalNum - missedBranches;
  const branchesPct = twoDigitsNumber((branchesCovered / branchesTotal) * 100);

  const branchesSkipped = 0;

  return {
    linesPct,
    linesTotal,
    linesCovered,
    linesSkipped,
    statementsPct,
    statementsTotal,
    statementsCovered,
    statementsSkipped,
    functionsPct,
    functionsTotal,
    functionsCovered,
    functionsSkipped,
    branchesPct,
    branchesTotal,
    branchesCovered,
    branchesSkipped,
  };
}

export function parseNycCoverage(text) {
  const stats = JSON.parse(text);
  const { total } = stats;

  const linesPct = total.lines.pct === 'Unknown' ? 0 : total.lines.pct;
  const linesTotal = total.lines.total;
  const linesCovered = total.lines.covered;
  const linesSkipped = total.lines.skipped;

  const statementsPct = total.statements.pct === 'Unknown' ? 0 : total.statements.pct;
  const statementsTotal = total.statements.total;
  const statementsCovered = total.statements.covered;
  const statementsSkipped = total.statements.skipped;

  const functionsPct = total.functions.pct === 'Unknown' ? 0 : total.functions.pct;
  const functionsTotal = total.functions.total;
  const functionsCovered = total.functions.covered;
  const functionsSkipped = total.functions.skipped;

  const branchesPct = total.branches.pct === 'Unknown' ? 0 : total.branches.pct;
  const branchesTotal = total.branches.total;
  const branchesCovered = total.branches.covered;
  const branchesSkipped = total.branches.skipped;

  return {
    linesPct,
    linesTotal,
    linesCovered,
    linesSkipped,
    statementsPct,
    statementsTotal,
    statementsCovered,
    statementsSkipped,
    functionsPct,
    functionsTotal,
    functionsCovered,
    functionsSkipped,
    branchesPct,
    branchesTotal,
    branchesCovered,
    branchesSkipped,
  };
}

export async function getDaeTestResult(s3Client, application, appEnv, deploymentId) {
  const folder = `s3://${DEPLOYMENT_REPORTS_BUCKET}/${appEnv}/${removeDaeSuffix(application)}/${deploymentId}/`;
  return getTestResult(s3Client, folder);
}

export async function getTestResult(s3Client, folder) {
  const files = await listAllFiles(s3Client, [folder]);
  const records = extractReports(files, PARSEABLE_FILES);
  return parseReports(s3Client, records);
}

export async function parseReports(s3Client, records) {
  const report = {
    lintProblemsCount: 0,
    lintErrorsCount: 0,
    lintWarningsCount: 0,

    coverageLinesPct: 0,
    coverageLinesTotal: 0,
    coverageLinesCovered: 0,

    coverageStatementsPct: 0,
    coverageStatementsTotal: 0,
    coverageStatementsCovered: 0,

    coverageBranchesPct: 0,
    coverageBranchesTotal: 0,
    coverageBranchesCovered: 0,

    coverageFunctionsPct: 0,
    coverageFunctionsTotal: 0,
    coverageFunctionsCovered: 0,

    testPasses: 0,
    testTests: 0,
    testPending: 0,
    testSkipped: 0,
    testFailures: 0,
    testPassPercent: 0,
  };

  for (const record of records) {
    const reportContent = await downloadFileToString(s3Client, DEPLOYMENT_REPORTS_BUCKET, record.key);
    if (record.testType === 'lint') {
      const lintResult = parseLint(reportContent);
      report.lintProblemsCount = lintResult?.problems || 0;
      report.lintErrorsCount = lintResult?.errors || 0;
      report.lintWarningsCount = lintResult?.warnings || 0;
    }

    if (record.testType === 'coverage') {
      let coverResult;
      if (record.filename === 'stats.txt') {
        coverResult = parseJacocoCoverage(reportContent);
      }
      if (record.filename === 'coverage-summary.json') {
        coverResult = parseNycCoverage(reportContent);
      }
      if (coverResult) {
        report.coverageLinesPct = coverResult?.linesPct || 0;
        report.coverageLinesTotal = coverResult?.linesTotal || 0;
        report.coverageLinesCovered = coverResult?.linesCovered || 0;

        report.coverageStatementsPct = coverResult?.statementsPct || 0;
        report.coverageStatementsTotal = coverResult?.statementsTotal || 0;
        report.coverageStatementsCovered = coverResult?.statementsCovered || 0;

        report.coverageBranchesPct = coverResult?.branchesPct || 0;
        report.coverageBranchesTotal = coverResult?.branchesTotal || 0;
        report.coverageBranchesCovered = coverResult?.branchesCovered || 0;

        report.coverageFunctionsPct = coverResult?.functionsPct || 0;
        report.coverageFunctionsTotal = coverResult?.functionsTotal || 0;
        report.coverageFunctionsCovered = coverResult?.functionsCovered || 0;
      }
    }
    if (record.testType === 'unit' || record.testType === 'smoke' || record.testType === 'integration') {
      const testResult = parseMocha(reportContent);
      report.testPasses += testResult.passes;
      report.testPending += testResult.pending;
      report.testSkipped += testResult.skipped;
      report.testFailures += testResult.failures;
      report.testTests += testResult.tests;
      report.testPassPercent = 0;
      if (report.testTests !== 0) {
        report.testPassPercent = twoDigitsNumber((report.testPasses / report.testTests) * 100);
      }
    }
  }
  return report;
}

export function accumulateTestResults(existingTestResults, testResults) {
  const {
    lintProblemsCount: existingLintProblemsCount,
    lintErrorsCount: existingLintErrorsCount,
    lintWarningsCount: existingLintWarningsCount,

    coverageLinesTotal: existingCoverageLinesTotal,
    coverageLinesCovered: existingCoverageLinesCovered,

    coverageStatementsTotal: existingCoverageStatementsTotal,
    coverageStatementsCovered: existingCoverageStatementsCovered,

    coverageFunctionsTotal: existingCoverageFunctionsTotal,
    coverageFunctionsCovered: existingCoverageFunctionsCovered,

    coverageBranchesTotal: existingCoverageBranchesTotal,
    coverageBranchesCovered: existingCoverageBranchesCovered,

    testPasses: existingTestPasses,
    testPending: existingTestPending,
    testFailures: existingTestFailures,
    testTests: existingTestTests,
  } = existingTestResults;

  const {
    lintProblemsCount = 0,
    lintErrorsCount = 0,
    lintWarningsCount = 0,
    coverageLinesTotal = 0,
    coverageLinesCovered = 0,
    coverageStatementsTotal = 0,
    coverageStatementsCovered = 0,
    coverageFunctionsTotal = 0,
    coverageFunctionsCovered = 0,
    coverageBranchesTotal = 0,
    coverageBranchesCovered = 0,

    testPasses = 0,
    testTests = 0,
    testPending = 0,
    testFailures = 0,
  } = testResults;
  const result = {
    lintProblemsCount: existingLintProblemsCount + lintProblemsCount,
    lintErrorsCount: existingLintErrorsCount + lintErrorsCount,
    lintWarningsCount: existingLintWarningsCount + lintWarningsCount,

    coverageLinesTotal: existingCoverageLinesTotal + coverageLinesTotal,
    coverageLinesCovered: existingCoverageLinesCovered + coverageLinesCovered,

    coverageStatementsTotal: existingCoverageStatementsTotal + coverageStatementsTotal,
    coverageStatementsCovered: existingCoverageStatementsCovered + coverageStatementsCovered,

    coverageFunctionsTotal: existingCoverageFunctionsTotal + coverageFunctionsTotal,
    coverageFunctionsCovered: existingCoverageFunctionsCovered + coverageFunctionsCovered,

    coverageBranchesTotal: existingCoverageBranchesTotal + coverageBranchesTotal,
    coverageBranchesCovered: existingCoverageBranchesCovered + coverageBranchesCovered,

    testPasses: existingTestPasses + testPasses,
    testPending: existingTestPending + testPending,
    testFailures: existingTestFailures + testFailures,
    testTests: existingTestTests + testTests,
  };

  result.testPassPercent = 0;
  if (result.testTests !== 0) {
    result.testPassPercent = twoDigitsNumber((result.testPasses / result.testTests) * 100);
  }

  result.coverageLinesPct = 0;
  if (result.coverageLinesTotal !== 0) {
    result.coverageLinesPct = twoDigitsNumber((result.coverageLinesCovered / result.coverageLinesTotal) * 100);
  }

  result.coverageStatementsPct = 0;
  if (result.coverageStatementsTotal !== 0) {
    result.coverageStatementsPct = twoDigitsNumber((result.coverageStatementsCovered / result.coverageStatementsTotal) * 100);
  }

  result.coverageFunctionsPct = 0;
  if (result.coverageFunctionsTotal !== 0) {
    result.coverageFunctionsPct = twoDigitsNumber((result.coverageFunctionsCovered / result.coverageFunctionsTotal) * 100);
  }

  result.coverageBranchesPct = 0;
  if (result.coverageBranchesTotal !== 0) {
    result.coverageBranchesPct = twoDigitsNumber((result.coverageBranchesCovered / result.coverageBranchesTotal) * 100);
  }
  return result;
}

export function extractReports(files, fileNames) {
  return files.map((file) => {
    const { Key } = file;
    const split = Key.split('/');
    const len = split.length;
    return {
      key: Key,
      application: split[1],
      testType: split[len - 2],
      filename: split[len - 1],
    };
  }).filter((item) => {
    const splitPath = item.key.split('/');
    const lastTwoSegments = splitPath.slice(-2).join('/');
    return fileNames.includes(lastTwoSegments);
  });
}

export function twoDigitsNumber(number) {
  return parseFloat(number.toFixed(2));
}

export function formatTestReport(testResults) {
  const testInfoJson = JSON.parse(testResults || '{}');
  const {
    lintProblemsCount = 0,
    lintErrorsCount = 0,
    lintWarningsCount = 0,

    coverageLinesPct = 0,
    coverageLinesTotal = 0,
    coverageLinesCovered = 0,

    coverageStatementsPct = 0,
    coverageStatementsTotal = 0,
    coverageStatementsCovered = 0,

    coverageBranchesPct = 0,
    coverageBranchesTotal = 0,
    coverageBranchesCovered = 0,

    coverageFunctionsPct = 0,
    coverageFunctionsTotal = 0,
    coverageFunctionsCovered = 0,

    testPasses = 0,
    testTests = 0,
    testPending = 0,
    testSkipped = 0,
    testFailures = 0,
    testPassPercent = 0,
  } = testInfoJson;
  let coverageTotal = 0;
  let coverageCount = 0;

  if (coverageLinesTotal) {
    coverageTotal += coverageLinesPct;
    coverageCount++;
  }
  if (coverageStatementsTotal) {
    coverageTotal += coverageStatementsPct;
    coverageCount++;
  }
  if (coverageBranchesTotal) {
    coverageTotal += coverageBranchesPct;
    coverageCount++;
  }
  if (coverageFunctionsTotal) {
    coverageTotal += coverageFunctionsPct;
    coverageCount++;
  }
  let coveragePct = 0;
  if (coverageCount > 0) {
    coveragePct = twoDigitsNumber(coverageTotal / coverageCount);
  }

  return {
    lintProblemsCount,
    lintErrorsCount,
    lintWarningsCount,

    coverageLinesPct,
    coverageLinesTotal,
    coverageLinesCovered,

    coverageStatementsPct,
    coverageStatementsTotal,
    coverageStatementsCovered,

    coverageBranchesPct,
    coverageBranchesTotal,
    coverageBranchesCovered,

    coverageFunctionsPct,
    coverageFunctionsTotal,
    coverageFunctionsCovered,

    coveragePct,

    testPasses,
    testTests,
    testPending,
    testSkipped,
    testFailures,
    testPassPercent,
  };
}
