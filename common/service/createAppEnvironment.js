import { STATUS_INVALID, STATUS_SUCCESS } from '../enums/crudStatus.js';
import {
  createNewAppEnvironment,
  getAppEnvironmentsByStatus,
  updateAppEnvironmentDefaultJsonConfig,
} from '../dal/appEnvironments.js';
import { saveDaeApps } from '../dal/applications.js';
import {
  DELETED, DEPLOYING, HEALTHY, INDEXING, INDEXING_FAILED, PROVISIONING, STABLE, TESTING, UNHEALTHY,
} from '../enums/environmentStatus.js';
import {
  API_STREAM_APP, MAX_APP_ENVIRONMENTS, RESERVED_APP_ENV_NAMES, RESERVED_DATA_ENV_NAMES,
} from '../util/const.js';
import { isValidJSON } from '../util/validatUtil.js';
import { getDataEnvironmentByName } from '../dal/dataEnvironments.js';
import { runEcsTask } from './ecsTools.js';
import { checkBranchExistence } from './checkBranchExistence.js';
import { CmdAppTasks } from '../enums/cmdAppTasks.js';
import { ApplicationStack } from '../enums/applicationStack.js';
import getConfig from '../config/getConfig.js';

export default async function createAppEnvironment(appEnv, payload, runTask) {
  const {
    dataEnv,
    defaultBranch,
    contextConfig,
    awsAccount,
    locked,
    includeQivs,
    includeQivsExternal,
    costCentre,
    applications,
    stacks,
    sleepTags,
  } = payload;
  let regex = /^[a-z0-9]{3,10}$/;

  if (includeQivs === true) {
    regex = /^[a-z0-9]{3,8}$/;
  }

  if (!regex.test(appEnv)) {
    return {
      status: STATUS_INVALID,
      message: 'Invalid app environment name, please use lowercase letters, numbers, minimum length: 3 characters, maximum length: 10 characters,8 characters if qivs included',
    };
  }
  if (!costCentre || costCentre === '') {
    return {
      status: STATUS_INVALID,
      message: 'Cost centre is required',
    };
  }
  if (RESERVED_APP_ENV_NAMES.includes(appEnv.toLowerCase())) {
    return {
      status: STATUS_INVALID,
      message: 'Reserved app environment name',
    };
  }
  if (isValidJSON(contextConfig) === false) {
    return {
      status: STATUS_INVALID,
      message: 'Context config should be a valid JSON',
    };
  }
  if (!stacks || stacks.length === 0) {
    return {
      status: STATUS_INVALID,
      message: 'stacks is required',
    };
  }
  if (stacks.includes(ApplicationStack.PUBLIC_WEBSITE) && RESERVED_DATA_ENV_NAMES.includes(dataEnv)) {
    return {
      status: STATUS_INVALID,
      message: 'public website stack cannot be included in reserved data environment',
    };
  }
  logger.info('checking max number');
  const stage = await getConfig('STAGE', 'local');
  const statuses = [HEALTHY, UNHEALTHY, DEPLOYING, TESTING, INDEXING, INDEXING_FAILED, STABLE, PROVISIONING, DELETED];
  const existingAppEnvironments = await getAppEnvironmentsByStatus(statuses);
  logger.info(`Found ${existingAppEnvironments.length} existing app environments`);
  const availableEnvironments = existingAppEnvironments.filter((item) => item.Status.S !== DELETED && item.Stage.S === stage);
  const unReservedAppEnvironments = availableEnvironments.filter((item) => !RESERVED_APP_ENV_NAMES.includes(item.Name.S));
  if (unReservedAppEnvironments.length >= MAX_APP_ENVIRONMENTS) {
    return { status: STATUS_INVALID, message: 'Maximum number of app environments reached' };
  }
  if (existingAppEnvironments.some((item) => item.Name.S === appEnv)) {
    logger.info('App environment name already exists');
    return { status: STATUS_INVALID, message: 'App environment name already exists' };
  }
  if (!dataEnv) {
    return {
      status: STATUS_INVALID,
      message: 'Data environment is required.',
    };
  }
  if (stacks.includes(ApplicationStack.PUBLIC_WEBSITE)) {
    const availableEnvironmentsByDataEnv = availableEnvironments.filter((item) => item.DataEnv.S === dataEnv);
    if (availableEnvironmentsByDataEnv.length > 0) {
      const publicWebsiteExist = availableEnvironmentsByDataEnv.some((item) => item.Stacks.L.some((stack) => stack.S === ApplicationStack.PUBLIC_WEBSITE));
      if (publicWebsiteExist) {
        const name = availableEnvironmentsByDataEnv[0].Name.S;
        return {
          status: STATUS_INVALID,
          message: `App Environment ${name} already has Public website included in the same data environment, `,
        };
      }
    }
  }

  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    return {
      status: STATUS_INVALID,
      message: 'Data environment does not exist.',
    };
  }
  if (![HEALTHY, UNHEALTHY, STABLE].includes(queryResults[0].Status.S)) {
    return {
      status: STATUS_INVALID,
      message: 'Data environment is not stable.',
    };
  }

  const hasApiStream = applications.some((application) => application.name === API_STREAM_APP);
  if (hasApiStream && RESERVED_DATA_ENV_NAMES.includes(dataEnv)) {
    return {
      status: STATUS_INVALID,
      message: 'API Stream application cannot be included in reserved data environment',
    };
  }

  const logs = [];
  if (runTask) {
    const task = CmdAppTasks.APP_ENV_CREATE;
    logger.info(`reindex: ${hasApiStream} for app ${appEnv}`);
    const url = await runEcsTask(task, { appEnv, reindex: hasApiStream, sleepTags: true });
    logger.info(`task ${task} started, log url: ${url}`);
    logs.push({ task, url });
  }

  logger.info('creating new app environment');
  await createNewAppEnvironment(
    {
      appEnv,
      dataEnv,
      stage,
      defaultBranch,
      contextConfig,
      awsAccount,
      locked,
      includeQivs,
      costCentre,
      stacks,
      logs,
      sleepTags,
      includeQivsExternal,
    },
  );
  await updateAppEnvironmentDefaultJsonConfig(appEnv, contextConfig);
  logger.info('saving applications');
  await saveDaeApps(appEnv, applications, await checkBranchExistence(defaultBranch), defaultBranch);
  return { status: STATUS_SUCCESS, message: `App Environment ${appEnv} started provisioning ` };
}
