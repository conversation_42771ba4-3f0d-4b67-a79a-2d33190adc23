import { DescribeTaskDefinitionCommand, ECSClient, RunTaskCommand } from '@aws-sdk/client-ecs';
import { REGION } from '../util/const.js';
import { getTask } from '../dal/getResourceName.js';

export const ecsClient = new ECSClient({ region: REGION });

const subnets = ['subnet-097383e7e4f76f0ec', 'subnet-0dd620c23992d88ae', 'subnet-0881390c25645dc44'];
const securityGroups = ['sg-04918d91687fd6b24'];
const taskDefinition = 'arn:aws:ecs:ap-southeast-2:948396734470:task-definition';
const CICD_CLUSTER = 'dynamic-environments';

export async function runEcsTask(task, options) {
  const taskName = await getTask(`${task}`);
  const taskDef = `${taskName}-task`;
  const input = { taskDefinition: taskDef };
  logger.info('DescribeTaskDefinitionCommand input ', { input });
  const taskDefinitionCommandOutput = await ecsClient.send(new DescribeTaskDefinitionCommand(input));
  const { revision } = taskDefinitionCommandOutput.taskDefinition;

  const params = {
    cluster: CICD_CLUSTER,
    taskDefinition: `${taskDefinition}/${taskDef}:${revision}`,
    count: 1,
    launchType: 'FARGATE',
    networkConfiguration: {
      awsvpcConfiguration: {
        subnets,
        securityGroups,
        assignPublicIp: 'DISABLED',
      },
    },
    overrides: {
      containerOverrides: [{
        name: taskDef,
        command: buildCommand(task, options),
      }],
    },
  };
  logger.info(`restore ${taskDef} task is running`);
  const result = await ecsClient.send(new RunTaskCommand(params));
  const { taskArn } = result.tasks[0];
  logger.info(`Started ECS task with ID: ${taskArn}`);
  const taskId = taskArn.split('/').pop();
  const awsBaseUrl = `https://${REGION}.console.aws.amazon.com`;
  const taskUrl = `${awsBaseUrl}/ecs/v2/clusters/dynamic-environments/tasks/${taskId}/logs?region=${REGION}`;
  logger.info(`taskUrl: ${taskUrl}`);
  const groupName = `/aws/ecs/task/${taskName}`;
  const logEventViewerUrl = `${awsBaseUrl}/cloudwatch/home?region=${REGION}#logEventViewer:group=${groupName};stream=${taskDef}/${taskDef}/${taskId}`;
  logger.info(`logEventViewerUrl: ${logEventViewerUrl}`);
  return logEventViewerUrl;
}

function buildCommand(task, options) {
  const envOptions = Object.entries(options)
    .filter(([, value]) => value)
    .map(([key, value]) => (key === 'payload' ? `--${key} ${JSON.stringify(value)}` : `--${key} ${value}`))
    .join(' ');

  logger.info(`envOption: ${envOptions}`);
  return ['sh', '-c', `cd /app/cmd && node src/index.js --name ${task} ${envOptions}`];
}
