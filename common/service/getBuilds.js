import { formatBuilds, getAllBuilds } from '../dal/builds.js';
import { getApplicationsByEnvAndType } from '../dal/applications.js';
import { getEnabledAppStacks } from '../dal/appStacks.js';
import getConfig from '../config/getConfig.js';

export async function getBuilds(parameters) {
  let appStacks = await getEnabledAppStacks();
  const stacks = parameters?.stacks;
  if (stacks) {
    const stackArray = stacks.split(',');
    appStacks = appStacks.filter((appStack) => appStack.Stacks.L.some((it) => stackArray.includes(it.S)));
  }
  const allowedApplications = appStacks.map((appStack) => appStack.Pipeline.S);
  logger.info(`Allowed applications: ${allowedApplications}`);
  const existingBuilds = await getAllBuilds();
  const filteredBuilds = existingBuilds.filter((build) => {
    logger.info(`Checking build ${build.Application.S}`);
    return allowedApplications.includes(build.Application.S);
  });

  logger.info(`Found ${filteredBuilds.length} existing builds`);

  const applications = formatBuilds(filteredBuilds);
  const results = applications.reduce((grouped, item) => {
    const key = item.application;

    if (!grouped[key]) {
      grouped[key] = {
        ...item,
        gitBranch: [item.gitBranch],
      };
    } else {
      grouped[key].gitBranch.push(item.gitBranch);
    }

    return grouped;
  }, {});

  let groupedValues = Object.values(results);
  groupedValues.sort((a, b) => {
    if (a.type < b.type) {
      return -1;
    }
    if (a.type > b.type) {
      return 1;
    }
    return 0;
  });

  const defaultCheckedApps = appStacks.filter((item) => item.DeployDefault.BOOL === true).map((item) => item.Pipeline.S);
  const selectableApps = appStacks.filter((item) => item.Selectable.BOOL === true).map((item) => item.Pipeline.S);
  const stage = await getConfig('STAGE', 'local');
  groupedValues.forEach((build) => {
    build.selected = defaultCheckedApps.includes(build.application);
    const selectable = stage !== 'cicd' || selectableApps.includes(build.application);
    logger.info(`application ${build.application}, selectable: ${selectable}`);
    build.selectable = selectable;
  });
  const envName = parameters?.envName;
  if (envName) {
    const type = parameters?.type;
    const existingApplications = await getApplicationsByEnvAndType(envName, type);
    const existingApplicationNames = existingApplications.map((application) => application.Name.S);
    groupedValues = groupedValues.filter((build) => existingApplicationNames.includes(build.application));
    logger.info('Add pods, branchOverride to builds for editing');
    for (let i = 0; i < existingApplications.length; i++) {
      for (let j = 0; j < groupedValues.length; j++) {
        const application = existingApplications[i];
        const build = groupedValues[j];
        if (application.Name.S === build.application) {
          build.branchOverride = application.BranchOverride.S;
          build.pods = application.Pods?.S;
          build.selected = true;
        }
      }
    }
  }
  groupedValues.sort((a, b) => a.application.localeCompare(b.application));
  return groupedValues;
}
