import { DescribeLoadBalancersCommand } from '@aws-sdk/client-elastic-load-balancing-v2';

export async function getLoadBalancerDNSName(client, loadBalancerName) {
  try {
    const data = await client.send(new DescribeLoadBalancersCommand({ Names: [loadBalancerName] }));

    if (data.LoadBalancers && data.LoadBalancers.length > 0) {
      return data.LoadBalancers[0].DNSName;
    }
    console.error('Load balancer not found');
    return null;
  } catch (error) {
    console.error('Error retrieving load balancer DNS name:', error);
    return null;
  }
}
