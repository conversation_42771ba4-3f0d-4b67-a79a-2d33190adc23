import { getIncompleteDeployments } from '../dal/deployments.js';
import { sleep } from '../util/sleepUtil.js';

const DEPLOYMENT_STATUS_CHECK_TIMEOUT_LIMIT = 8 * 60 * 60 * 1000;
const DEPLOYMENT_STATUS_CHECK_INTERVAL = 60 * 1000;
/**
 *  only allow 1 deployment at a time,
 *
 * @param env
 * @param type
 * @returns {Promise<unknown>}
 */

export default async function waitDeploymentCompleted(env, type) {
  const startTime = Date.now();
  while (Date.now() - startTime < DEPLOYMENT_STATUS_CHECK_TIMEOUT_LIMIT) {
    const incompleteDeployments = await getIncompleteDeployments(env, type);
    logger.info('Current not completed deployments', { incompleteDeployments });
    if (incompleteDeployments.length === 0) {
      return;
    }
    await sleep(DEPLOYMENT_STATUS_CHECK_INTERVAL);
  }
  throw new Error(`Timeout: Deployment completion check took more than ${DEPLOYMENT_STATUS_CHECK_TIMEOUT_LIMIT}.`);
}
