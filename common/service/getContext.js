import { getAppEnvironmentByName, getStacks } from '../dal/appEnvironments.js';
import { getApplicationsByEnvAndType } from '../dal/applications.js';
import { ApplicationType } from '../enums/applicationType.js';
import { groupApplicationByPlatform } from '../dal/appStacks.js';
import { getPodsConfig } from './getAppEnvConfig.js';
import { getDataEnvironmentByName } from '../dal/dataEnvironments.js';
import { ApplicationStack } from '../enums/applicationStack.js';

export async function getContext(name, type) {
  if (type === ApplicationType.DAE) {
    return getDaeContext(name);
  }
  if (type === ApplicationType.DDE) {
    return getDdeContext(name);
  }
  return {};
}

export async function getDaeContext(name) {
  const queryResults = await getAppEnvironmentByName(name);
  let result = {};
  if (queryResults.length === 0) {
    return result;
  }

  const appEnvironment = queryResults[0];
  const stacks = getStacks(appEnvironment);
  const monarchIncluded = stacks.includes(ApplicationStack.MONARCH);
  logger.info(`monarchIncluded ${monarchIncluded}`);
  const contextConfig = JSON.parse(appEnvironment.ContextConfig?.S ?? '{}');
  const tfDeterminedConfig = JSON.parse(appEnvironment.TfDeterminedConfig?.S ?? '{}');
  result = { ...contextConfig, ...tfDeterminedConfig };
  result.COST_CENTRE = appEnvironment.CostCentre?.S;
  result.SHOULD_USE_TEST_FOLLOWERS = !monarchIncluded;
  const applications = await getApplicationsByEnvAndType(name, ApplicationType.DAE);
  const { k8sApplications } = await groupApplicationByPlatform(applications);
  const k8sPodsConfig = await getPodsConfig(k8sApplications);
  logger.info(k8sPodsConfig);
  result = {
    ...result,
    ...k8sPodsConfig,
  };
  return result;
}

export async function getDdeContext(name) {
  const queryResults = await getDataEnvironmentByName(name);
  let result = {};
  if (queryResults.length === 0) {
    return result;
  }

  const dataEnvironment = queryResults[0];
  const dataEnv = dataEnvironment.Name?.S;
  const {
    mssqlServer = '',
    monarch_main_db = '',
    monarch_services_username = '',
    monarch_services_password = '',
  } = JSON.parse(dataEnvironment?.SqlConfig?.S ?? '{}');
  const jsonConfig = {
    MSSQL_DB_HOST: mssqlServer,
    QIVS_MAIN_DB: monarch_main_db || `${dataEnv}_qvnz`,
    QIVS_MAIN_USER: monarch_services_username,
    QIVS_MAIN_PASSWORD: monarch_services_password,

  };

  result = JSON.parse(dataEnvironment.ContextConfig?.S ?? '{}');

  return { ...result, ...jsonConfig };
}
