import { STATUS_SUCCESS } from '../enums/crudStatus.js';
import { updateApplicationHealthCheck } from '../dal/applications.js';
import { getPlatform } from '../dal/appStacks.js';
import { Platform } from '../enums/platform.js';
import { getItemById } from '../dal/common.js';
import { APPLICATIONS, RESERVED_APP_ENV_NAMES } from '../util/const.js';
import { getLoadBalancerDNSName } from './elb.js';
import { normalizeName } from '../util/appNameUtli.js';
import { devElbClient } from '../util/awsConfig.js';
import { sleep } from '../util/sleepUtil.js';
import { getApiKeyByName } from './apiGateway.js';
import { PipelineStatus } from '../enums/pipelineStatus.js';

export async function healthCheckResult(envName, application, type, lambdaApiKey) {
  const platform = await getPlatform(application.name);
  let result;
  if (platform === Platform.LAMBDA) {
    const appName = application.name.replace(/^api-/, '');
    logger.info(`appName: ${appName}`);
    let baseUrl = `https://${envName}.${type.toLowerCase()}.qvapi.co.nz`;
    if (RESERVED_APP_ENV_NAMES.includes(envName)) {
      baseUrl = `https://${envName}.qvapi.co.nz`;
    }

    const url = `${baseUrl}/${appName}/health-check`;

    try {
      logger.info(`Sending health check request to ${url}`);
      const options = {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'x-api-key': lambdaApiKey,
        },
      };
      const response = await fetch(url, options);
      if (response.ok) {
        result = await response.json();
      } else {
        logger.info(`Network response was not ok: ${response.statusText}`);
      }
    } catch (error) {
      logger.error('ERR-CICD-API-100', 'failed to get health check', error);
    }
  }
  if (platform === Platform.K8S) {
    let monarchWebUrl = `https://${normalizeName(envName)}.monarch.internal.quotablevalue.co.nz`;
    if (RESERVED_APP_ENV_NAMES.includes(envName)) {
      monarchWebUrl = `https://${envName}.qvmonarch.co.nz`;
    }

    const elbDnsName = await getLoadBalancerDNSName(devElbClient, `Monarch-${normalizeName(envName)}-PrivateALB`);
    let appName = toCamelCase(application.name);
    if (appName === 'saleAnalysis') {
      appName = 'sAnalysis';
    }
    logger.info(`appName: ${appName}`);
    let url;
    if (appName === 'monarchWeb') {
      url = `${monarchWebUrl}/healthCheck`;
    } else {
      url = `http://${elbDnsName}/${appName}HealthCheck`;
    }
    try {
      logger.info(`Sending health check request to ${url}`);
      const options = {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      };
      const response = await fetch(url, options);
      if (response.ok) {
        result = await response.json();
      } else {
        logger.info(`Network response was not ok: ${response.statusText}`);
      }
    } catch (error) {
      logger.error('ERR-CICD-API-101', 'failed to get health check', error);
    }
  }
  if (result) {
    delete result.ctx;
    await updateApplicationHealthCheck(application.key, JSON.stringify(result));
  }
  return result;
}

function toCamelCase(str) {
  return str
    .split('-')
    .map((word, index) => (index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)))
    .join('');
}

export async function healthCheck(envName, applicationName, type) {
  const apiKeyName = `${envName}-${type.toLowerCase()}-lambda-api-key`;
  const apiKeyMap = {
    dev: 'website-dev',
    test: 'website-test',
    uat: 'website-uat',
  };

  const lambdaApiKey = await getApiKeyByName(apiKeyMap[envName] ?? apiKeyName, 5, 2);

  const key = `${type}-${envName}-${applicationName}`;

  const application = await getItemById(APPLICATIONS, { Key: key });

  const result = await healthCheckResult(envName, application, type, lambdaApiKey);

  return { status: STATUS_SUCCESS, data: result };
}

export async function waitApplicationToBeHealthyWithTimeout(envName, application, type, lambdaApiKey, timeout) {
  const endTime = Date.now() + timeout;

  while (Date.now() < endTime) {
    try {
      const result = await healthCheckResult(envName, application, type, lambdaApiKey);
      if (result) {
        logger.info(`Application ${application.name} in environment ${envName} is healthy.`);
        return true;
      }
      logger.info(`Application ${application.name} in environment ${envName} is not healthy yet.`);
    } catch (error) {
      logger.error(`Error checking application health: ${error.message}`);
      throw error;
    }
    await sleep(5 * 1000);
  }
  return false;
}

export async function isAppHealthy(app) {
  if (app.status !== PipelineStatus.DEPLOYED) {
    return false;
  }
  if (await getPlatform(app.name) === Platform.ECS) {
    return true;
  }
  if (!app.healthCheckResult || !app.healthCheckTime) {
    return false;
  }
  const json = JSON.parse(app.healthCheckResult);
  if (json.mssql && json.mssql !== 'SUCCESS') {
    return false;
  }
  if (json.pg && json.pg !== 'SUCCESS') {
    return false;
  }
  if (json.redshift && json.redshift !== 'SUCCESS') {
    return false;
  }
  if (json.replication && json.replication !== 'SUCCESS') {
    return false;
  }
  const resultOutDated = app.healthCheckTime && (Date.now() - app.healthCheckTime) > 60 * 60 * 1000;
  return !resultOutDated;
}
