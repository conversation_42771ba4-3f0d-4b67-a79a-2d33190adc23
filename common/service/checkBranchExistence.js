import { getAllBuilds } from '../dal/builds.js';
import { getBuildPipelineNames } from '../dal/appStacks.js';

export async function checkBranchExistence(branch) {
  const pipelineNames = await getBuildPipelineNames();
  const allBuilds = await getAllBuilds();
  return pipelineNames.map((application) => {
    const checkBranch = allBuilds.some((build) => build.Key.S === `${application}-${branch}`);
    return {
      name: application,
      exists: checkBranch,
    };
  });
}
