import fs from 'fs';
import getParameterValueFromSSM from '../dal/ssm.js';

let config;

export default async function getConfig(input, defaultValue = null) {
  if (config) {
    return _getConfig(input, defaultValue);
  }
  const configJSON = process.env.ENV === 'local'
    ? fs.readFileSync('config.json')
    : await getParameterValueFromSSM(process.env.PARAM_STORE_KEY);

  try {
    config = JSON.parse(configJSON);
  } catch (error) {
    throw new Error('ERR-OBJ-022 failed to parse parameter store config');
  }

  return _getConfig(input, defaultValue);
}

function _getConfig(input, defaultValue) {
  const values = config?.[input];
  if (!values && !defaultValue) {
    throw new Error(`ERR-OBJ-021 not configured with ${input}`);
  }
  return values ?? defaultValue;
}
