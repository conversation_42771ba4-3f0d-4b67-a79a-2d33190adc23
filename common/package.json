{"name": "common", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {}, "dependencies": {"adm-zip": "^0.5.10", "cheerio": "^1.0.0-rc.10"}, "devDependencies": {"@aws-sdk/client-api-gateway": "^3.624.0", "@aws-sdk/client-apigatewayv2": "^3.750.0", "@aws-sdk/client-elastic-load-balancing-v2": "^3.624.0", "@aws-sdk/client-codebuild": "^3.427.0", "@aws-sdk/client-codepipeline": "^3.427.0", "@aws-sdk/client-dynamodb": "^3.363.0", "@aws-sdk/util-dynamodb": "^3.326.0", "@aws-sdk/client-ecs": "^3.363.0", "@aws-sdk/client-s3": "^3.363.0", "@aws-sdk/client-ssm": "^3.363.0", "@aws-sdk/util-utf8-node": "^3.259.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.6"}}