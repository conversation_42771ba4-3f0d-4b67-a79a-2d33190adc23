import { ElasticLoadBalancingV2Client } from '@aws-sdk/client-elastic-load-balancing-v2';
import { APIGatewayClient } from '@aws-sdk/client-api-gateway';
import { ApiGatewayV2Client } from '@aws-sdk/client-apigatewayv2';
import getConfig from '../config/getConfig.js';
import { REGION } from './const.js';

export const DEV_AWS_JSON_CREDENTIALS = {
  accessKeyId: `${await getConfig('DEV_AWS_ACCESS_KEY_ID')}`,
  secretAccessKey: `${await getConfig('DEV_AWS_SECRET_ACCESS_KEY')}`,
};

export const devAPIGatewayClient = new APIGatewayClient({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});

export const devAPIGatewayV2Client = new ApiGatewayV2Client({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});

export const devElbClient = new ElasticLoadBalancingV2Client(
  {
    region: REGION,
    credentials: DEV_AWS_JSON_CREDENTIALS,
  },
);
