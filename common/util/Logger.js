const LOG_DEBUG = 'DEBUG';
const LOG_INFO = 'INFO';
const LOG_WARN = 'WARN';
const LOG_ERROR = 'ERROR';
const LOG_DEFAULT = LOG_INFO;

/**
 * Builds a json log output from the given parameters and logs to the console along with current AWSContext.
 * Falls back to console.log when run using serverless offline.
 *
 * @private
 * @param message {string} Message to log
 * @param args {any[]} Array of arguments
 * @param context {AWSContext} AWS context
 * @param severity {string} Log level
 * @param error {object} Error information
 */
function _log(message, args, context, severity = LOG_DEFAULT, error = null) {
  const output = {
    severity,
    message,
    ...args.reduce(unnestArgsArray, {}),
    ...error,
    ...context,
  };
  console.log(JSON.stringify(output));
}

/**
 * Log Handler Class
 */
class Logger {
  /**
     * @param context {AWSContext}
     */
  constructor(context) {
    this.context = context;
  }

  /**
     * @param message {string}
     * @param args {...any}
     */
  log(message, ...args) {
    _log(message, args, this.context);
  }

  /**
     * @param message {string}
     * @param args {...any}
     */
  debug(message, ...args) {
    _log(message, args, this.context, LOG_DEBUG);
  }

  /**
     * @param message {string}
     * @param args {...any}
     */
  info(message, ...args) {
    _log(message, args, this.context, LOG_INFO);
  }

  /**
     * @param message {string}
     * @param args {...any}
     */
  warn(message, ...args) {
    _log(message, args, this.context, LOG_WARN);
  }

  /**
     * @param errorCode {string}
     * @param exception {Error}
     * @param message {string}
     * @param args {...any}
     */
  error(errorCode, message, exception, ...args) {
    _log(message, args, this.context, LOG_ERROR, { errorCode, error: parseErrorObject(exception) });
  }
}

function parseErrorObject(error) {
  if (!error) {
    return {};
  }
  return JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error)));
}

function unnestArgsArray(acc, value) {
  if (typeof (value) === 'object') {
    return { ...acc, ...value };
  }

  return { ...acc, [Object.keys(acc).length]: value };
}

export default Logger;
