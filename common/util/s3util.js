import {
  CopyObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { fromUtf8 } from '@aws-sdk/util-utf8-node';
import fs, { readFileSync } from 'fs';
import { pipeline } from 'stream';
import { promisify } from 'util';

export async function uploadText(s3Client, bucket, key, content) {
  const uploadParams = {
    Bucket: bucket,
    Key: key,
    Body: fromUtf8(content),
    Metadata: {
      'cicd-managed': 'true',
    },
  };

  const data = await s3Client.send(new PutObjectCommand(uploadParams));
  return data;
}

export async function uploadFileToS3(s3Client, bucket, localFilePath, s3Key) {
  const fileContent = readFileSync(localFilePath);
  const putCommand = new PutObjectCommand({
    Bucket: bucket,
    Key: s3Key,
    Body: fileContent,
    ContentType: 'text/plain',
  });
  const response = await s3Client.send(putCommand);
  logger.info('File uploaded successfully:', { response });
}

export async function deleteObjectIfManaged(s3Client, bucket, key) {
  const headParams = {
    Bucket: bucket,
    Key: key,
  };

  try {
    const headData = await s3Client.send(new HeadObjectCommand(headParams));
    if (headData.Metadata['cicd-managed'] !== 'true') {
      throw new Error('The specified object is not managed by cicd');
    }
  } catch (error) {
    if (error.name === 'NotFound') {
      return { message: 'Object does not exist or is not accessible.' };
    }
    throw error;
  }

  const deleteParams = {
    Bucket: bucket,
    Key: key,
  };
  const deleteData = await s3Client.send(new DeleteObjectCommand(deleteParams));
  return deleteData;
}

export async function downloadFile(s3Client, bucket, key, fileName) {
  logger.info(`Downloading ${key} from ${bucket} to ${fileName}`);
  const params = {
    Bucket: bucket,
    Key: key,
  };
  const command = new GetObjectCommand(params);
  const response = await s3Client.send(command);
  const asyncPipeline = promisify(pipeline);

  const fileStream = fs.createWriteStream(fileName);
  return asyncPipeline(response.Body, fileStream);
}

async function listFilesInFolder(s3Client, bucket, prefix) {
  const command = new ListObjectsV2Command({
    Bucket: bucket, Prefix: prefix,
  });
  const response = await s3Client.send(command);
  if (!response.Contents) {
    return [];
  }
  return response.Contents;
}

export async function listAllFiles(s3Client, paths) {
  const allFiles = [];

  for (const s3path of paths) {
    const matches = s3path.match(/s3:\/\/(.*?)\/(.*)/);
    const bucket = matches[1];
    const prefix = matches[2];

    const files = await listFilesInFolder(s3Client, bucket, prefix);
    allFiles.push(...files);
  }

  return allFiles;
}

export async function download(s3Client, bucket, key) {
  const params = {
    Bucket: bucket,
    Key: key,
  };

  return s3Client.send(new GetObjectCommand(params));
}

export async function downloadFileToString(s3Client, bucket, key) {
  const params = {
    Bucket: bucket,
    Key: key,
  };

  const response = await s3Client.send(new GetObjectCommand(params));
  const body = await streamToString(response.Body);
  return body;
}

export async function streamToString(stream) {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks).toString('utf8');
}

export async function streamToBuffer(stream) {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

export async function cloneBucket(s3Client, srcBucket, destBucket, directoriesToClone) {
  for (const dir of directoriesToClone) {
    let continuationToken;
    do {
      const listParams = {
        Bucket: srcBucket,
        Prefix: dir,
        ContinuationToken: continuationToken,
      };

      const listCommand = new ListObjectsV2Command(listParams);
      const listResponse = await s3Client.send(listCommand);

      const copyPromises = listResponse.Contents.map(async (object) => {
        const copyParams = {
          Bucket: destBucket,
          CopySource: `${srcBucket}/${object.Key}`,
          Key: object.Key,
        };

        const copyCommand = new CopyObjectCommand(copyParams);
        await s3Client.send(copyCommand);
      });

      await Promise.all(copyPromises);

      continuationToken = listResponse.NextContinuationToken;
    } while (continuationToken);
  }
}
