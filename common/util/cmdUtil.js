import { exec, execSync } from 'child_process';
import { sleep } from './sleepUtil.js';
import getConfig from '../config/getConfig.js';

export function runCmd(command) {
  try {
    logger.info(command);
    return exec(command).toString();
  } catch (error) {
    throw new Error(error.stderr.toString());
  }
}

export function runCmdSync(command) {
  try {
    logger.info('runCmdSync', { command });
    const result = execSync(command, { stdio: 'pipe' }).toString();
    logger.info('runCmdSync', { result });
    return result;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('stdout', error.stdout.toString());
    // eslint-disable-next-line no-console
    console.log('stderr', error.stderr.toString());
    throw new Error(error.stderr.toString());
  }
}

export async function runCmdSyncWithRetry(command, message, maxRetries = 3, retryInterval = 60000) {
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      attempt++;
      logger.info(`Attempt ${attempt}: Running command...`);
      const result = runCmdSync(command);
      logger.info('Command executed successfully');
      return result;
    } catch (error) {
      if (error.message.includes(message)) {
        logger.info(`Encountered "${message}". Waiting ${retryInterval} ms before retrying...`);
        if (attempt < maxRetries) {
          await sleep(retryInterval); // Sleep for 1 minute
        } else {
          logger.info(`Max retries (${maxRetries}) reached. Exiting.`);
          return ''; // Exit the function if max retries are reached
        }
      } else {
        throw error;
      }
    }
  }
}

export async function runRemoteLinuxCmd(cmd) {
  const user = await getConfig('SSH_USERNAME');
  const password = await getConfig('SSH_PASSWORD');
  const server = await getConfig('SSH_SERVER');
  const sshConfig = `sshpass -p ${password} ssh ${user}@${server}`;
  const command = `${sshConfig} "${cmd}"`;
  logger.info(command);
  return runCmd(command);
}

export async function runRemoteLinuxCmdSync(cmd) {
  const user = await getConfig('SSH_USERNAME');
  const password = await getConfig('SSH_PASSWORD');
  const server = await getConfig('SSH_SERVER');
  const sshConfig = `sshpass -p ${password} ssh ${user}@${server}`;
  const command = `${sshConfig} "${cmd}"`;
  logger.info('runRemoteLinuxCmdSync', { command });
  return runCmdSync(command);
}

export async function runRemotePowerShellCmd(cmd) {
  const user = await getConfig('SSH_USERNAME');
  const password = await getConfig('SSH_PASSWORD');
  const server = await getConfig('SSH_SERVER');
  const sshConfig = `sshpass -p ${password} ssh ${user}@${server}`;
  const command = `${sshConfig} 'powershell -ExecutionPolicy Bypass -Command "${cmd}"'`;
  return runCmdSync(command);
}

export function runK8sCmdSync(k8sIp, cmd) {
  const k8sCmd = `ssh -i cicd-managed.pem -o StrictHostKeyChecking=no ubuntu@${k8sIp} '${cmd}'`;
  return runCmdSync(k8sCmd);
}

export async function scpFromQivs(server, source, target) {
  const { USERNAME: user, PASSWORD: password } = await getConfig('CICD_QIVS');
  const command = `sshpass -p '${password}' scp -o StrictHostKeyChecking=no ${user}@${server}:${source}  ${target} `;
  logger.info(command);
  return runCmdSync(command);
}

export async function scpToQivs(server, source, target) {
  const { USERNAME: user, PASSWORD: password } = await getConfig('CICD_QIVS');
  const command = `sshpass -p '${password}' scp -o StrictHostKeyChecking=no ${source}  ${user}@${server}:${target}`;
  logger.info(command);
  return runCmdSync(command);
}

export async function runRemoteLinuxCmdOnQivs(server, cmd) {
  const { USERNAME: user, PASSWORD: password } = await getConfig('CICD_QIVS');
  const sshConfig = `sshpass -p '${password}' ssh ${user}@${server}`;
  const command = `${sshConfig} "${cmd}"`;
  logger.info(command);
  return runCmdSyncWithRetry(command, 'Connection reset');
}

export async function runRemotePowerShellCmdOnQivs(server, cmd) {
  const { USERNAME: user, PASSWORD: password } = await getConfig('CICD_QIVS');
  const sshConfig = `sshpass -p '${password}' ssh ${user}@${server} -o StrictHostKeyChecking=no `;
  const command = `${sshConfig} "powershell -ExecutionPolicy Bypass -Command \\"${cmd}\\""`;
  return runCmdSyncWithRetry(command, 'timed out');
}

export async function runRemotePowerShellSingleQuoteCmdOnQivs(server, cmd) {
  const { USERNAME: user, PASSWORD: password } = await getConfig('CICD_QIVS');
  const sshConfig = `sshpass -p '${password}' ssh ${user}@${server} -o StrictHostKeyChecking=no `;
  const command = `${sshConfig} 'powershell -ExecutionPolicy Bypass -Command "${cmd}"'`;
  return runCmdSync(command);
}

export async function runRemotePowerShellSingleQuoteCmdOnCicd(cmd) {
  const server = await getConfig('SSH_SERVER');
  const user = await getConfig('SSH_USERNAME');
  const password = await getConfig('SSH_PASSWORD');
  const sshConfig = `sshpass -p '${password}' ssh ${user}@${server} -o StrictHostKeyChecking=no `;
  const command = `${sshConfig} 'powershell -ExecutionPolicy Bypass -Command "${cmd}"'`;
  return runCmdSync(command);
}

export async function joinDomainToDev(server) {
  const {
    HOST: host, USERNAME: username, PASSWORD: password,
  } = await getConfig('QV_DEV');
  const credential = getDomainJoinCredential(username, password);
  try {
    await runRemotePowerShellSingleQuoteCmdOnQivs(
      server,
      `Add-Computer -DomainName "${host}" -Credential ${credential}`.replace(/"/g, '\\"'),
    );
  } catch (e) {
    if (e.message.includes('already in that domain.')) {
      logger.info('already in domain');
      return false;
    }
    throw e;
  }
  return true;
}

export async function unJoinDomainFromDev(server) {
  const {
    USERNAME: username, PASSWORD: password,
  } = await getConfig('QV_DEV');
  const credential = getDomainJoinCredential(username, password);
  try {
    await runRemotePowerShellSingleQuoteCmdOnQivs(
      server,
      `Add-Computer -WorkgroupName "WORKGROUP" -Force -Credential ${credential}`.replace(/"/g, '\\"'),
    );
  } catch (e) {
    if (e.message.includes('it is already in that workgroup')) {
      logger.info('already in workgroup');
      return;
    }
    throw e;
  }
}

export async function reNameComputer(server, newName) {
  try {
    await runRemotePowerShellCmdOnQivs(server, `Rename-Computer -NewName "${newName}" -Force`.replace(/"/g, '\\"'));
  } catch (e) {
    if (e.message.includes('the new name is the same as the current name.')) {
      logger.info('already renamed');
      return false;
    }
    throw e;
  }
  return true;
}

export function getDomainJoinCredential(username, password) {
  return `(New-Object System.Management.Automation.PSCredential ("${username}", (ConvertTo-SecureString "${password}" -AsPlainText -Force)))`;
}

export async function getQvUsers() {
  const {
    USERNAME: username, PASSWORD: password,
  } = await getConfig('QV_DEV');
  const credential = getDomainJoinCredential(username, password);

  return runRemotePowerShellSingleQuoteCmdOnCicd(
    `Get-ADGroupMember -Identity "Developers" -Server 'qv.co.nz' -Credential ${credential}`.replace(/"/g, '\\"'),
  );
}

export async function getQvUserDetails(name) {
  const {
    USERNAME: username, PASSWORD: password,
  } = await getConfig('QV_DEV');
  const credential = getDomainJoinCredential(username, password);

  return runRemotePowerShellSingleQuoteCmdOnCicd(
    `Get-ADUser -Identity "${name}" -Server 'qv.co.nz' -Credential ${credential}`.replace(/"/g, '\\"'),
  );
}
