import {
  DeleteItemCommand, DynamoDBClient, QueryCommand, ScanCommand,
} from '@aws-sdk/client-dynamodb';
import {
  DEPLOYMENT_BUILDS, DEPLOYMENTS, REGION, TEST_REPORTS,
} from './const.js';

export const dynamoDbClient = new DynamoDBClient({
  region: REGION,
});

export async function deleteLockItems(env) {
  logger.log('Deleting lock items...');
  let previousKey = null;
  do {
    const scanParams = {
      TableName: 'terraform-state-lock',
      FilterExpression: 'contains(LockID, :LockID)',
      ExpressionAttributeValues: {
        ':LockID': { S: `/${env}/` },
      },
      ExclusiveStartKey: previousKey,
    };
    const scanCommand = new ScanCommand(scanParams);
    const scanResult = await dynamoDbClient.send(scanCommand);

    for (const item of scanResult.Items) {
      const deleteParams = {
        TableName: 'terraform-state-lock',
        Key: { LockID: item.LockID },
      };
      const deleteCommand = new DeleteItemCommand(deleteParams);
      await dynamoDbClient.send(deleteCommand);
    }
    previousKey = scanResult.LastEvaluatedKey;
  } while (previousKey);
  logger.log('Lock items deleted.');
}

export async function deleteDeploymentItems(env, type) {
  logger.log('Deleting deployment items...');
  let previousKey = null;
  do {
    const queryParams = {
      TableName: DEPLOYMENTS,
      IndexName: 'EnvIndex',
      KeyConditionExpression: 'Env = :envValue',
      ExpressionAttributeValues: {
        ':envValue': { S: env },
      },
      ExclusiveStartKey: previousKey,
    };

    const queryCommand = new QueryCommand(queryParams);
    const queryResult = await dynamoDbClient.send(queryCommand);

    if (!queryResult.Items) {
      break;
    }
    const filteredItems = queryResult.Items.filter((item) => item.Type?.S === type);

    for (const item of filteredItems) {
      const deleteParams = {
        TableName: DEPLOYMENTS,
        Key: {
          DeploymentId: item.DeploymentId,
        },
      };
      const deleteCommand = new DeleteItemCommand(deleteParams);
      await dynamoDbClient.send(deleteCommand);
    }

    previousKey = queryResult.LastEvaluatedKey;
  } while (previousKey);
  logger.log('Deployment items deleted.');
}

export async function deleteReportItems(env) {
  logger.log('Deleting report items...');
  let previousKey = null;

  do {
    const scanParams = {
      TableName: TEST_REPORTS,
      FilterExpression: 'contains(Env, :envValue)',
      ExpressionAttributeValues: {
        ':envValue': { S: env },
      },
      ExclusiveStartKey: previousKey,
    };

    const scanCommand = new ScanCommand(scanParams);
    const scanResult = await dynamoDbClient.send(scanCommand);

    if (!scanResult.Items) {
      logger.log('No items found to delete.');
      break;
    }

    for (const item of scanResult.Items) {
      const deleteParams = {
        TableName: TEST_REPORTS,
        Key: {
          ReportId: item.ReportId,
        },
      };
      const deleteCommand = new DeleteItemCommand(deleteParams);
      await dynamoDbClient.send(deleteCommand);
    }

    previousKey = scanResult.LastEvaluatedKey;
  } while (previousKey);
  logger.log('Report items deleted.');
}

export async function deleteDeploymentBuilds(envName, type) {
  logger.log('Deleting deployment builds...');
  let previousKey = null;
  do {
    const scanParams = {
      TableName: DEPLOYMENT_BUILDS,
      FilterExpression: '#env = :envName',
      ExpressionAttributeNames: {
        '#env': 'Env',
      },
      ExpressionAttributeValues: {
        ':envName': { S: envName },
      },
      ExclusiveStartKey: previousKey,
    };

    const scanCommand = new ScanCommand(scanParams);
    const scanResult = await dynamoDbClient.send(scanCommand);
    if (!scanResult.Items) {
      logger.log('No items found to delete.');
      break;
    }
    const filteredItems = scanResult.Items.filter((item) => item.Type?.S === type);
    for (const item of filteredItems) {
      const deleteParams = {
        TableName: DEPLOYMENT_BUILDS,
        Key: {
          Key: item.Key,
        },
      };
      const deleteCommand = new DeleteItemCommand(deleteParams);
      await dynamoDbClient.send(deleteCommand);
    }

    previousKey = scanResult.LastEvaluatedKey;
  } while (previousKey);
  logger.log('Deployment builds deleted.');
}

export async function deleteDynamodb(envName, type) {
  await deleteReportItems(envName);
  await deleteDeploymentItems(envName, type);
  await deleteDeploymentBuilds(envName, type);
  await deleteLockItems(envName);
}
