import { getAppEnvironmentByName } from '../dal/appEnvironments.js';
import { HEALTHY, INDEXING_FAILED, UNHEALTHY } from '../enums/environmentStatus.js';
import { formatApplications, getApplicationsByEnvAndType } from '../dal/applications.js';
import { ApplicationType } from '../enums/applicationType.js';
import { API_STREAM_APP, RESERVED_APP_ENV_NAMES } from './const.js';
import { isAppHealthy } from '../service/healthCheck.js';
import { ApplicationStack } from '../enums/applicationStack.js';

export async function canPerformReindex(appEnv) {
  if (RESERVED_APP_ENV_NAMES.includes(appEnv.toLowerCase())) {
    return { success: false, message: `AppEnvironment not found for the given name: ${appEnv}.Reindex cannot be performed.` };
  }
  const results = await getAppEnvironmentByName(appEnv);
  if (results.length === 0) {
    return { success: false, message: `AppEnvironment not found for the given name: ${appEnv}.Reindex cannot be performed.` };
  }
  const appEnvironment = results[0];
  const includeMonarch = appEnvironment.Stacks.L.map((it) => it.S).includes(ApplicationStack.MONARCH);
  if (!includeMonarch) {
    return { success: false, message: `Monarch Stack is not included in ${appEnv}. Reindex cannot be performed.` };
  }
  if (![HEALTHY, UNHEALTHY, INDEXING_FAILED].includes(appEnvironment.Status.S)) {
    return { success: false, message: `${appEnv} is not healthy or in failed state. Reindex cannot be performed.` };
  }
  const applications = formatApplications(await getApplicationsByEnvAndType(appEnv, ApplicationType.DAE));
  const apiStreamApp = applications.find((app) => app.name === API_STREAM_APP);
  if (!apiStreamApp) {
    return { success: false, message: `${API_STREAM_APP} is not available in ${appEnv}. ReIndex cannot be performed.` };
  }

  if (!await isAppHealthy(apiStreamApp)) {
    return { success: false, message: `${API_STREAM_APP} is not healthy in ${appEnv}. ReIndex cannot be performed.` };
  }
  return { success: true, message: `Reindex can be performed for ${appEnv}.` };
}
