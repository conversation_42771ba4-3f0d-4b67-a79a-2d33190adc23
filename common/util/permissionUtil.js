// expand '*' wildcard
export function parsePermission(action, resource) {
  const allowedActions = [
    'list',
    'create',
    'read',
    'update',
    'delete',
    'deploy',
  ];
  const allowedResources = [
    'data-env',
    'app-env',
    'dev-aws-account',
    'prod-aws-account',
    'testdata',
    'background-tests',
  ];

  // If no need to expand, return the permission as is
  if (action !== '*' && resource !== '*') {
    return isValidCombination(action, resource) ? [`${action}:${resource}`] : [];
  }

  const expandedActions = action === '*' ? allowedActions : [action];
  const expandedResources = resource === '*' ? allowedResources : [resource];

  // Generate all combinations of expanded actions and resources
  const permissions = [];
  expandedActions.forEach((a) => {
    expandedResources.forEach((r) => {
      if (isValidCombination(a, r)) {
        permissions.push(`${a}:${r}`);
      }
    });
  });

  return permissions;
}

// Additional function to check if a combination is valid
function isValidCombination(action, resource) {
  if (action === 'deploy' && (resource === 'data-env' || resource === 'app-env')) {
    return false;
  }
  return !((action === 'list' || action === 'create' || action === 'read' || action === 'update' || action === 'delete')
        && (resource === 'dev-aws-account' || resource === 'prod-aws-account'));
}

export function expandPermission(storedPermissions) {
  return [...new Set(
    storedPermissions
      .flatMap((permission) => {
        const [action, resource] = permission.split(':');
        return parsePermission(action, resource);
      }),
  )];
}
