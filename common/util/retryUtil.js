import { sleep } from './sleepUtil.js';

/**
 * Retries the execution of a specified asynchronous function until a certain condition is met or the timeout is reached.
 *
 * @param {Function} retryFunction - The asynchronous function to execute. It should return a value that will be evaluated by the endConditionFn.
 * @param {Function} endConditionFunction - A function that takes the result of fn and returns a boolean indicating whether the desired condition has been met.
 * @param {Number} timeoutSeconds - The maximum time (in seconds) to keep retrying the execution of fn.
 * @param {Number} delaySeconds - The delay time (in seconds) between retries if the condition is not met.
 *
 * @returns {Promise<Object>} Resolves with an object containing:
 * - `timeoutReached`: A boolean indicating if the timeout was reached.
 * - `result`: The result of the successful execution of fn (if the condition was met before the timeout).
 */
export async function retryWithDelay(retryFunction, endConditionFunction, timeoutSeconds, delaySeconds) {
  const startTime = Date.now();
  const endTime = startTime + timeoutSeconds * 1000;

  while (Date.now() < endTime) {
    const result = await retryFunction();
    logger.info('retryWithDelay result', { result });
    if (endConditionFunction(result)) {
      return {
        timeoutReached: false,
        result,
      };
    }
    logger.info(`Condition not met. Retrying in ${delaySeconds} seconds...`);
    await sleep(delaySeconds * 1000);
  }

  logger.info('Timeout reached. Exiting...');
  return {
    timeoutReached: true,
  };
}
