import { getPlatform } from '../dal/appStacks.js';
import { Platform } from '../enums/platform.js';
import { DB_SCRIPTS } from './const.js';

export function removeDaeSuffix(appName) {
  if (appName.endsWith('-dae')) {
    return appName.slice(0, -1 * '-dae'.length);
  }
  return appName;
}

export function normalizeName(name) {
  return name.replaceAll('_', '-');
}

export async function getPipelineName(application) {
  let appName = application.Name.S;
  const platform = await getPlatform(appName);
  if (platform === Platform.K8S) {
    appName = `monarch-${appName}`;
  }
  return appName;
}

export async function getDeployPipelineName(application, appEnv) {
  let pipelineName = `${application}-pipeline-${appEnv}`;
  const platform = await getPlatform(application);
  if (platform === Platform.K8S) {
    pipelineName = `monarch-${pipelineName}`;
  }
  pipelineName = normalizeName(pipelineName);
  return pipelineName;
}

export async function getDeployPipelineUrl(application, appEnv) {
  const pipelineName = await getDeployPipelineName(application, appEnv);
  return getPipelineUrl(pipelineName);
}

export function getDbPipelineName(envName, datastore) {
  return `${DB_SCRIPTS}-${datastore}-pipeline-${envName}`;
}

export function getDbPipelineUrl(envName, datastore) {
  const pipelineName = getDbPipelineName(envName, datastore);
  return getPipelineUrl(pipelineName);
}
export function getPipelineUrl(pipelineName) {
  return `https://ap-southeast-2.console.aws.amazon.com/codesuite/codepipeline/pipelines/${pipelineName}/view?region=ap-southeast-2`;
}
