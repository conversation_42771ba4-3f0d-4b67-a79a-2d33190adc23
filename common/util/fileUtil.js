import fs from 'fs';
import fpath from 'path';
import { PG_DATA_PATH } from './const.js';

export function getPgDataFolder(drive) {
  return `${drive}:${PG_DATA_PATH}`;
}

export function readFileToString(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file: ${error}`);
    throw error;
  }
}

export function createFolders(filePath) {
  const parentFolder = fpath.dirname(filePath);
  if (!fs.existsSync(parentFolder)) {
    fs.mkdirSync(parentFolder, { recursive: true });
  }
}

export async function listFiles(dir, filenameFilter, ignoreDirs = ['.terraform']) {
  const files = await fs.promises.readdir(dir);
  const filePaths = [];

  for (const file of files) {
    const filePath = fpath.join(dir, file);
    const stats = await fs.promises.stat(filePath);
    const isFile = stats.isFile();
    const isDirectory = stats.isDirectory();
    if (isFile && filenameFilter(dir, file)) {
      filePaths.push(filePath);
    } else if (isDirectory && !ignoreDirs.includes(file)) {
      const subDirectoryFiles = await listFiles(filePath, filenameFilter);
      filePaths.push(...subDirectoryFiles);
    }
  }
  return filePaths;
}

export function substringAfter(inputString, sequence) {
  const index = inputString.indexOf(sequence);
  if (index === -1) {
    return '';
  }
  return inputString.slice(index + sequence.length);
}

export async function listFilesByModifiedTime(dir) {
  try {
    let files = await fs.promises.readdir(dir);
    files = files.filter((file) => fpath.extname(file) === '.gz');

    const fileStatsPromises = files.map((file) => fs.promises.stat(fpath.join(dir, file)));
    const fileStats = await Promise.all(fileStatsPromises);

    // Pair each file with its stats
    files = files.map((file, index) => ({ file, stats: fileStats[index] }));

    // Sort by last modified time, oldest first
    files.sort((a, b) => a.stats.mtime - b.stats.mtime);

    return files.map((file) => file.file);
  } catch (error) {
    console.error('Error:', error);
    return [];
  }
}

export function deleteFolder(path) {
  try {
    const files = fs.readdirSync(path);
    files.forEach((file) => {
      const filePath = fpath.join(path, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        fs.unlinkSync(filePath);
        logger.info(`File ${filePath} deleted.`);
      } else if (stats.isDirectory()) {
        deleteFolder(filePath);
        fs.rmdirSync(filePath);
        logger.info(`Directory ${filePath} deleted.`);
      }
    });

    fs.rmdirSync(path);
    logger.info(`Directory ${path} deleted.`);
  } catch (err) {
    console.error(`Error occurred: ${err}`);
  }
}

export function writeToFile(text, filePath) {
  try {
    fs.writeFileSync(filePath, text);
    console.log(`File was written successfully to ${filePath}`);
  } catch (err) {
    console.error('Error writing to file:', err);
  }
}
