import { dataStoreTypes } from '../enums/dataStoreTypes.js';
import {
  DEPLOYING,
  ERROR,
  HEALTHY, INDEXING, INDEXING_FAILED,
  PROVISIONING,
  STABLE,
  TEARING_DOWN,
  TESTING, UNHEALTHY,
  UPDATING,
} from '../enums/environmentStatus.js';
import { CmdAppTasks } from '../enums/cmdAppTasks.js';

export const REGION = 'ap-southeast-2';

export const DATA_ENVIRONMENTS = 'data_environments';
export const APP_ENVIRONMENTS = 'app_environments';
export const DATASTORES = 'datastores';
export const APPLICATIONS = 'applications';
export const BUILDS = 'builds';
export const TF_SCRIPTS = 'tf_scripts';
export const PODS_CONFIGURATION = 'pods_configuration';
export const APP_STACKS = 'app_stacks';
export const DATASTORE_STACKS = 'datastore_stacks';
export const DEPLOYMENTS = 'deployments';
export const DEPLOYMENT_BUILDS = 'deployment_builds';
export const TEST_REPORTS = 'test_reports';

export const ERROR_CODES = {
  INVALID_OPTIONS: {
    code: 'ERR-CICD-CMD-000',
    message: 'Invalid cmd options.',
  },
  SQL_AGENT_JOB_ERROR: {
    code: 'ERR-CICD-CMD-001',
    message: 'QVNZ sql agent job Exception, please check the jobhistory in the SSMS',
  },
  RUN_JOB_STATUS_CHECK: {
    code: 'ERR-CICD-CMD-002',
    message: 'Job Status Check Exception.',
  },
  UNKNOWN_JOB_STATUS: {
    code: 'ERR-CICD-CMD-003',
    message: 'Unknown job status, please investigate.',
  },
  NO_ENV_FOUND: {
    code: 'ERR-CICD-CMD-004',
    message: 'No data environment found.',
  },
  LAST_RESTORE_NOT_FINISHED: {
    code: 'ERR-CICD-CMD-005',
    message: 'Previous restore job not finished.',
  },
  DATABASE_CONNECTION_ERROR: {
    code: 'ERR-CICD-CMD-006',
    message: 'MSSQL connection fail.',
  },
  LAST_DESTROY_NOT_FINISHED: {
    code: 'ERR-CICD-CMD-007',
    message: 'Previous destroy job not finished.',
  },
  LAST_REFRESH_NOT_FINISHED: {
    code: 'ERR-CICD-CMD-008',
    message: 'Previous refresh job not finished.',
  },
  BAK_FILE_NOT_EXIST: {
    code: 'ERR-CICD-CMD-009',
    message: 'MSSQL bak file not exist.',
  },
  MSSQL_DB_ERROR: {
    code: 'ERR-CICD-CMD-010',
    message: 'MSSQL DB error.',
  },
  RESTORE_TERMINATED_ABNORMALLY: {
    code: 'ERR-CICD-CMD-011',
    message: 'MSSQL restore terminated abnormally.',
  },
  BAK_FILE_ERROR: {
    code: 'ERR-CICD-CMD-012',
    message: 'MSSQL bak file error.',
  },
  FILE_NOT_EXIST: {
    code: 'ERR-CICD-CMD-013',
    message: 'file not exist.',
  },
  RESTORE_PG_DUMP_ERROR: {
    code: 'ERR-CICD-CMD-014',
    message: 'PG dump restore error.',
  },
  PG_DB_ERROR: {
    code: 'ERR-CICD-CMD-015',
    message: 'PG DB error.',
  },
  NO_APP_ENV_FOUND: {
    code: 'ERR-CICD-CMD-016',
    message: 'No app environment found.',
  },
  INVALID_SYNONYM_ERROR: {
    code: 'ERR-CICD-CMD-017',
    message: 'not able to recreate synonym',
  },
  NO_DEPLOYMENT_FOUND: {
    code: 'ERR-CICD-CMD-018',
    message: 'No deployment found.',
  },
  APP_ENV_VERIFY_TIMEOUT: {
    code: 'ERR-CICD-CMD-019',
    message: 'App env verify timeout.',
  },
  NO_DATASTORE_FOUND: {
    code: 'ERR-CICD-CMD-020',
    message: 'No datastore found.',
  },
  SQL_ERROR: {
    code: 'ERR-CICD-CMD-021',
    message: 'SQL error.',
  },

  FILE_NOT_E: {
    code: 'ERR-CICD-CMD-021',
    message: 'SQL error.',
  },
  UNKNOWN_ERROR: {
    code: 'ERR-CICD-CMD-100',
    message: 'Unknown error, please investigate.',
  },
};

export const DYNAMIC_DB_BAK_FILE_LOCATION = new Map([
  ['report_generator', 'V:\\MSSQL\\BACKUP\\report_generator\\report_generator_backup.bak'],
  ['qv_apihub', 'V:\\MSSQL\\BACKUP\\qv_apihub\\qv_apihub.BAK'],
  ['qvnz_history', 'V:\\MSSQL\\BACKUP\\qvnz_history_bau\\qvnz_history_bau_backup.bak'],
  ['qvcostbuilder', 'V:\\MSSQL\\BACKUP\\qvcostbuilder\\qvcostbuilder.bak'],
  ['qvnz', 'V:\\MSSQL\\BACKUP\\qvnz_bau\\qvnz_bau_backup.bak'],
  ['qvnz_mapping', 'V:\\MSSQL\\BACKUP\\QV\\QV.BAK'],
]);

export const QVNZ_PUBLIC = 'qvnz_public';
export const STATIC_DB_BAK_FILE_LOCATION = new Map([
  ['qvnz_resources', 'V:\\MSSQL\\BACKUP\\qvnz_resources\\qvnz_resources.BAK'],
  ['infobase_main', 'V:\\MSSQL\\BACKUP\\inforbase_main\\infobase_main_backup.bak'],
]);

export const RECREATE_SYNONYM_DB_NAMES = ['qvnz', 'qvnz_history', 'qvnz_prod'];

export const DB_SCRIPTS = 'db-scripts';
export const DB_SCRIPTS_GIT_REPO = `https://github.com/Quotable-Value/${DB_SCRIPTS}`;
export const DB_SCRIPTS_FOLDER = `${DB_SCRIPTS}/cicd`;

export const QVNZ_REFRESH_JOB_NAME = 'Website Daily Refresh';

export const REFRESH_INTERVAL_IN_MILLISECONDS = 60 * 1000;

export const ES_TIMEOUT_IN_MILLISECONDS = 2 * 60 * 60 * 1000;

export const PG_BACKUP_FOLDER = 'PostgreSQL';
export const PG_BACKUP_PATH = `V:/${PG_BACKUP_FOLDER}`;

export const MSSQL_LOG_PATH = '\\MSSQL\\LOG';
export const MSSQL_DATA_PATH = '\\MSSQL\\DATA';
export const MSSQL_INDEXES_PATH = '\\MSSQL\\INDEXES';
export const MSSQL_PATHS = [MSSQL_LOG_PATH, MSSQL_DATA_PATH, MSSQL_INDEXES_PATH];

export const PG_DATA_PATH = '\\PGSQL\\DATA';

export const CICDDB01_EC2_ID = 'i-01fce2f2bfdda3760';
export const CICDDB01_IP = '************';

export const DYNAMIC_DEVICE_PREFIX = 'xvd';

export const dataStoreNameToVolumeSize = new Map([
  [dataStoreTypes.QIVS, 1300],
  [dataStoreTypes.PGMonarch, 700],
]);

export const RESERVED_DATA_ENV_NAMES = ['dev', 'test', 'uat', 'preprod', 'prod', 'tmp'];
export const RESERVED_APP_ENV_NAMES = ['cicd', 'dev', 'test', 'uat', 'preprod', 'prod', 'tmp'];
export const MAX_DATA_ENVIRONMENTS = 5;
export const MAX_APP_ENVIRONMENTS = 20;
export const dataStoreNameToRestoreCmd = new Map([
  [dataStoreTypes.QIVS, CmdAppTasks.QVNZ_RESTORE],
  [dataStoreTypes.PGMonarch, CmdAppTasks.PG_RESTORE],
]);

export const DEV_TF_BUCKET_PREFIX = 'dev/dynamic-environments';
export const CICD_TF_BUCKET_PREFIX = 'cicd/dynamic-environments';

export const TF_APP_TEMPLATE_FOLDER = 'tf/app_template';
export const TF_DATA_TEMPLATE_FOLDER = 'tf/data_template';
export const DB_PASSWORD_LENGTH = 16;
export const PG_USER = 'pg_user';

export const PG_EXTENSIONS = ['pg_buffercache', 'pg_stat_statements', 'pgstattuple', '"uuid-ossp"'];

export const MONARCH_K8S_BUCKET = 'monarch-kubernetes-deployment-dev';
export const K8S_CONFIG = 'kubeconfig.yml';

export const AWS_ACCOUNTS = ['dev', 'cicd'];

export const QIVS_USER = 'qivs_user';
export const MSSQL_USER_ROLE = new Map([
  ['monarch_services', 'monarch_services_role'],
  ['qvapihub_user', 'qvapihub_user_role'],
  ['kaba_services', 'kaba_services_role'],
  ['report_generator_user', 'report_generator_role'],
  ['qvcostbuilder_app_user', 'qvcostbuilder_app_role'],
]);
export const MSSQL_DB_ROLES = ['db_owner'];

export const DEPLOY_BUCKET_NAME = 'qv-deployment';
export const TMP_FOLDER = '/tmp';
export const ARTIFACT_FILE_NAME = 'deploy.zip';
export const CONFIG_SAMPLE_NAME = 'config.sample.json';
export const INFRA_SAMPLE_PATH = 'infra/terraform.tfvars.sample';
export const DIFF_SUMMARY = 'changed_sql_files.txt';
export const SHA = 'sha.txt';

export const DEV_ACCOUNT = '************';
export const PROD_ACCOUNT = '************';
export const AWS_ACCOUNTS_MAPPING = new Map([
  ['dev', DEV_ACCOUNT],
]);

export const DAE_HOST_ZONE_ID = 'Z04875672MYF9I580X4C4';
export const DDE_HOST_ZONE_ID = 'Z04436693CY5EGM8AVV0K';

export const DB_SCRIPTS_ORDER = ['roles', 'data-types', 'types', 'schemas', 'tables', 'synonyms', 'views', 'triggers', 'seed'];

export const MONITOR_PIPELINE_STATUS_TIMEOUT = 60 * 60 * 1000;

export const DEPLOYMENT_REPORTS_BUCKET = 'qv-deployment-reports';

export const DEFAULT_APP_STATUS_FOR_SEARCH = [STABLE, PROVISIONING, DEPLOYING, TESTING, HEALTHY, UNHEALTHY, INDEXING, INDEXING_FAILED, ERROR, TEARING_DOWN, UPDATING];

export const API_STREAM_APP = 'api-stream';

export const NO_TEST = 'no test';

export const DEFAULT_PAGE_SIZE = '25';

export const RDS_ACCESS_ROLE = 'CICD-Service-User-RDS-Access';
export const PROD_RDS_ROLE = `arn:aws:iam::${PROD_ACCOUNT}:role/${RDS_ACCESS_ROLE}`;

export const QVCONZ_DB_NAME = 'qvconz';
export const PROPERTY_SERVICE_DB_NAME = 'property_service';
export const DB_SNAPSHOTS = new Map([
  [QVCONZ_DB_NAME, 'qvconz'],
  [PROPERTY_SERVICE_DB_NAME, 'monarch'],
]);

export const BASE_BUILD_URL = 'https://ap-southeast-2.console.aws.amazon.com/codesuite/codebuild/************/projects';

export const TF_ALLOWED_EXTENSIONS = ['.tf', '.sh', '.sample', '.yml'];

export const QV_CONTENT_DIRECTORIES = [
  'static/',
  'media/images/',
  'media/original_images/',
  'media/documents/',
];

export const QV_CONTENT_BUCKET_DAE = 'daeqvconz-content';
export const QV_CONTENT_BUCKET_PROD = 'qvconz-content';

export const QVCONZ_DAE_HOST = 'qvconz.internal.quotablevalue.co.nz';

export const PROD_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const REINDEXER_QUEUE_NAME = 'os-reindexer-queue';

export const QIVS_EX = 'EXDAE01';
export const QIVS_IN = 'INDAE01';
export const DEFAULT_QIVS_ENV = 'uat';

export const QIVS_EXTERNAL_DOMAINS = [
  '.quotable.co.nz',
  '.acuity.org.nz',
  '.acuity.external.quotablevalue.co.nz',
  '.qivs.external.quotablevalue.co.nz',
];

export const hashedPassword = 'pbkdf2_sha256$260000$OsJQF9ZIoCReD8dnVyaetN$QXXdNOwdyC6U60a3UWVCrXulJfzhVCd+7HysJgrrlhU=';
