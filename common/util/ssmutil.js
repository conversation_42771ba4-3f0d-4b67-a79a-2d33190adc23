import { DeleteParameterCommand, GetParameterCommand, PutParameterCommand } from '@aws-sdk/client-ssm';
import { normalizeName } from './appNameUtli.js';

const CICD = {
  Key: 'CICD',
  Value: 'true',
};

export async function getParameter(client, parameterName) {
  try {
    const getCommand = new GetParameterCommand({ Name: parameterName, WithDecryption: true });
    const { Parameter } = await client.send(getCommand);
    return Parameter.Value;
  } catch (err) {
    if (err.name === 'ParameterNotFound') {
      return false;
    }
    logger.error(err);
    throw err;
  }
}

export async function createParameter(client, parameterName, value, costCentre) {
  const input = {
    Name: parameterName,
    Value: value,
    Type: 'String',
    Tags: [
      CICD,
      {
        Key: 'Cost Centre',
        Value: costCentre,
      },
    ],
  };
  logger.info('createParameter', { input });
  const putCommand = new PutParameterCommand(input);
  const response = await client.send(putCommand);
  logger.info(response);
  return response;
}

export async function createAdvancedParameter(client, parameterName, value, costCentre) {
  const putCommand = new PutParameterCommand({
    Name: parameterName,
    Value: value,
    Type: 'String',
    Tags: [
      CICD,
      {
        Key: 'Cost Centre',
        Value: costCentre,
      },
    ],
    Tier: 'Advanced',
  });
  const response = await client.send(putCommand);
  logger.info(response);
  return response;
}

export async function createAdvancedSecureParameter(client, parameterName, value, costCentre) {
  const putCommand = new PutParameterCommand({
    Name: parameterName,
    Value: value,
    Type: 'SecureString',
    Tags: [
      CICD,
      {
        Key: 'Cost Centre',
        Value: costCentre,
      },
    ],
    Tier: 'Advanced',
  });
  const response = await client.send(putCommand);
  logger.info(response);
  return response;
}

export async function mergeAndUpdateAdvancedParameter(client, parameterName, newValues) {
  try {
    const getCommand = new GetParameterCommand({ Name: parameterName });
    const { Parameter } = await client.send(getCommand);
    const existingJsonConfig = JSON.parse(Parameter.Value);
    const mergedValue = JSON.stringify({ ...existingJsonConfig, ...newValues }, null, 4);
    logger.info(`dae context config length ${mergedValue.length}`);
    const putCommand = new PutParameterCommand({
      Name: parameterName,
      Value: mergedValue,
      Type: 'String',
      Tier: 'Advanced',
      Overwrite: true, // Set to true to update the existing parameter
    });
    const response = await client.send(putCommand);
    logger.info(response);
  } catch (err) {
    logger.error(err);
    throw err;
  }
}

export async function deleteParameter(client, parameterName) {
  const command = new DeleteParameterCommand({ Name: parameterName });
  try {
    const response = await client.send(command);
    logger.info(response);
  } catch (err) {
    logger.error(err);
    throw err;
  }
}

export async function checkParameterExistence(client, parameterName) {
  try {
    const getCommand = new GetParameterCommand({ Name: parameterName });
    await client.send(getCommand);
    return true;
  } catch (err) {
    if (err.name === 'ParameterNotFound') {
      return false;
    }
    logger.error(err);
    throw err;
  }
}

export function testConfigParameterName(appEnv, application) {
  return `/${normalizeName(appEnv)}/${application}-test-config`;
}

export function tfConfigParameterName(appEnv, application) {
  return `/${normalizeName(appEnv)}/${application}/tfvars`;
}
