<template>
    <div>
        <h1>QV Deployment!</h1>
        <transition name="toast">
            <Toast
                v-if="showToast"
                :message="toastMessage"
                :toastColor="toastColor"
            />
        </transition>
        <p v-if="!selectedPrefix">Select a project!</p>
        <p v-if="!selectedDeploy && selectedPrefix">Select a branch!</p>

        <div class="deploymentContainer">
            <select v-model="selectedPrefix" @change="getBranchesFromPrefix">
                <option value="null" disabled selected hidden>
                    Select a Project
                </option>
                <option v-for="item in prefixesData" :key="item" :value="item">
                    {{ item.Prefix.replace('/', '') }}
                </option>
            </select>
            <br />

            <!-- // NOTE: select model that doesn't display -->
            <!-- // REFACTOR: Can be deleted as it has served its purpose -->
            <select v-model="selectedDeploy" v-if="selectedPrefix && false">
                <option value="null" disabled selected hidden>
                    Select a branch
                </option>
                <option
                    v-for="item in filteredBranches"
                    :key="item"
                    :value="item"
                >
                    {{
                        `${item.Key.split('/')[1]}/${item.Key.split('/')[2]} ${
                            item.LastModified.split('T')[0]
                        } ${item.LastModified.split('T')[1].substring(
                            0,
                            item.LastModified.split('T')[1].length - 5
                        )}`
                    }}
                </option>
            </select>
            <table v-if="selectedPrefix && filteredBranches.length > 0">
                <th
                    v-for="col in columns"
                    :key="col"
                    @click="
                        sortTableControl(
                            col === columns[0] ? 'Key' : 'LastModified'
                        )
                    "
                >
                    {{ col }}
                    <div
                        class="arrow"
                        v-if="
                            (col === columns[0] ? 'Key' : 'Last Modified') ==
                            sortedColumn
                        "
                        :class="ascending ? 'arrow_up' : 'arrow_down'"
                    ></div>
                </th>

                <!-- // * NOTE: table row structured for only two columns: key and last modified
                    // * for more columns in the future, would require some refactoring                
                 -->

                <tr
                    v-for="item in filteredBranches"
                    :key="item"
                    :value="item"
                    @click="setSelectedDeploy(item)"
                    :class="{ selected: selectedDeploy === item }"
                >
                    <td v-for="col in columns" :key="col">
                        <div v-if="col == columnBranchName">
                            <!-- NOTE: debug line (show entire Key) -->
                            <!-- {{ item.Key }} -->
                            <!-- NOTE: ideally shows only branch and version, but only works in ideal scenarios -->
                            <!-- {{
                                `${item.Key.split('/')[1]}/${
                                    item.Key.split('/')[2]
                                }`
                            }} -->
                            <!-- NOTE: removes prefix, and removes deploy.zip, from path -->
                            <!-- {{
                                `${item.Key.split('/')
                                    .slice(1)
                                    .join('/')
                                    .replace('deploy.zip', '')}`
                            }} -->
                            <!--// NOTE: Always shows Branch, then conditionally shows branch 
                                    doesn't have version number if not present
                             -->
                            {{
                                `
                                ${
                                    item.Key.replace('deploy.zip', '').split(
                                        '/'
                                    )[1]
                                }
                                /
                                ${
                                    item.Key.replace('deploy.zip', '').split(
                                        '/'
                                    )[2] != ''
                                        ? item.Key.replace(
                                              'deploy.zip',
                                              ''
                                          ).split('/')[2]
                                        : ''
                                }
                                `
                            }}
                        </div>
                        <!-- // # LAST MODIFIED -->
                        <div v-else>
                            {{ item.LastModified }}
                            <!-- {{
                                `${
                                    item.LastModified.split('T')[0]
                                } ${item.LastModified.split('T')[1].substring(
                                    0,
                                    item.LastModified.split('T')[1].length - 5
                                )}`
                            }} -->
                        </div>
                    </td>
                </tr>
            </table>
            <div v-else-if="filteredBranches.length === 0 && selectedPrefix">
                No deployments available for
                {{ selectedPrefix.Prefix.replace('/', '') }}
            </div>
            <!-- // TODO: display loading spinner here while deploying -->
            <div class="loader" v-if="loading"></div>
            <button
                class="btn first"
                v-if="
                    filteredBranches.length != 0 &&
                    filteredBranches != undefined
                "
                @click="deploy"
            >
                Deploy branch
            </button>
            <button
                class="btn second"
                v-if="selectedDeploy && filteredBranches.length > 0"
                @click="deleteDeploy"
            >
                Delete branch
            </button>
            <Modal
                v-if="showModal"
                @close="showModal = false"
                :headerMessage="headerMessage"
                :bodyMessage="bodyMessage"
                :footerMessage="footerMessage"
            />
        </div>
    </div>
</template>

<script>
import { ref, computed } from '@vue/reactivity';
import axios from 'axios';
import getOptions from '../composables/getOptions';
import sortTable from '../composables/sortTable';
import { useRoute } from 'vue-router';
import { onMounted } from '@vue/runtime-core';
import moment from 'moment-timezone';

import Modal from '../components/Modal.vue';
import Toast from '../components/Toast';
const config = window.siteConfig;

export default {
    name: 'Deployment',
    components: { Modal, Toast },
    props: ['contentData', 'prefixesData', 'prefix'],
    setup(props) {
        // * declare variables
        const ascending = ref(false);
        const loading = ref(false);

        const showModal = ref(false);
        const headerMessage = ref('Congrats');
        const bodyMessage = ref('You deployed the build!');
        const footerMessage = ref('close');

        const showToast = ref(false);
        const toastMessage = ref('');
        const greenSuccess = 'greenSuccess';
        const redFail = 'redFail';
        const toastColor = ref(greenSuccess);
        const triggerToast = (messageVal, colorVal) => {
            showToast.value = true;
            toastMessage.value = messageVal;
            toastColor.value = colorVal;
            setTimeout(() => (showToast.value = false), 3000);
        };

        const columnBranchName = ref('Branch / Version');
        const columns = ref([columnBranchName.value, 'Last Modified']);
        const sortedColumn = ref('');

        const selectedPrefix = ref(null);
        const selectedDeploy = ref(null);

        const branchOptions = ref([]);

        const sortTableControl = (col) => {
            if (branchOptions.value === undefined) return;
            branchOptions.value = sortTable(
                col,
                ascending.value,
                branchOptions.value
            );
            ascending.value = !ascending.value;
            sortedColumn.value = col === 'LastModified' ? 'Last Modified' : col;
        };

        /* // * conditions for filter:
         * End in deploy.zip
         * Not end in undefined
         */
        const filteredBranches = computed(() => {
            if (branchOptions.value === undefined) {
                return [];
            }
            return branchOptions.value
                .filter((elem) => elem.Key.slice(-10) === 'deploy.zip')
                .filter((elem) => elem.Key.split('/')[2] != undefined);
        });

        const getBranchesFromPrefix = () => {
            const inputUrl =
                config.apiUrl + `/branches/${selectedPrefix.value.Prefix}`;
            loading.value = true;
            axios(getOptions('GET', inputUrl)).then((response) => {
                loading.value = false;
                branchOptions.value = response.data.contentData;
                branchOptions.value = convertTimesToNZTime(branchOptions.value);
                setSelectedDeploy(null);
                // * sort tables on load and update values, create new function to do these three options
                ascending.value = false;
                sortTableControl('LastModified');
            });
        };

        const convertTimesToNZTime = (input) => {
            return input.map((elem) => {
                const lastModifiedDate = moment(elem.LastModified);
                elem.LastModified = lastModifiedDate
                    .tz('Pacific/Auckland')
                    .format('YYYY-MM-DD HH:mm:ss');
                return elem;
            });
        };

        // * Get Prefix from url parameter

        const route = useRoute();

        // ! HACK: to get prefix from url to initialize after data has loaded
        onMounted(() => {
            let run;
            run = setInterval(() => {
                if (props.prefixesData.length === 0) return;

                const processParams = () => {
                    // * get matching prefix
                    const matchingElem = props.prefixesData.filter(
                        (e) =>
                            e.Prefix.substring(0, e.Prefix.length - 1) ===
                            route.params.prefix
                    )[0];
                    if (!matchingElem) return;

                    // * If matching prefix
                    selectedPrefix.value = matchingElem;
                    getBranchesFromPrefix();
                    if (
                        route.params.branch === '' ||
                        route.params.branch === null
                    )
                        return;

                    // console.log(route.params.branch);
                    // console.log(route.params.version);
                    let innerRun;
                    innerRun = setInterval(() => {
                        if (filteredBranches.value.length === 0) return;
                        findDeployByParams(
                            route.params.prefix,
                            route.params.branch,
                            route.params.version
                        );
                        clearInterval(innerRun);
                    }, 50);
                };
                processParams();
                clearInterval(run);
            }, 50);
        });

        const findDeployByParams = (prefix, branch, version) => {
            filteredBranches.value.forEach((element) => {
                if (
                    element.Key.split('/')[0] === prefix &&
                    element.Key.split('/')[1] === branch &&
                    element.Key.split('/')[2] === version
                ) {
                    // console.log(route.params);
                    setSelectedDeploy(element);
                }
            });
        };

        // REFACTOR: possibly superfluous function
        const setSelectedDeploy = (val) => {
            selectedDeploy.value = val;
            // console.log(selectedDeploy.value);
        };

        const getConfirmationMessage = (inputCommand) => {
            return confirm(
                `Are you sure you want to ${inputCommand} ${
                    selectedDeploy.value.Key.split('/')[1]
                } ${selectedDeploy.value.Key.split('/')[2]}?`
            );
        };

        const buildInputUrl = (command, key) => {
            // * slice off deploy.zip
            const inputKeyValues = key.split('/').slice(0, -1);

            // * if branch doesn't have a version number ie: last elem of array is null
            // prefix + branch + version
            // prefix + branch
            if (inputKeyValues.length < 3) inputKeyValues.push('null');

            return config.apiUrl + `/${command}/${inputKeyValues.join('/')}`;
        };

        const deploy = () => {
            if (selectedDeploy.value === null) {
                // * return most recent deploy if no selected branch
                selectedDeploy.value = filteredBranches.value.reduce((a, b) => {
                    return new Date(a.LastModified) > new Date(b.LastModified)
                        ? a
                        : b;
                });
            }

            if (!getConfirmationMessage('deploy')) return;

            const inputUrl = buildInputUrl('deploy', selectedDeploy.value.Key);
            // console.log(selectedDeploy.value);
            // console.log(inputUrl);
            // console.log(getOptions('POST', inputUrl));
            loading.value = true;
            axios(getOptions('POST', inputUrl))
                .then((response) => {
                    loading.value = false;
                    // console.log(response);
                    const message = `${selectedDeploy.value.Key} set as build deploy`;
                    triggerToast(message, greenSuccess); // green

                    // showModal.value = true;
                    // headerMessage.value = message;
                    // bodyMessage.value = message;
                    // footerMessage.value = message;
                })
                .catch((err) => {
                    throwError(err);
                });
        };

        const deleteDeploy = () => {
            if (!getConfirmationMessage('delete')) return;

            const inputUrl = buildInputUrl('delete', selectedDeploy.value.Key);
            // console.log(inputUrl);
            loading.value = true;
            axios(getOptions('POST', inputUrl))
                .then((response) => {
                    loading.value = false;
                    // console.log(response);
                    const message = `${selectedDeploy.value.Key} deleted from build list`;
                    triggerToast(message, redFail); // red
                    // showModal.value = true;
                    // headerMessage.value = message;
                    // bodyMessage.value = message;
                    // footerMessage.value = message;
                    // * remove from list
                    branchOptions.value = branchOptions.value.filter(
                        (obj) => obj != selectedDeploy.value
                    );
                    selectedDeploy.value = null;
                })
                .catch((err) => {
                    throwError(err);
                });
        };

        const throwError = (err) => {
            loading.value = false;
            console.error(err);
            console.error(err.response.data);
            console.error(err.response.status);
            console.error(err.response.headers);
            alert(err);
        };

        return {
            ascending,
            loading,
            showModal,
            showToast,
            triggerToast,
            toastMessage,
            toastColor,
            headerMessage,
            bodyMessage,
            footerMessage,
            columnBranchName,
            columns,
            sortedColumn,
            sortTableControl,
            selectedPrefix,
            selectedDeploy,
            branchOptions,
            filteredBranches,
            getBranchesFromPrefix,
            setSelectedDeploy,
            deploy,
            deleteDeploy
        };
    }
};
</script>

<style lang="scss" scoped>
@import '@/assets/_shared.scss';

.deploymentContainer {
    display: inline-grid;
    // display: table;
    // width: 100%;
    // height: 400px; /* for demo only */
    min-width: 250px;
    max-width: 600px;
    width: 70%;
    max-height: 300px;
}

select {
    padding: 35px;

    max-width: 100%;
    width: 100%;
}

table {
    display: block;
    width: 100%;
}

th {
    width: 100%;
}

tr {
    cursor: pointer;
    width: 100%;
}

tr:hover {
    background-color: #3297fd;
    color: black;
}

.selected {
    background-color: #0259b1;
}

// * Loading spinner
// REFACTOR: display this in the shared css folder

.loader {
    width: 50%;
    margin: 0 auto;

    border: 16px solid #f3f3f3; /* Light grey */
    border-top: 16px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// Toast transtiion styling

/* enter transitions */
.toast-enter-from {
    opacity: 0;
    transform: translateY(-60px);
}
.toast-enter-to {
    opacity: 1;
    transform: translateY(0);
}
.toast-enter-active {
    transition: all 0.3s ease;
}
/* leave transitions */
.toast-leave-from {
    opacity: 1;
    transform: translateY(0);
}
.toast-leave-to {
    opacity: 0;
    transform: translateY(-60px);
}
.toast-leave-active {
    transition: all 0.3s ease;
}
</style>