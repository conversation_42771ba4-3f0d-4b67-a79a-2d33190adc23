{"name": "dynamic-environments-api", "version": "3.1", "description": "Auth0 protected API for Dynamic Environments BackOffice application", "license": "Commercial", "type": "module", "repository": {"type": "git", "url": "https://github.com/Quotable-Value/dynamic-environments.git"}, "scripts": {"start": "npx serverless offline --reload<PERSON><PERSON><PERSON>", "test": "mocha test/unit --reporter mochaw<PERSON><PERSON>", "test:integration": "mocha test/integration/todo.js --reporter mochaw<PERSON>ome", "test:smoke": "mocha test/smoke --reporter mochaw<PERSON><PERSON>", "cover": "nyc mocha tests/**/*.test.js", "lint": "eslint src/**/*.js", "test_unit_single": "ENV=local mocha test/unit/getAppBuild.test.js"}, "devDependencies": {"@aws-sdk/client-api-gateway": "^3.624.0", "@aws-sdk/client-elastic-load-balancing-v2": "^3.624.0", "@aws-sdk/client-codebuild": "^3.427.0", "@aws-sdk/client-codepipeline": "^3.427.0", "@aws-sdk/client-dynamodb": "^3.326.0", "@aws-sdk/client-ecs": "^3.326.0", "@aws-sdk/client-s3": "^3.326.0", "@aws-sdk/client-ssm": "^3.326.0", "@aws-sdk/s3-request-presigner": "^3.430.0", "@aws-sdk/util-dynamodb": "^3.326.0", "@aws-sdk/util-utf8-node": "^3.259.0", "@quotable-value/serverless": "3.38.3", "@smithy/util-stream": "^2.1.1", "aws-sdk-client-mock": "^3.0.0", "chai": "^4.3.7", "chai-http": "^4.3.0", "chai-json-schema": "^1.5.1", "chai-string": "^1.5.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.6", "mocha": "^10.2.0", "mochawesome": "^7.1.3", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "serverless-domain-manager": "^6.2.1", "serverless-offline": "^13.6.0", "sinon": "^15.0.1"}, "dependencies": {"adm-zip": "^0.5.10", "cheerio": "^1.0.0-rc.12", "common": "file:../common", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "mssql": "^10.0.2", "pg": "^8.9.0", "pg-pool": "^3.5.2", "tedious": "^15.1.2"}}