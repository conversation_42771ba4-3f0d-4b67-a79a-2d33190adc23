import { CodeBuildClient, StartBuildCommand } from '@aws-sdk/client-codebuild';
import { config } from './appDeployArtifacts.js';

export default async function invokeTestCodeBuild(parameters) {
  const REGION = config.region;

  const codebuildClient = new CodeBuildClient({ region: REGION });

  const environmentVariablesOverride = [
    getEnvironmentVariableObject('ENV', parameters.env),
    getEnvironmentVariableObject('PROJECT_NAME', parameters.app),
    getEnvironmentVariableObject('INITIATOR', parameters.email),
  ];
  if (parameters.testSpec) {
    const testSpecOverride = getEnvironmentVariableObject('TEST_SPEC_OVERRIDE', parameters.testSpec);
    environmentVariablesOverride.push(testSpecOverride);
  }

  const buildCommitInputParameters = {
    projectName: parameters.testSuite,
    sourceVersion: parameters.commit,
    // EnvironmentVariables
    environmentVariablesOverride,
  };

  const runBuildCommand = new StartBuildCommand(buildCommitInputParameters);

  const output = await codebuildClient.send(runBuildCommand);
  return output;
}

function getEnvironmentVariableObject(name, value, type = 'PLAINTEXT') {
  return { name, value, type };
}
