import {
  CodeBuildClient,
  ListBuildsForProjectCommand,
  BatchGetBuildsCommand,
} from '@aws-sdk/client-codebuild';
import { config } from './appDeployArtifacts.js';

export default async function getCodeBuildHistory(appName) {
  const REGION = config.region;

  const codebuildClient = new CodeBuildClient({ region: REGION });

  const listBuildsInput = {
    projectName: appName,
    sortOrder: 'DESCENDING',
  };
  const listBuildsCommand = new ListBuildsForProjectCommand(listBuildsInput);
  const listBuildsResult = await codebuildClient.send(listBuildsCommand);

  const batchGetBuildsInput = { ids: listBuildsResult.ids };
  const batchGetBuildsCommand = new BatchGetBuildsCommand(batchGetBuildsInput);
  const batchGetBuildsResult = await codebuildClient.send(batchGetBuildsCommand);

  return batchGetBuildsResult.builds;
}
