import {
  CopyObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
  S3Client,
} from '@aws-sdk/client-s3';
import {
  STATUS_DELETED, STATUS_FAILED, STATUS_INVALID, STATUS_SUCCESS,
} from 'common/enums/crudStatus.js';
import { RESERVED_APP_ENV_NAMES } from 'common/util/const.js';

export const config = {
  deployBucketName: 'qv-deployment',
  region: 'ap-southeast-2',
  artifactFileName: 'deploy.zip',
};

function getS3Client() {
  return new S3Client({ region: config.region });
}

export async function getApps() {
  const s3 = getS3Client();
  let allPrefixes = [];
  let continuationToken;

  do {
    const prefixesResponse = await s3.send(
      new ListObjectsV2Command({
        Bucket: config.deployBucketName,
        Prefix: '',
        Delimiter: '/',
        ContinuationToken: continuationToken,
      }),
    );

    if (prefixesResponse.CommonPrefixes) {
      allPrefixes = [...allPrefixes, ...prefixesResponse.CommonPrefixes];
    }

    continuationToken = prefixesResponse.NextContinuationToken;
  } while (continuationToken);

  return allPrefixes.map(
    (prefix) => prefix.Prefix.replace('/', ''),
  ) || [];
}

export async function getBuildArtifacts(appName) {
  const s3 = getS3Client();
  const prefix = `${appName}/build/`;
  let allContents = [];
  let continuationToken;

  do {
    const branchesResponse = await s3.send(
      new ListObjectsV2Command({
        Bucket: config.deployBucketName,
        Prefix: prefix,
        ContinuationToken: continuationToken,
      }),
    );

    if (branchesResponse.Contents) {
      allContents = [...allContents, ...branchesResponse.Contents];
    }

    continuationToken = branchesResponse.NextContinuationToken;
  } while (continuationToken);

  return allContents.filter(
    (content) => content.Key.endsWith(config.artifactFileName),
  ).map(
    (artifact) => artifactFields(appName, artifact),
  ) || [];
}

export async function getBuildArtifactsLegacy(appName) {
  const s3 = getS3Client();
  const prefix = `${appName}/`;
  let allContents = [];
  let continuationToken;

  do {
    const branchesResponse = await s3.send(
      new ListObjectsV2Command({
        Bucket: config.deployBucketName,
        Prefix: prefix,
        ContinuationToken: continuationToken,
      }),
    );

    if (branchesResponse.Contents) {
      allContents = [...allContents, ...branchesResponse.Contents];
    }

    continuationToken = branchesResponse.NextContinuationToken;
  } while (continuationToken);

  return allContents.filter(
    (content) => content.Key.endsWith(config.artifactFileName),
  ).map(
    (artifact) => artifactFields(appName, artifact),
  ) || [];
}

export async function getEnvironments(appName, includeDae) {
  const s3 = getS3Client();
  const response = await s3.send(
    new ListObjectsV2Command({
      Bucket: config.deployBucketName,
      Prefix: `${appName}/deploy/`,
    }),
  );

  let environments = response.Contents?.filter(
    (content) => content.Key.endsWith(config.artifactFileName),
  ).map((artifact) => artifactFields(appName, artifact)) || [];

  if (includeDae !== 'true') {
    environments = environments.filter((it) => RESERVED_APP_ENV_NAMES.includes(it.name));
  }

  return environments;
}

export async function getArtifactMetadata(appName, artifactName, type = 'build') {
  return await getMetadata(`${appName}/${type}/${artifactName}/${config.artifactFileName}`, {
    app: appName,
    name: artifactName,
    type,
  });
}

export async function getArtifactMetadataLegacy(appName, artifactName = null) {
  return await getMetadata(`${appName}/${artifactName ? `${artifactName}/` : ''}${config.artifactFileName}`, {
    app: appName,
    name: artifactName,
  });
}

async function getMetadata(key, entity) {
  try {
    const s3 = getS3Client();
    const response = await s3.send(
      new HeadObjectCommand({
        Bucket: config.deployBucketName,
        Key: key,
      }),
    );
    entity.status = response?.$metadata.httpStatusCode === 200 ? STATUS_SUCCESS : STATUS_INVALID;
    entity.metadata = response?.Metadata || {};
  } catch (err) {
    logger.error('ERR-DYE-ADA01 Failed to get artifact metadata', key, err);
    entity.status = STATUS_FAILED;
  }
  return entity;
}

export async function deleteBuildArtifact(appName, artifactName) {
  return await deleteBuild(appName, artifactName, `${appName}/build/${artifactName}/${config.artifactFileName}`);
}

export async function deleteBuildArtifactLegacy(appName, artifactName) {
  return await deleteBuild(appName, artifactName, `${appName}/${artifactName}/${config.artifactFileName}`);
}

async function deleteBuild(appName, artifactName, key) {
  try {
    const s3 = getS3Client();
    const response = await s3.send(
      new DeleteObjectCommand({
        Bucket: config.deployBucketName,
        Key: key,
      }),
    );

    return {
      status: response?.$metadata.httpStatusCode === 204 ? STATUS_DELETED : STATUS_INVALID,
      app: appName,
      name: artifactName,
    };
  } catch (err) {
    console.error('ERR-DYE-ADA03 Failed to delete build artifact', key, err);
    return {
      status: STATUS_FAILED,
      app: appName,
      name: artifactName,
    };
  }
}

export async function deployBuildArtifact(appName, artifactName, envName) {
  return await copyFile(
    `${config.deployBucketName}/${appName}/build/${artifactName}/${config.artifactFileName}`,
    `${appName}/deploy/${envName}/${config.artifactFileName}`,
  );
}

export async function deployBuildArtifactLegacy(appName, artifactName) {
  return await copyFile(
    `${config.deployBucketName}/${appName}/${artifactName}/${config.artifactFileName}`,
    `${appName}/${config.artifactFileName}`,
  );
}

export async function copyFile(from, toKey) {
  const s3 = new S3Client({ region: config.region });
  console.info(`Copying artifact from ${from} to ${toKey}`);
  const response = await s3.send(
    new CopyObjectCommand({
      Bucket: config.deployBucketName,
      CopySource: from,
      Key: toKey,
    }),
  );
  console.log('Finished copying artifact, status: ', response.$metadata.httpStatusCode);
  const success = response.$metadata.httpStatusCode === 200;
  if (!success) {
    console.error('Failed to copy build artifact', response.$metadata);
  }
  return success;
}

function artifactFields(appName, artifact) {
  const path = getArtifactPath(appName, artifact);
  return {
    name: path.replace(`/${config.artifactFileName}`, ''),
    key: artifact.Key,
    lastModified: artifact.LastModified,
    size: artifact.Size,
  };
}

function getArtifactPath(appName, artifact) {
  if (artifact.Key.startsWith(`${appName}/build/`)) {
    return artifact.Key.replace(`${appName}/build/`, '');
  }

  if (artifact.Key.startsWith(`${appName}/deploy/`)) {
    return artifact.Key.replace(`${appName}/deploy/`, '');
  }

  // legacy format
  return artifact.Key.replace(`${appName}/`, '');
}
