import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { pathParam } from './eventValidators.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { getDatabaseBuild } from '../service/getDatabaseBuild.js';

export async function getDatabaseBuildHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid name must be provided',
      },
    };
  }
  const { value: datastore } = pathParam(event, 'datastore');

  if (!datastore) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid datastore must be provided.',
      },
    };
  }
  return { body: await getDatabaseBuild(name, datastore) };
}

export const handle = wrapHttpHandler(getDatabaseBuildHandler);
