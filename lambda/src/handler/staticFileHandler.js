import { readFileByKey } from '../service/readFileByKey.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { getClaims } from '../service/auth.js';

export const handle = wrapHttpHandler(async (event) => {
  const path = event.pathParameters.file;
  try {
    const {
      contentType,
      s3Body,
      isBase64Encoded,
    } = await readFileByKey(path);
    const headers = {
      'Content-Type': contentType,
    };
    const bearer = event.queryStringParameters?.bearer;
    if (bearer) {
      const claims = getClaims(event);
      const { exp } = claims;
      headers['Set-Cookie'] = `bearer=${bearer}; HttpOnly; Secure; SameSite=None;expires=${new Date(exp * 1000).toUTCString()}`;
    }

    return {
      statusCode: 200,
      headers,
      body: s3Body,
      isBase64Encoded,
    };
  } catch (e) {
    logger.error('', 'Error reading file:', e);
    return {
      statusCode: 404,
    };
  }
});
