import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';
import { getReportFiles } from '../service/getReportFiles.js';

export async function getReportFilesHandler(event) {
  const { value: reportId } = pathParam(event, 'reportId');
  if (!reportId) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid reportId must be provided',
      },
    };
  }
  return ({ body: await getReportFiles(reportId) });
}

export const handle = wrapHttpHandler(getReportFilesHandler);
