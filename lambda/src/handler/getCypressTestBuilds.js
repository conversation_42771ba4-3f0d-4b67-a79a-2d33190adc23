import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { getCypressTestBuilds as getCypressTestBuildsService } from '../service/getCypressTestBuilds.js';

export const handle = wrapHttpHandler(getCypressTestBuilds);

export default handle;

export async function getCypressTestBuilds(event) {
  const { appName } = event.pathParameters;
  if (!appName) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'No appName provided',
      },
    };
  }

  const builds = await getCypressTestBuildsService(appName);

  return { body: { status: STATUS_SUCCESS, builds } };
}
