import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import getAppBuilds from '../service/getAppBuilds.js';

export const handle = wrapHttpHandler(async (event) => {
  const { appName } = event.pathParameters;
  if (!appName) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'No appName provided',
      },
    };
  }
  console.log('queryStringParameters', event.queryStringParameters);
  const parameters = event.queryStringParameters || {};

  const { includeDae } = parameters;
  return { body: await getAppBuilds(appName, includeDae) };
});
