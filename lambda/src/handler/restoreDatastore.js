import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { pathParam } from './eventValidators.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { restoreDatastore } from '../service/restoreDatastore.js';

export async function restoreDatastoreHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid name must be provided',
      },
    };
  }
  const { value: datastore } = pathParam(event, 'datastore');

  if (!datastore) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid datastore must be provided.',
      },
    };
  }
  logger.info('deployDatastore', { name, datastore });

  return { body: await restoreDatastore(name, datastore) };
}

export const handle = wrapHttpHandler(restoreDatastoreHandler);
