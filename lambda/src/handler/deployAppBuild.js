import { RESERVED_APP_ENV_NAMES } from 'common/util/const.js';
import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { pathParam, queryParam } from './eventValidators.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import deployAppBuild from '../service/deployAppBuild.js';
import getAppBuildMetadata from '../service/getAppBuildMetadata.js';

export async function deployAppHandler(event) {
  const { value: appName } = pathParam(event, 'appName');
  if (!appName) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid appName must be provided',
      },
    };
  }

  const { value: build = null } = queryParam(event, 'build');
  if (!build) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid build must be provided',
      },
    };
  }

  const { value: env = null } = queryParam(event, 'env');

  if (!RESERVED_APP_ENV_NAMES.includes(env)) {
    return {
      body: {
        status: STATUS_INVALID,
        message: `Invalid env name provided: ${env}. Only the following are allowed: ${RESERVED_APP_ENV_NAMES.join(', ')}`,
      },
    };
  }

  const { builds } = await getAppBuildMetadata(appName, {
    buildNames: [build],
    envNames: [env],
    isLegacy: false,
  });
  const buildMetadata = builds[0]?.metadata;

  await deployAppBuild(appName, build, buildMetadata, env);

  return {
    body: {
      status: STATUS_SUCCESS,
      message: 'Deployment successful',
    },
  };
}

export const handle = wrapHttpHandler(deployAppHandler);
