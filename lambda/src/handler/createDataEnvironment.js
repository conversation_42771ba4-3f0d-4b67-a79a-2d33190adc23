import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody, pathParam } from './eventValidators.js';
import createDataEnvironment from '../service/createDataEnvironment.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  return { body: await createDataEnvironment(name, payload) };
});
