import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import getAppBuildMetadata from '../service/getAppBuildMetadata.js';

export const handle = wrapHttpHandler(async (event) => {
  const { appName } = event.pathParameters;
  if (!appName) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'appName is required',
      },
    };
  }

  const params = new URLSearchParams(event.rawQueryString);
  const buildNames = params.getAll('build');
  const envNames = params.getAll('env');
  const isLegacy = params.get('legacy') === 'true';

  if (!buildNames?.length && !envNames?.length) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'build name or env name is required.',
      },
    };
  }

  return {
    body: await getAppBuildMetadata(appName, {
      buildNames,
      envNames,
      isLegacy,
    }),
  };
});
