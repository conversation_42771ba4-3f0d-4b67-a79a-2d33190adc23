import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { startCypressTest } from '../service/startCypressTest.js';

export const startCypressTestHandler = (async (event) => {
  const parameters = event.queryStringParameters || {};
  const output = await startCypressTest(parameters);
  return { body: { status: STATUS_SUCCESS, ...output } };
});

export const handle = wrapHttpHandler(startCypressTestHandler);

export default handle;
