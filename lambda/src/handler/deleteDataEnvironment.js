import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { pathParam } from './eventValidators.js';
import { deleteDataEnvironment } from '../service/deleteDataEnvironment.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      status: STATUS_INVALID,
      message: 'a valid name must be provided',
    };
  }

  return { body: await deleteDataEnvironment(name) };
});
