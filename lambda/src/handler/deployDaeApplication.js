import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getIncompleteDeployments } from 'common/dal/deployments.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { pathParam } from './eventValidators.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

export async function deployDaeApplicationHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid name must be provided',
      },
    };
  }
  const { value: applicationName } = pathParam(event, 'applicationName');

  if (!applicationName) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid applicationName must be provided.',
      },
    };
  }
  logger.info('deployApplicationByName', { name, applicationName });
  const deployments = await getIncompleteDeployments(name, ApplicationType.DAE);
  logger.info('deployments', { deployments });
  if (deployments.length > 0) {
    return {
      body: { status: STATUS_INVALID, message: 'a deployment is already in progress' },
    };
  }
  const deploymentId = await initDeployment(name, ApplicationType.DAE, applicationName);

  logger.info(`deploymentId ${deploymentId} initiated`);

  return {
    body: {
      status: STATUS_SUCCESS,
    },
  };
}

export const handle = wrapHttpHandler(deployDaeApplicationHandler);
