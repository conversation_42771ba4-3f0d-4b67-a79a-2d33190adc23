import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { getDaeContext } from 'common/service/getContext.js';
import Logger from 'common/util/Logger.js';
import { pathParam } from './eventValidators.js';

export async function getApplicationContextHandler(event) {
  global.logger = new Logger();
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  return { body: await getDaeContext(name) };
}

export const handle = getApplicationContextHandler;
