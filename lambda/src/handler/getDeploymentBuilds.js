import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';
import { getDeploymentBuilds } from '../service/getDeploymentBuilds.js';

export async function getDeploymentBuildsHandler(event) {
  const { value: deploymentId } = pathParam(event, 'deploymentId');
  if (!deploymentId) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid deploymentId must be provided',
      },
    };
  }
  return ({ body: await getDeploymentBuilds(deploymentId) });
}

export const handle = wrapHttpHandler(getDeploymentBuildsHandler);
