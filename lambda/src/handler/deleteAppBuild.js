import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import deleteAppBuilds from '../service/deleteAppBuilds.js';
import { pathParam } from './eventValidators.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: appName, ...appNameError } = pathParam(event, 'appName');
  if (!appName) {
    return { body: appNameError };
  }
  const params = new URLSearchParams(event.rawQueryString);
  const buildNames = params.getAll('build');
  const isLegacy = params.get('legacy') === 'true';
  if (!buildNames?.length) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'build name or env name is required.',
      },
    };
  }
  return { body: await deleteAppBuilds(appName, buildNames, isLegacy) };
});
