import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getAppEnvConfig } from 'common/service/getAppEnvConfig.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody, pathParam } from './eventValidators.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      status: STATUS_INVALID,
      message: 'a valid name must be provided.',
    };
  }
  const { value: dataEnv } = pathParam(event, 'dataEnv');

  if (!dataEnv) {
    return {
      status: STATUS_INVALID,
      message: 'a valid dataEnv must be provided.',
    };
  }
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  const { update } = payload;
  const { account } = payload;
  const { includeQivs } = payload;
  logger.info('payload', { payload });
  return ({
    body: {
      status: STATUS_SUCCESS,
      contextConfig: await getAppEnvConfig(name, dataEnv, account, includeQivs, update),
    },
  });
});
