import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';
import { getAppEnvConfig } from '../service/getAppEnvConfig.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      status: STATUS_INVALID,
      message: 'a valid name must be provided',
    };
  }
  return ({
    body: await getAppEnvConfig(name),
  });
});
