import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import createAppEnvironment from 'common/service/createAppEnvironment.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody, pathParam } from './eventValidators.js';

export async function createAppEnvironmentHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  if (!payload.defaultBranch || payload.defaultBranch === '') {
    payload.defaultBranch = 'master';
  }
  return { body: await createAppEnvironment(name, payload, true) };
}

export const handle = wrapHttpHandler(createAppEnvironmentHandler);
