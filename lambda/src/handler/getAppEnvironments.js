import getConfig from 'common/config/getConfig.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { getAppEnvironments } from '../service/getAppEnvironments.js';

export const handle = wrapHttpHandler(async (event) => {
  const parameters = event.queryStringParameters;
  const status = parameters?.status;
  let name = parameters?.name;
  logger.info(`status: ${status}`);
  let statuses = [];
  if (status !== undefined) {
    statuses = status.split(',');
  }
  if (name === undefined) {
    name = '';
  }

  const stage = await getConfig('STAGE');
  return ({
    body: await getAppEnvironments({
      statuses,
      name,
      stage,
    }),
  });
});
