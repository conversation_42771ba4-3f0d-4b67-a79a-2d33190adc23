import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';
import { getApplications } from '../service/getApplications.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }

  return ({ body: await getApplications(name, ApplicationType.DDE) });
});
