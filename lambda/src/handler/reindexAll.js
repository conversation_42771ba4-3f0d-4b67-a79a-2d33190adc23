import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';
import createIndex from '../service/createIndex.js';

export async function reindexAllHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }

  const result = await createIndex(name);
  return { body: result };
}

export const handle = wrapHttpHandler(reindexAllHandler);
