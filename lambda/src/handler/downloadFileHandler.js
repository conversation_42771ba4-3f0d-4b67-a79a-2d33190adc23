import { readFileByKey } from '../service/readFileByKey.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

export const handle = wrapHttpHandler(async (event) => {
  const path = event.queryStringParameters.file;
  logger.info(path);
  try {
    const {
      contentType,
      s3Body,
      isBase64Encoded,
    } = await readFileByKey(path);
    return {
      statusCode: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename=${path}`,
      },
      body: s3Body,
      isBase64Encoded,
    };
  } catch (e) {
    return {
      statusCode: 404,
    };
  }
});
