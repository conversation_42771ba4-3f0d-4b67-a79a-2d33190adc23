import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { pathParam } from './eventValidators.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { deployDatastore } from '../service/deployDatastore.js';

export async function deployDatastoreHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid name must be provided',
      },
    };
  }
  const { value: datastore } = pathParam(event, 'datastore');

  if (!datastore) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'a valid datastore must be provided.',
      },
    };
  }
  logger.info('deployDatastore', { name, datastore });

  return { body: await deployDatastore(name, datastore) };
}

export const handle = wrapHttpHandler(deployDatastoreHandler);
