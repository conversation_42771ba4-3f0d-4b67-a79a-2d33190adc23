import { scanTableWithFilter } from 'common/dal/common.js';
import { DEPLOYMENT_REPORTS_BUCKET, TEST_REPORTS } from 'common/util/const.js';
import { getTestResult } from 'common/service/testReports.js';
import Logger from 'common/util/Logger.js';
import { updateTestReportByReportId } from 'common/dal/testReports.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function reportsEventsSubscriber(event) {
  global.logger = new Logger();
  logger.info('reportsEventsSubscriber', { event });
  const s3Key = event.Records[0].s3.object.key;
  logger.info('s3Key:', s3Key);
  const reportId = s3Key.split('/')[2];
  logger.info(`reportId: ${reportId}`);
  const testReports = await scanTableWithFilter(TEST_REPORTS, { reportId });
  if (testReports.length === 0) {
    logger.error('No test report found for', { reportId, s3Key });
    return;
  }
  const testReport = testReports[0];

  const folder = `s3://${DEPLOYMENT_REPORTS_BUCKET}/${testReport.folder}`;
  const result = await getTestResult(cicdS3Client, folder);
  logger.info('result:', result);
  await updateTestReportByReportId(reportId, result);
  logger.info('Test report updated:', { reportId });
}

export const handle = reportsEventsSubscriber;
