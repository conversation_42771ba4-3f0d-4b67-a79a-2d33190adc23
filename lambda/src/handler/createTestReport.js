import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import Logger from 'common/util/Logger.js';
import { createTestReport } from '../service/createTestReport.js';
import { parseBody } from './eventValidators.js';

export async function createTestReportHandler(event) {
  global.logger = new Logger();
  logger.info('createTestReportHandler', { event });
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  return ({ body: await createTestReport(payload) });
}

export const handle = createTestReportHandler;
