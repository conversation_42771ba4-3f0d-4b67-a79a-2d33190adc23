import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { checkBranchExistence } from 'common/service/checkBranchExistence.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody } from './eventValidators.js';

export const handle = wrapHttpHandler(async (event) => {
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  const { branch } = payload;
  if (!branch) {
    return {
      status: STATUS_INVALID,
      message: 'Branch name is required',
    };
  }
  return {
    body: { branches: await checkBranchExistence(branch) },
  };
});
