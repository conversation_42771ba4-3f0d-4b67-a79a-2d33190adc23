import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody, pathParam } from './eventValidators.js';
import createIndex from '../service/createIndex.js';

export async function reindexHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }

  const result = await createIndex(name, payload);
  return { body: result };
}

export const handle = wrapHttpHandler(reindexHandler);
