import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { healthCheck } from 'common/service/healthCheck.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { pathParam } from './eventValidators.js';

export const handle = wrapHttpHandler(async (event) => {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  const { value: applicationName } = pathParam(event, 'applicationName');

  if (!applicationName) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid application must be provided',
      },
    };
  }

  return ({ body: await healthCheck(name, applicationName, ApplicationType.DDE) });
});
