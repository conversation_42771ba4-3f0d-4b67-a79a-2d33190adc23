import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import Logger from 'common/util/Logger.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';

export const handle = async (event) => {
  global.logger = new Logger();
  const payload = JSON.stringify({
    branch: process.env.BRANCH ?? 'master',
    dde: process.env.DDE,
  });
  logger.info(`app-env-verify payload: ${payload}`);
  await runEcsTask(CmdAppTasks.APP_ENV_VERIFY, { payload });
  return { status: STATUS_SUCCESS, message: 'app env verification started' };
};
