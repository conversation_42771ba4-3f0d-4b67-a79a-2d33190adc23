import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import { getIncompleteDeployments } from 'common/dal/deployments.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody, pathParam } from './eventValidators.js';
import updateDataEnvironment from '../service/updateDataEnvironment.js';

export async function updateDataEnvironmentHandler(event) {
  const { value: name } = pathParam(event, 'name');
  if (!name) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid name must be provided',
      },
    };
  }
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  if ((await getIncompleteDeployments(name, ApplicationType.DDE)).length > 0) {
    return {
      body: {
        status: STATUS_INVALID,
        message: 'updating data environment will potentially trigger a deployment, another deployment is still in progress',
      },
    };
  }
  return { body: await updateDataEnvironment(name, payload) };
}

export const handle = wrapHttpHandler(updateDataEnvironmentHandler);
