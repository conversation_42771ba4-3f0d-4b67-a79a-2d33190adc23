import {
  DELETED, DEPLOYING,
  ERROR, HEALTHY,
  INFRA_DELETED,
  INITIALISING_INFRA,
  PROVISIONING,
  STABLE,
  TEARING_DOWN, TESTING, UNHEALTHY, UPDATING,
} from 'common/enums/environmentStatus.js';
import getConfig from 'common/config/getConfig.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

import { getDataEnvironments } from '../service/getDataEnvironments.js';

export const handle = wrapHttpHandler(async (event) => {
  const parameters = event.queryStringParameters;
  const status = parameters?.status;
  logger.info(`status: ${status}`);
  let statuses;
  if (status === undefined) {
    statuses = [STABLE, UPDATING, PROVISIONING, DEPLOYING, HEALTHY, UNHEALTHY, TESTING, TEARING_DOWN, DELETED, ERROR, INITIALISING_INFRA, INFRA_DELETED];
  } else {
    statuses = status.split(',');
  }
  const stage = await getConfig('STAGE');
  return ({ body: await getDataEnvironments(statuses, stage) });
});
