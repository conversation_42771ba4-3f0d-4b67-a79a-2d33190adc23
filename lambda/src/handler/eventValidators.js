import { STATUS_INVALID } from 'common/enums/crudStatus.js';

export const pathParam = (event, param) => {
  if (event?.pathParameters[param]) {
    return { value: event?.pathParameters[param] };
  }

  return {
    message: `Path parameter missing: ${param}`,
    status: STATUS_INVALID,
  };
};

export function queryParam(event, param, paramDefault) {
  if (event.queryStringParameters && event.queryStringParameters?.[param] !== undefined) {
    return { value: event.queryStringParameters[param] };
  }

  if (paramDefault !== undefined) {
    return {
      value: paramDefault,
      paramDefault: true,
    };
  }

  return {
    message: `Query parameter missing: ${param}`,
    status: STATUS_INVALID,
  };
}

export const parseBody = (event) => {
  try {
    const body = JSON.parse(event.body);
    if (!body || Object.keys(body).length === 0) {
      return {
        status: STATUS_INVALID,
        message: 'Invalid JSON body.',
      };
    }
    return { body };
  } catch {
    const message = 'Malformed JSON';
    global.logger.error(`Cannot parse body (${message}): `, event.body);
    return {
      message,
      status: STATUS_INVALID,
    };
  }
};
