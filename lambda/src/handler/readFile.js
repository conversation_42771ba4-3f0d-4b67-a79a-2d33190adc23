import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';
import { parseBody } from './eventValidators.js';
import { downloadFileByKey } from '../service/downloadFileByKey.js';

export const handle = wrapHttpHandler(async (event) => {
  const { body: payload } = parseBody(event);
  if (!payload) {
    return {
      body: {
        status: STATUS_INVALID, message: 'a valid payload must be provided',
      },
    };
  }
  const { key } = payload;
  if (!key) {
    return {
      status: STATUS_INVALID,
      message: 'key  is required',
    };
  }
  return {
    body: { branches: await downloadFileByKey(key) },
  };
});
