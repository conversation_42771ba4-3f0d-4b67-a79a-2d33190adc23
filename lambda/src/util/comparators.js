export function descByFieldFn(field) {
  return (a, b) => {
    const aStartTime = a[field]?.S;
    const bStartTime = b[field]?.S;
    if (aStartTime > bStartTime) {
      return -1;
    }
    if (aStartTime < bStartTime) {
      return 1;
    }
    return 0;
  };
}

export function descByFieldValue(field) {
  return (a, b) => {
    const aStartTime = a[field];
    const bStartTime = b[field];
    if (aStartTime > bStartTime) {
      return -1;
    }
    if (aStartTime < bStartTime) {
      return 1;
    }
    return 0;
  };
}

export function ascByFieldValue(field) {
  return (a, b) => {
    const valueA = a[field];
    const valueB = b[field];
    if (valueA < valueB) {
      return -1;
    }
    if (valueA > valueB) {
      return 1;
    }
    return 0;
  };
}
