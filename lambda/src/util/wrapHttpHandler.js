import {
  crudHttpStatus, STATUS_FAILED, STATUS_LOCKED, STATUS_SUCCESS,
} from 'common/enums/crudStatus.js';
import Logger from 'common/util/Logger.js';
import { getClaims, hasPermission, userInfo } from '../service/auth.js';

export default function wrapHttpHandler(handler) {
  global.logger = new Logger();
  return async (...handlerArgs) => {
    const [event, context] = handlerArgs;
    global.AWSContext = {
      event,
      ctx: context,
    };
    logger.info('Event received', { event });
    global.logger.info(`EVENT ${event.resource}`, {
      origin: event?.headers?.origin ?? event?.headers?.Origin,
      path: event?.rawPath ?? event?.requestContext?.routeKey,
      query: event?.rawQueryString ?? event?.queryStringParameters,
      cookies: event?.cookies,
    });

    const claims = getClaims(event);
    if (claims == null) {
      const message = 'No valid jwt token provided.';
      global.logger.error('ERR-CBO-WRP002', message);
      return stringifyResponseBody({
        statusCode: 403,
        body: {
          status: STATUS_LOCKED,
          message,
        },
      });
    }

    const user = await userInfo(event);
    global.userInfo = user;
    global.logger.info(`User ${JSON.stringify(user)}.`);
    if (!await hasPermission(claims)) {
      const message = `User ${user.email} not authorized.`;
      global.logger.error('ERR-CBO-WRP001', message);
      return stringifyResponseBody({
        statusCode: 401,
        body: {
          status: STATUS_FAILED,
          message,
        },
      });
    }
    try {
      const response = await handler(...handlerArgs);
      response.statusCode = response.statusCode ?? await statusifyResponse(response.body);
      return stringifyResponseBody(response);
    } catch (err) {
      if (err.statusCode) {
        return stringifyResponseBody({ ...err, event, context });
      }

      const message = `Unhandled exception ${err.toString()}`;
      global.logger.error('ERR-OBJ-001', message, err);

      return stringifyResponseBody({
        statusCode: 500,
        body: {
          status: STATUS_FAILED,
          message,
        },
      });
    }
  };
}

async function statusifyResponse(body) {
  const result = Array.isArray(body) ? body[0] : body;
  return crudHttpStatus(result?.status ?? STATUS_SUCCESS);
}

function stringifyResponseBody(response) {
  if (response?.body?.constructor !== String) {
    response.body = JSON.stringify(response.body);
  }
  return response;
}
