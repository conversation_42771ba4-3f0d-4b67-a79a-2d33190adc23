import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatApplications, getApplicationsByEnvAndType } from 'common/dal/applications.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { healthCheckResult, isAppHealthy } from 'common/service/healthCheck.js';
import { getApiKeyByName } from 'common/service/apiGateway.js';
import { getAppEnvironmentByName, updateAppEnvironmentStatus } from 'common/dal/appEnvironments.js';
import { HEALTHY, STABLE, UNHEALTHY } from 'common/enums/environmentStatus.js';
import { updateItem } from 'common/dal/common.js';
import { APP_ENVIRONMENTS, DATA_ENVIRONMENTS } from 'common/util/const.js';

import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import { ascByFieldValue } from '../util/comparators.js';

export async function getApplications(envName, type) {
  const applicationsList = await getApplicationsByEnvAndType(envName, type);
  logger.info(`Found ${applicationsList.length} existing applications`);

  const applications = formatApplications(applicationsList);
  const apiKeyName = `${envName}-${type.toLowerCase()}-lambda-api-key`;
  const apiKeyMap = {
    dev: 'website-dev',
    test: 'website-test',
    uat: 'website-uat',
  };

  const lambdaApiKey = await getApiKeyByName(apiKeyMap[envName] ?? apiKeyName, 5, 2);

  const healthCheckPromises = applications.map(async (application) => {
    const noResult = application.status === PipelineStatus.DEPLOYED && !application.healthCheckResult;
    const resultOutDated = application.healthCheckTime && (Date.now() - application.healthCheckTime) > 60 * 60 * 1000;
    if (noResult || resultOutDated) {
      logger.info(`Health check for ${application.name}`);
      await healthCheckResult(envName, application, type, lambdaApiKey);
    }
  });

  await Promise.all(healthCheckPromises);

  const result = formatApplications(await getApplicationsByEnvAndType(envName, type));
  result.sort(ascByFieldValue('name'));
  const totalApps = result.length;
  const unhealthyApps = [];
  let environment;
  if (type === ApplicationType.DDE) {
    [environment] = await getDataEnvironmentByName(envName);
  } else if (type === ApplicationType.DAE) {
    [environment] = await getAppEnvironmentByName(envName);
  }
  const status = environment.Status.S;
  const validStatuses = [HEALTHY, UNHEALTHY, STABLE];
  if (validStatuses.includes(status)) {
    let envStatus = HEALTHY;
    for (const app of result) {
      if (!await isAppHealthy(app)) {
        unhealthyApps.push(app.name);
        envStatus = UNHEALTHY;
      }
    }
    const updateData = {
      healthCheckResult: JSON.stringify({
        totalApps,
        unhealthyApps,
      }),
    };
    if (type === ApplicationType.DDE) {
      await updateItem(DATA_ENVIRONMENTS, 'name', envName, updateData);
      await updateDataEnvironmentStatus(envName, envStatus);
    }
    if (type === ApplicationType.DAE) {
      await updateItem(APP_ENVIRONMENTS, 'name', envName, updateData);
      await updateAppEnvironmentStatus(envName, envStatus);
    }
  }

  return { status: STATUS_SUCCESS, data: result };
}
