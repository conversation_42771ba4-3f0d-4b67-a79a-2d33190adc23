import { STATUS_INVALID } from 'common/enums/crudStatus.js';
import getAppBuildMetadata from './getAppBuildMetadata.js';
import invokeTestCodeBuild from '../dal/invokeTestCodeBuild.js';

export async function startCypressTest(parameters) {
  const {
    app,
    env,
    build,
    testSpec,
    testSuite,
    email,
  } = parameters;

  const appBuildMetadataObject = build ? { buildNames: [build] } : { envNames: [env] };

  // determine commit sha (either from build supplied in parameters or what commit is deployed in env)
  const metaData = await getAppBuildMetadata(app, appBuildMetadataObject);

  // Check if metaData and the necessary nested properties are defined
  const type = build ? 'builds' : 'envs';
  if (
    !metaData[type][0]?.metadata
  ) {
    return {
      status: STATUS_INVALID, message: 'Metadata or required nested properties are missing',
    };
  }

  const commit = metaData[type][0].metadata['commit-sha'];

  // this invokes the cypress codebuild with the env name and commit sha
  const output = await invokeTestCodeBuild({
    app,
    env,
    commit,
    testSpec,
    testSuite,
    email,
  });

  return output;
}

export default startCypressTest;
