import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';

import {
  formatDataEnvironments,
  getDataEnvironmentsByStatus,
  getDataStoreList,
  startTimeCompareFn,
} from 'common/dal/dataEnvironments.js';

export async function getDataEnvironments(statuses, stage) {
  logger.info(`status: ${statuses}`, `stage: ${stage}`);
  let existingDataEnvironments = await getDataEnvironmentsByStatus(statuses);
  existingDataEnvironments = existingDataEnvironments.filter((env) => env.Stage.S === stage);
  existingDataEnvironments.sort(startTimeCompareFn());

  logger.info(`Found ${existingDataEnvironments.length} existing environments`);
  const dataStoreList = await getDataStoreList(existingDataEnvironments);

  const result = formatDataEnvironments(existingDataEnvironments, dataStoreList);
  return { status: STATUS_SUCCESS, environments: result };
}
