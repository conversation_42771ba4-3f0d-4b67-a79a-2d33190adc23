import { getDataEnvironmentsByStatus } from 'common/dal/dataEnvironments.js';
import {
  ERROR, HEALTHY, INITIALISING_INFRA, PROVISIONING, STABLE, TEARING_DOWN, UNHEALTHY,
} from 'common/enums/environmentStatus.js';
import getConfig from 'common/config/getConfig.js';

export async function getNewDriveLetter() {
  const driveExists = await getDataEnvironmentsByStatus([STABLE, HEALTHY, UNHEALTHY, PROVISIONING, INITIALISING_INFRA, TEARING_DOWN, ERROR]);
  const existingDriveLetters = driveExists.map((item) => item?.Drive?.S);
  logger.info(`existing drive letters: ${existingDriveLetters}`);
  const driveLetters = await getConfig('DRIVE_LETTERS');

  const letters = driveLetters.split('');
  const drive = letters.find((char) => !existingDriveLetters.includes(char));
  logger.info(`new drive letter: ${drive}`);
  return drive;
}
