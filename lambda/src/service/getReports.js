import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getDeploymentByDeploymentId } from 'common/dal/deployments.js';
import { getBuildsByDeployment } from 'common/dal/deploymentBuilds.js';
import { DEPLOYMENT_REPORTS_BUCKET } from 'common/util/const.js';
import { listAllFiles } from 'common/util/s3util.js';
import { removeDaeSuffix } from 'common/util/appNameUtli.js';
import { extractReports, REPORT_OUTPUT_FILES } from 'common/service/testReports.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function getReports(parameters) {
  const { deploymentId, application: app, testType } = parameters;
  if (!deploymentId) {
    return { status: STATUS_SUCCESS, reports: [] };
  }
  const deployment = await getDeploymentByDeploymentId(deploymentId);
  let deploymentBuilds = await getBuildsByDeployment(deploymentId);
  const env = deployment.Env.S;
  if (app) {
    logger.info(`Filtering by application ${app}`);
    deploymentBuilds = deploymentBuilds.filter((build) => build.Application.S.includes(app));
  }
  let folders = deploymentBuilds.map((build) => {
    const application = build.Application.S;
    return `s3://${DEPLOYMENT_REPORTS_BUCKET}/${env}/${removeDaeSuffix(application)}/${deploymentId}/`;
  });
  logger.info(`Searching for reports in ${folders.length} folders`, { folders });
  if (testType) {
    folders = folders.map((folder) => `${folder + testType}/`);
  }
  const files = await listAllFiles(cicdS3Client, folders);
  logger.info(`Found ${files.length} files`);

  const reports = extractReports(files, REPORT_OUTPUT_FILES);

  return { status: STATUS_SUCCESS, reports };
}
