import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getItemBySecondaryIndex } from 'common/dal/common.js';
import { DEPLOYMENT_BUILDS } from 'common/util/const.js';
import { ascByFieldValue } from '../util/comparators.js';

export async function getDeploymentBuilds(deploymentId) {
  const builds = await getItemBySecondaryIndex(DEPLOYMENT_BUILDS, 'DeploymentId', deploymentId);
  builds.sort(ascByFieldValue('application'));
  return { status: STATUS_SUCCESS, data: builds };
}
