import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import {
  PROVISIONING, STABLE, UNHEALTHY, HEALTHY,
} from 'common/enums/environmentStatus.js';
import { getDataEnvironmentsByStatus, saveDataEnvironment } from 'common/dal/dataEnvironments.js';
import { deleteDatastoresByEnv, saveDataStores } from 'common/dal/datastores.js';
import {
  dataStoreNameToRestoreCmd,
  dataStoreNameToVolumeSize,
  MAX_DATA_ENVIRONMENTS,
  RESERVED_DATA_ENV_NAMES,
} from 'common/util/const.js';
import getConfig from 'common/config/getConfig.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { checkBranchExistence } from 'common/service/checkBranchExistence.js';
import { saveDdeApps } from 'common/dal/applications.js';
import { getDatastoresByStacks } from 'common/dal/datastoreStacks.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import { getNewDriveLetter } from './createDrive.js';

export default async function createDataEnvironment(dataEnv, payload) {
  const {
    stacks, branch, costCentre, defaultBranch, applications,
  } = payload;
  logger.info(`create new environment ${dataEnv},branch: ${branch}, stacks: ${stacks}`);
  const regex = /^[a-z0-9_]{1,8}$/;
  if (!regex.test(dataEnv)) {
    return {
      status: STATUS_INVALID,
      message: 'Invalid environment name, please use lowercase letters, numbers and underscores, maximum length: 8 characters',
    };
  }
  if (RESERVED_DATA_ENV_NAMES.includes(dataEnv.toLowerCase())) {
    return {
      status: STATUS_INVALID,
      message: 'Reserved environment name',
    };
  }
  if (!branch) {
    return {
      status: STATUS_INVALID,
      message: 'Branch name is required',
    };
  }
  if (!costCentre) {
    return {
      status: STATUS_INVALID,
      message: 'Cost centre is required',
    };
  }
  if (!stacks) {
    return {
      status: STATUS_INVALID,
      message: 'stacks is required',
    };
  }

  if (!Array.isArray(stacks)) {
    return {
      status: STATUS_INVALID,
      message: 'Invalid stacks parameter, please use an array',
    };
  }

  logger.info('checking max number');
  const statuses = [STABLE, UNHEALTHY, HEALTHY, PROVISIONING];
  const stage = await getConfig('STAGE', 'local');
  const existingDataEnvironments = (await getDataEnvironmentsByStatus(statuses)).filter((item) => item.Stage.S === stage);
  const unReservedDataEnvironments = existingDataEnvironments.filter((item) => !RESERVED_DATA_ENV_NAMES.includes(item.Name.S));
  logger.info(`Found ${unReservedDataEnvironments.length} existing environments`);
  if (unReservedDataEnvironments.length >= MAX_DATA_ENVIRONMENTS) {
    return { status: STATUS_INVALID, message: 'Maximum number of data environments reached' };
  }
  if (unReservedDataEnvironments.some((item) => item.Name.S === dataEnv)) {
    logger.info('Environment name already exists');
    return { status: STATUS_INVALID, message: 'Environment name already exists' };
  }

  const drive = await getNewDriveLetter();
  const datastores = await getDatastoresByStacks(stacks);
  logger.info(`found ${datastores.length} datastores`, { datastores });
  const size = getSize(datastores);
  logger.info(`creating new drive ${drive} with size ${size}`);

  const logs = [];
  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  if (ecsTaskEnabled === 'true') {
    const tfTaskName = CmdAppTasks.DATA_ENV_CREATE;
    const logUrl = await runEcsTask(tfTaskName, { dataEnv, branch });
    logs.push({ task: tfTaskName, url: logUrl });
    const uniqueDatastoreTypes = [...new Set(datastores.map((dataStore) => dataStore.Type.S))];
    for (const type of uniqueDatastoreTypes) {
      logger.info(`starting provisioning for ${type}`);
      const task = dataStoreNameToRestoreCmd.get(type);
      const url = await runEcsTask(task, { dataEnv, branch });
      logger.info(`log: ${url}`);
      logs.push({ task, url });
    }
  }

  await saveDataEnvironment(dataEnv, stage, branch, defaultBranch, drive, costCentre, `${size}`, stacks, logs);
  await deleteDatastoresByEnv(dataEnv);
  await saveDataStores(dataEnv, datastores);
  await saveDdeApps(dataEnv, applications, await checkBranchExistence(defaultBranch), defaultBranch);

  return { status: STATUS_SUCCESS, message: `Environment ${dataEnv} started provisioning from branch ${branch}` };
}

function getSize(datastores) {
  const types = [...new Set(datastores.map((item) => item.Type.S))];
  return types.map((type) => {
    if (dataStoreNameToVolumeSize.has(type)) {
      return dataStoreNameToVolumeSize.get(type);
    }
    return 0;
  }).reduce((a, b) => a + b, 0);
}
