import { addCloudWatchLogs, deleteDataEnvironmentByName, getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { STATUS_INVALID, STATUS_MISSING, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import {
  ERROR, HEALTHY, STABLE, UNHEALTHY,
} from 'common/enums/environmentStatus.js';
import { deleteDataStores, getDataStoresByEnv } from 'common/dal/datastores.js';
import getConfig from 'common/config/getConfig.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import { RESERVED_DATA_ENV_NAMES } from 'common/util/const.js';

export async function deleteDataEnvironment(dataEnv) {
  const queryResults = await getDataEnvironmentByName(dataEnv);
  logger.info(`Found ${queryResults.length} environments with name: ${dataEnv}`);
  if (queryResults.length === 0) {
    return { status: STATUS_MISSING, message: `No environment found with name: ${dataEnv}` };
  }

  const allowedStatuses = [STABLE, ERROR, HEALTHY, UNHEALTHY];
  const dataEnvironment = queryResults[0];

  if (RESERVED_DATA_ENV_NAMES.includes(dataEnv.toLowerCase())) {
    return {
      status: STATUS_INVALID,
      message: 'Reserved environment',
    };
  }

  if (!allowedStatuses.includes(dataEnvironment.Status.S)) {
    return {
      status: STATUS_INVALID,
      message: `Environment ${dataEnv} is not in a valid state to be deleted, allowedStatuses are ${allowedStatuses}`,
    };
  }
  if (dataEnvironment.locked) {
    return {
      status: STATUS_INVALID,
      message: `Environment ${dataEnv} is not in a valid state to be deleted, allowedStatuses are ${allowedStatuses}`,
    };
  }

  logger.info(`Found environment ${dataEnv} to delete.`);
  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  const { Logs } = dataEnvironment;
  if (ecsTaskEnabled === 'true') {
    const cloudWatchLogs = [];
    const task = CmdAppTasks.DATA_ENV_DESTROY;
    const url = await runEcsTask(task, { dataEnv });
    cloudWatchLogs.push({ task, url });
    addCloudWatchLogs(cloudWatchLogs, Logs);
  }

  await deleteDataEnvironmentByName(dataEnv, Logs);
  logger.info(`Environment ${dataEnv} deleted.`);
  const dataStores = await getDataStoresByEnv(dataEnvironment.Name.S);
  await deleteDataStores(dataStores);
  logger.info(`Data stores for environment ${dataEnv} deleted.`);

  return { status: STATUS_SUCCESS, message: `Environment ${dataEnv} started deleting` };
}
