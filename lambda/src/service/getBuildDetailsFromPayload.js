export default async function (payload) {
  const detail = payload.detail || {};
  const root = detail['additional-information'] || {};
  const env = extractEnvVars(root);
  const projectName = env.get('PROJECT_NAME') || env.get('SERVICE_NAME') || 'Unknown Project';
  const branch = env.get('BUILD_BRANCH') || '';
  const buildVersion = env.get('BUILD_VERSION');
  const taggedBuild = env.get('TAGGED_BUILD') === 'true';
  const outputPath = env.get('OUTPUT_PATH');
  const success = detail['build-status'] === 'SUCCEEDED';
  return {
    success,
    projectName,
    status: detail['build-status'] || 'SPAMBDA FAILED',
    gitRepo: (root.source || {}).location,
    gitSha: env.get('COMMIT_SHA'),
    gitMessage: env.get('COMMIT_MESSAGE') || '',
    gitBranch: branch,
    buildVersion,
    taggedBuild,
    outputPath,
  };
}

function extractEnvVars(additionalInfo) {
  const env = new Map();
  const varsIn = (additionalInfo.environment || {})['environment-variables'] || [];
  const varsOut = additionalInfo['exported-environment-variables'] || [];
  for (const envVar of [...varsIn, ...varsOut]) {
    env.set(envVar.name, envVar.value);
  }
  return env;
}
