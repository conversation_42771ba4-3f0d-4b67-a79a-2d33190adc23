import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getAppEnvironmentByName, updateNewAppEnvironment } from 'common/dal/appEnvironments.js';
import { isValidJSON } from 'common/util/validatUtil.js';
import { updateApps } from 'common/dal/applications.js';
import { addCloudWatchLogs } from 'common/dal/dataEnvironments.js';
import {
  DELETED,
  DEPLOYING,
  HEALTHY, INDEXING,
  PROVISIONING,
  TEARING_DOWN, TESTING,
  UNHEALTHY,
  UPDATING,
} from 'common/enums/environmentStatus.js';
import getConfig from 'common/config/getConfig.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { checkBranchExistence } from 'common/service/checkBranchExistence.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';

export default async function updateAppEnvironment(appEnv, payload) {
  const {
    dataEnv, defaultBranch, contextConfig, awsAccount, locked, costCentre, applications,
    sleepTags,
  } = payload;

  const queryResults = await getAppEnvironmentByName(appEnv);
  logger.info(`Found ${queryResults.length} environments with name: ${appEnv}`);
  if (queryResults.length === 0) {
    return {
      status: STATUS_INVALID,
      message: 'app environment name does not exist',
    };
  }
  const appEnvironment = queryResults[0];
  logger.info(`Found application environment ${appEnv} to update.`);
  if (isValidJSON(contextConfig) === false) {
    return {
      status: STATUS_INVALID,
      message: 'Context config should be a valid JSON',
    };
  }

  if ([PROVISIONING, TEARING_DOWN, DELETED, UPDATING, DEPLOYING, TESTING, INDEXING].includes(appEnvironment.Status.S)) {
    return {
      status: STATUS_INVALID,
      message: 'This app environment is being updated, please wait.',
    };
  }

  const validStatuses = [HEALTHY, UNHEALTHY];

  if (!validStatuses.includes(appEnvironment.Status.S)) {
    return {
      status: STATUS_INVALID,
      message: `Status ${appEnvironment.Status.S} is invalid. valid statuses are ${validStatuses.join(', ')}`,
    };
  }
  if (costCentre === '') {
    return {
      status: STATUS_INVALID,
      message: 'Cost centre is required',
    };
  }
  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  const { Logs: logs } = appEnvironment;
  if (ecsTaskEnabled === 'true') {
    const cloudWatchLogs = [];
    const task = CmdAppTasks.APP_ENV_UPDATE;
    const url = await runEcsTask(task, { appEnv });
    cloudWatchLogs.push({ task, url });
    addCloudWatchLogs(cloudWatchLogs, logs);
  }
  logger.info('updating new app environment');
  await updateNewAppEnvironment({
    appEnv, dataEnv, defaultBranch, contextConfig, awsAccount, locked, costCentre, logs, sleepTags,
  });
  logger.info('updating applications');
  await updateApps(appEnv, ApplicationType.DAE, applications, await checkBranchExistence(defaultBranch), defaultBranch);

  logger.info('re-create context');
  return { status: STATUS_SUCCESS, message: `App Environment ${appEnv} started updating ` };
}

export async function getPodsConfig(applications) {
  return applications.reduce((acc, app) => {
    acc[`${app.name}_PODS`.toUpperCase()] = parseInt(app.pods, 10);
    return acc;
  }, {});
}
