import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import {
  getAppEnvironmentByName,
  reindexAppEnvironmentByName,
  updateAppEnvironmentStatus,
} from 'common/dal/appEnvironments.js';
import { INDEXING } from 'common/enums/environmentStatus.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import { addCloudWatchLogs } from 'common/dal/dataEnvironments.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { canPerformReindex } from 'common/util/canPerformReindex.js';

export default async function createIndex(appEnv, payload) {
  const { success, message } = await canPerformReindex(appEnv);
  if (!success) {
    return { status: STATUS_INVALID, message };
  }
  await updateAppEnvironmentStatus(appEnv, INDEXING);
  const appEnvironment = (await getAppEnvironmentByName(appEnv))[0];
  const { Logs: logs } = appEnvironment;
  const cloudWatchLogs = [];
  const task = CmdAppTasks.APP_ENV_REINDEX;
  const payloadJson = JSON.stringify(payload);
  const url = await runEcsTask(task, { appEnv, payload: payloadJson });
  cloudWatchLogs.push({ task, url });
  addCloudWatchLogs(cloudWatchLogs, logs);
  await reindexAppEnvironmentByName(appEnv, logs);
  return { status: STATUS_SUCCESS, message: `App Environment ${appEnv} started reindexing ` };
}
