import {
  DB_SCRIPTS, DEPLOY_BUCKET_NAME, DIFF_SUMMARY, MONARCH_K8S_BUCKET, SHA, TMP_FOLDER,
} from 'common/util/const.js';
import fs from 'fs';
import { downloadFile, uploadFileToS3 } from 'common/util/s3util.js';
import AdmZip from 'adm-zip';
import { deleteFolder, readFileToString } from 'common/util/fileUtil.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function readBuildInfo(name, branch, datastore) {
  logger.info(`branch: ${branch}`);
  const folder = `${TMP_FOLDER}/${name}/${branch.replaceAll('/', '_')}/${datastore}`;
  const fileName = `${folder}/${datastore}.zip`;
  const bucketKey = `${DB_SCRIPTS}/build/${branch.replaceAll('/', '_')}/${datastore}.zip`;
  logger.info('bucketKey', { bucketKey });
  fs.mkdirSync(`${folder}`, { recursive: true });
  try {
    await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);
  } catch (e) {
    if (e.message.includes('The specified key does not exist.')) {
      return null;
    }
    throw e;
  }
  const zip = new AdmZip(fileName, {});
  zip.extractAllTo(`${folder}/`, true, true);
  const diffFilePath = `${folder}/${DIFF_SUMMARY}`;
  const targetDiffPath = `db-scripts/deploy/${name}/${datastore}/${DIFF_SUMMARY}`;
  await uploadFileToS3(cicdS3Client, DEPLOY_BUCKET_NAME, diffFilePath, targetDiffPath);
  const diffSummary = readFileToString(diffFilePath);
  const sha = readFileToString(`${folder}/${SHA}`);
  logger.info('build info', { sha, diffSummary });
  deleteFolder(folder);
  return { sha, diffSummary };
}
