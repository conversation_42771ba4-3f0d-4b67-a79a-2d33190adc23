import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatAppEnvironments, getAppEnvironmentByName, getAppsList } from 'common/dal/appEnvironments.js';
import { ApplicationType } from 'common/enums/applicationType.js';

export async function getAppEnvironment(name) {
  const existingAppEnvironments = await getAppEnvironmentByName(name);

  logger.info(`Found ${existingAppEnvironments.length} existing environments`);

  const applicationsList = await getAppsList(existingAppEnvironments, ApplicationType.DAE);

  const result = (await formatAppEnvironments(existingAppEnvironments, applicationsList))[0];

  return { status: STATUS_SUCCESS, environment: result };
}
