import { STATUS_INVALID, STATUS_MISSING, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import {
  addLogsToDataEnv,
  getDataEnvironmentByName,
  updateEnvironmentStatusByName,
} from 'common/dal/dataEnvironments.js';
import getConfig from 'common/config/getConfig.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { dataStoreNameToRestoreCmd } from 'common/util/const.js';
import { ERROR, PROVISIONING } from 'common/enums/environmentStatus.js';
import { getDataStoresByEnv } from 'common/dal/datastores.js';

export async function restoreDatastore(dataEnv, datastoreName) {
  logger.info(`restoreDatastore  ${dataEnv} ${datastoreName}`);

  const queryResults = await getDataEnvironmentByName(dataEnv);
  logger.info(`Found ${queryResults.length} environments with name: ${dataEnv}`);
  if (queryResults.length === 0) {
    return { status: STATUS_MISSING, message: `No environment found with name: ${dataEnv}` };
  }
  const dataEnvironment = queryResults[0];
  const dataStores = await getDataStoresByEnv(dataEnv);
  const datastore = dataStores.find((item) => item.Name.S === datastoreName);
  logger.info(`Found datastore ${datastoreName} to restore.`, datastore);
  if (!datastore) {
    return { status: STATUS_MISSING, message: `No datastore found with name: ${datastoreName}` };
  }
  if (datastore.Status.S !== ERROR) {
    return {
      status: STATUS_INVALID,
      message: `Datastore ${datastoreName} is not in error status.`,
    };
  }
  const anotherOneInProgress = dataStores.filter((item) => item.Name.S !== datastoreName).find((item) => item.Status.S === PROVISIONING);
  if (anotherOneInProgress) {
    return {
      status: STATUS_INVALID,
      message: `Another datastore is in provisioning status: ${anotherOneInProgress.Name.S}`,
    };
  }

  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  if (ecsTaskEnabled === 'true') {
    const task = dataStoreNameToRestoreCmd.get(datastore.Type.S);
    const url = await runEcsTask(task, { dataEnv, datastore: datastore.Name.S });
    logger.info(`log: ${url}`);
    await addLogsToDataEnv(dataEnv, task, url);
  }
  await updateEnvironmentStatusByName(dataEnv, PROVISIONING, dataEnvironment.EndTime.N);

  return {
    status: STATUS_SUCCESS,
    message: `Datastore Restore started for ${dataEnv} ${datastoreName}`,
  };
}
