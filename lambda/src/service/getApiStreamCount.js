import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getAppEnvironmentsByDataEnv } from 'common/dal/appEnvironments.js';
import { getApplicationByKey } from 'common/dal/applications.js';
import { API_STREAM_APP } from 'common/util/const.js';
import {
  DEPLOYING, HEALTHY, STABLE, TESTING, UNHEALTHY,
} from 'common/enums/environmentStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';

export async function getApiStreamCount(dataEnv) {
  const appEnvironments = await getAppEnvironmentsByDataEnv(dataEnv);
  const stableAppEnvironments = appEnvironments.filter((appEnv) => [DEPLOYING, TESTING, STABLE, HEALTHY, UNHEALTHY].includes(appEnv.Status.S));
  const count = {
    [API_STREAM_APP]: 0,
  };

  for (const appEnv of stableAppEnvironments) {
    const apiApps = await getApplicationByKey(`${ApplicationType.DAE}-${appEnv.Name.S}-${API_STREAM_APP}`);

    if (apiApps.length > 0) {
      count[API_STREAM_APP] += 1;
    }
  }

  return { status: STATUS_SUCCESS, count };
}
