import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { STATUS_MISSING, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { readBuildInfo } from './readZipFiles.js';

export async function getDatabaseBuild(name, datastore) {
  const queryResults = await getDataEnvironmentByName(name);
  logger.info(`Found ${queryResults.length} environments with name: ${name}`);
  if (queryResults.length === 0) {
    return { status: STATUS_MISSING, message: `No environment found with name: ${name}` };
  }

  const dataEnvironment = queryResults[0];
  const branch = dataEnvironment.Branch.S;

  const buildInfo = await readBuildInfo(name, branch, datastore);
  if (buildInfo === null) {
    return { status: STATUS_MISSING, message: `No build found for ${name} ${datastore}` };
  }
  return { status: STATUS_SUCCESS, diff: buildInfo.diffSummary.trim().split('\n') };
}
