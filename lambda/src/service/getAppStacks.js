import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatAppStacks, getAllAppStacks } from 'common/dal/appStacks.js';
import { ascByFieldValue } from '../util/comparators.js';

export async function getAppStacks(queryStringParameters) {
  let appStacks = await getAllAppStacks();
  const stack = queryStringParameters?.stack;
  if (stack) {
    appStacks = appStacks.filter((appStack) => appStack.Stacks.L.map((it) => it.S).includes(stack));
  }

  const result = formatAppStacks(appStacks).sort(ascByFieldValue('name'));

  return { status: STATUS_SUCCESS, appStacks: result };
}
