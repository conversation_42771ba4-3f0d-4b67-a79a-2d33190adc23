import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { paginateArray, scanTableWithFilter } from 'common/dal/common.js';
import { DEFAULT_PAGE_SIZE, TEST_REPORTS } from 'common/util/const.js';
import { formatTestReport } from 'common/service/testReports.js';
import { descByFieldValue } from '../util/comparators.js';

export async function getTestReports(queryStringParameters) {
  logger.info('Getting getTestReports', { queryStringParameters });
  const {
    limit = DEFAULT_PAGE_SIZE, offset = 0, env, testType, appName, reportId,
  } = queryStringParameters;

  let testReports = await scanTableWithFilter(TEST_REPORTS, {
    reportId, testType, appName, env,
  });

  testReports = testReports.map((testReport) => ({ ...testReport, ...formatTestReport(testReport.testResults) }));
  testReports.sort(descByFieldValue('createdAt'));
  const pagedTestReports = paginateArray(testReports, limit, offset);

  return {
    status: STATUS_SUCCESS,
    data: pagedTestReports.currentPageData,
    total: testReports.length,
  };
}
