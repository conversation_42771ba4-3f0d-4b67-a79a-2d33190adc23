import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getApiKeyByName } from 'common/service/apiGateway.js';
import { getAppEnvironmentByName } from 'common/dal/appEnvironments.js';
import { RESERVED_APP_ENV_NAMES } from 'common/util/const.js';

export async function getIndex(appEnv) {
  const appEnvironments = await getAppEnvironmentByName(appEnv);
  if (appEnvironments.length === 0) {
    logger.info(`Could not find app environment ${appEnv}`);
    return {
      status: STATUS_SUCCESS,
      indices: [],
    };
  }
  const dataEnv = appEnvironments[0].DataEnv.S;
  const apiKeyName = `${appEnv}-dae-lambda-api-key`;
  const apiKeyMap = {
    dev: 'website-dev',
    test: 'website-test',
    uat: 'website-uat',
  };

  const lambdaApiKey = await getApiKeyByName(apiKeyMap[appEnv] ?? apiKeyName, 5, 2);
  if (!lambdaApiKey) {
    logger.warn(`failed to get api key for ${appEnv}`);
    return {
      status: STATUS_SUCCESS,
      indices: [],
    };
  }
  let baseUrl = `https://${appEnv}.dae.qvapi.co.nz`;
  if (RESERVED_APP_ENV_NAMES.includes(appEnv)) {
    baseUrl = `https://${appEnv}.qvapi.co.nz`;
  }

  const url = `${baseUrl}/stream/get-index/${dataEnv}`;
  logger.info(`fetching index from ${url}`);
  let response;
  try {
    response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': lambdaApiKey,
      },
    });
  } catch (error) {
    logger.warn(`Network error while fetching index: ${error.message}`);
    return {
      status: STATUS_SUCCESS,
      indices: [],
    };
  }
  if (!response.ok) {
    logger.warn(`failed to get index ${response.status}`);
    return {
      status: STATUS_SUCCESS,
      indices: [],
    };
  }
  const json = await response.json();
  return {
    status: STATUS_SUCCESS,
    indices: json.indices,
  };
}
