import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { APPLICATIONS, ARTIFACT_FILE_NAME } from 'common/util/const.js';
import { updateItem } from 'common/dal/common.js';
import { updateApplicationDeployed } from 'common/dal/applications.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { deployBuildArtifact, deployBuildArtifactLegacy } from '../dal/appDeployArtifacts.js';

export default async function deployAppBuild(appName, buildName, buildMetadata, envName = null) {
  if (!envName) {
    console.info(`Legacy deploy for ${appName} ${buildName}`);
    const success = await deployBuildArtifactLegacy(appName, buildName);
    return {
      status: success ? STATUS_SUCCESS : STATUS_INVALID,
      message: `Legacy deploy ${success ? 'started' : 'failed'}, for ${appName} ${buildName}`,
    };
  }

  logger.info(`Deploying ${appName} ${buildName} to ${envName}`);
  const success = await deployBuildArtifact(appName, buildName, envName);
  const bucketKey = `${appName}/build/${buildName}/${ARTIFACT_FILE_NAME}`;
  const applicationKey = `dae-${envName}-${appName.replace('monarch-', '')}`;
  const deployBranch = buildMetadata?.branch || '';

  const updateData = {
    deployBranch,
    bucketKey,
    sha: buildMetadata ? buildMetadata['commit-sha'] : '',
  };
  if (success) {
    await updateApplicationDeployed(applicationKey);
    updateData.deployed = Date.now().toString();
    updateData.status = PipelineStatus.DEPLOYED;
  } else {
    updateData.status = PipelineStatus.FAILED;
  }

  await updateItem(APPLICATIONS, 'key', applicationKey, updateData);

  return {
    status: success ? STATUS_SUCCESS : STATUS_INVALID,
    message: `Deploy ${success ? 'started' : 'failed'}, for ${appName} ${buildName} to ${envName}`,
  };
}
