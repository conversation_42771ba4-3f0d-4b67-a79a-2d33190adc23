import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { updateApps } from 'common/dal/applications.js';
import { addCloudWatchLogs, getDataEnvironmentByName, updateNewDataEnvironment } from 'common/dal/dataEnvironments.js';
import {
  HEALTHY, STABLE, UNHEALTHY, UPDATING,
} from 'common/enums/environmentStatus.js';
import getConfig from 'common/config/getConfig.js';
import { runEcsTask } from 'common/service/ecsTools.js';
import { checkBranchExistence } from 'common/service/checkBranchExistence.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import { RESERVED_DATA_ENV_NAMES } from 'common/util/const.js';

export default async function updateDataEnvironment(dataEnv, payload) {
  const {
    defaultBranch, costCentre, applications,
  } = payload;

  const queryResults = await getDataEnvironmentByName(dataEnv);
  logger.info(`Found ${queryResults.length} environments with name: ${dataEnv}`);
  if (queryResults.length === 0) {
    return {
      status: STATUS_INVALID,
      message: 'data environment name does not exist',
    };
  }
  const dataEnvironment = queryResults[0];
  logger.info(`Found data environment ${dataEnv} to update.`);

  if (RESERVED_DATA_ENV_NAMES.includes(dataEnv.toLowerCase())) {
    return {
      status: STATUS_INVALID,
      message: 'Reserved environment',
    };
  }

  if (dataEnvironment.Status.S === UPDATING) {
    return {
      status: STATUS_INVALID,
      message: 'This data environment is being updated, please wait.',
    };
  }

  if (![STABLE, HEALTHY, UNHEALTHY].includes(dataEnvironment.Status.S)) {
    return {
      status: STATUS_INVALID,
      message: 'Status is invalid, only stable data environment can be updated.',
    };
  }
  if (costCentre === '') {
    return {
      status: STATUS_INVALID,
      message: 'Cost centre is required',
    };
  }
  const ecsTaskEnabled = await getConfig('ECS_TASK_ENABLED');
  const { Logs } = dataEnvironment;
  if (ecsTaskEnabled === 'true') {
    const cloudWatchLogs = [];
    const task = CmdAppTasks.DATA_ENV_UPDATE;
    const url = await runEcsTask(task, { dataEnv });
    cloudWatchLogs.push({ task, url });
    addCloudWatchLogs(cloudWatchLogs, Logs);
  }
  logger.info('updating data environment');
  await updateNewDataEnvironment(dataEnv, defaultBranch, Logs);
  logger.info('updating applications');
  await updateApps(dataEnv, ApplicationType.DDE, applications, await checkBranchExistence(defaultBranch), defaultBranch);

  return { status: STATUS_SUCCESS, message: `Data Environment ${dataEnv} started updating ` };
}
