import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatAppEnvironments, getAppEnvironmentsByFilter } from 'common/dal/appEnvironments.js';
import { descByFieldFn } from '../util/comparators.js';

export async function getAppEnvironments(filter) {
  const existingAppEnvironments = await getAppEnvironmentsByFilter(filter);

  existingAppEnvironments.sort(descByFieldFn('Created'));

  logger.info(`Found ${existingAppEnvironments.length} existing environments`);

  const result = await formatAppEnvironments(existingAppEnvironments);

  return { status: STATUS_SUCCESS, environments: result };
}
