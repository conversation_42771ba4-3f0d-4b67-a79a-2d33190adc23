import { download, streamToBuffer, streamToString } from 'common/util/s3util.js';
import { DEPLOYMENT_REPORTS_BUCKET } from 'common/util/const.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function readFileByKey(key) {
  const contentType = getContentType(key);
  logger.info(`Reading file ${key} with content type ${contentType}`);
  let isBase64Encoded = false;
  let s3Body;
  let s3Response;
  try {
    s3Response = await download(cicdS3Client, DEPLOYMENT_REPORTS_BUCKET, key);
    s3Body = s3Response.Body;
  } catch (e) {
    logger.error('ERR-API-001', `Error reading file ${key}`, e);
    return {
      statusCode: 404,
    };
  }

  if (isStringObject(contentType)) {
    s3Body = await streamToString(s3Body);
  } else if (isBase64Object(contentType)) {
    const buffer = await streamToBuffer(s3Body);
    s3Body = buffer.toString('base64');
    isBase64Encoded = true;
  } else {
    logger.warn(`unsupported content type: ${contentType}`);
    return {
      statusCode: 400,
    };
  }
  return {
    contentType,
    s3Body,
    isBase64Encoded,
  };
}

export function isStringObject(contentType) {
  const validContentTypes = [
    'application/json',
    'application/javascript',
    'text/html',
    'text/plain',
    'text/css',
  ];

  return validContentTypes.includes(contentType);
}

export function isBase64Object(contentType) {
  const validContentTypes = [
    'font/woff2',
    'font/woff',
    'image/png',
    'image/gif',
  ];

  return validContentTypes.includes(contentType);
}

export function getContentType(filename) {
  const extension = filename.split('.').pop().toLowerCase();
  switch (extension) {
    case 'js':
      return 'application/javascript';
    case 'json':
      return 'application/json';
    case 'css':
      return 'text/css';
    case 'html':
      return 'text/html';
    case 'txt':
      return 'text/plain';
    case 'woff':
      return 'font/woff';
    case 'woff2':
      return 'font/woff2';
    case 'ttf':
      return 'font/ttf';
    case 'eot':
      return 'application/vnd.ms-fontobject';
    case 'otf':
      return 'font/otf';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'svg':
      return 'image/svg+xml';
    case 'ico':
      return 'image/x-icon';
    default:
      return 'application/octet-stream'; // default to binary stream if unknown
  }
}
