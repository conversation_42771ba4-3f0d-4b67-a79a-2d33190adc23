import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatDatastoreStacks, getAllDatastoreStacks } from 'common/dal/datastoreStacks.js';

export async function getDatastoreStacks(queryStringParameters) {
  let datastoreStacks = await getAllDatastoreStacks();
  const stacks = queryStringParameters?.stacks;
  if (stacks) {
    const stackArr = stacks.split(',');
    datastoreStacks = datastoreStacks.filter((appStack) => appStack.Stacks.L.some((stack) => stackArr.includes(stack.S)));
  }

  const result = formatDatastoreStacks(datastoreStacks);
  return { status: STATUS_SUCCESS, datastoreStacks: result };
}
