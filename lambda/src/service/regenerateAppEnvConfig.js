import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getAppEnvironmentByName, updateAppEnvironmentJsonConfig } from 'common/dal/appEnvironments.js';
import { getDefaultAppEnvConfig } from 'common/service/getAppEnvConfig.js';

export default async function regenerateAppEnvConfig(name, dataEnv, payload) {
  const { contextConfig } = payload;
  if (!contextConfig) {
    return {
      status: STATUS_INVALID,
      message: 'ContextConfig is required',
    };
  }
  const { update } = payload;
  const { account } = payload;
  const { includeQivs } = payload;
  let result = {};
  if (update) {
    const queryResults = await getAppEnvironmentByName(name);
    if (queryResults.length === 0) {
      return {
        status: STATUS_INVALID,
        message: 'App environment not found',
      };
    }
    const appEnvironment = queryResults[0];
    const defaultContextConfig = appEnvironment.DefaultContextConfig.S;
    result = {
      ...JSON.parse(contextConfig),
      ...JSON.parse(defaultContextConfig),
    };

    await updateAppEnvironmentJsonConfig(name, {
      ...JSON.parse(appEnvironment.ContextConfig.S),
      ...JSON.parse(defaultContextConfig),
    });
  } else {
    result = await getDefaultAppEnvConfig(name, dataEnv, account, includeQivs);
  }

  return { status: STATUS_SUCCESS, contextConfig: result };
}
