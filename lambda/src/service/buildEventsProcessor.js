import { saveBuilds } from 'common/dal/builds.js';
import { removeDaeSuffix } from 'common/util/appNameUtli.js';
import Logger from 'common/util/Logger.js';
import { getAppEnvironmentsByStatus } from 'common/dal/appEnvironments.js';
import {
  DEPLOYING, HEALTHY, STABLE, TESTING, UNHEALTHY,
} from 'common/enums/environmentStatus.js';
import { getApplicationByKey } from 'common/dal/applications.js';
import { getAllAppStacks, isDaeApplication } from 'common/dal/appStacks.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { Platform } from 'common/enums/platform.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { RESERVED_APP_ENV_NAMES } from 'common/util/const.js';
import getBuildDetailsFromPayloadService from './getBuildDetailsFromPayload.js';
import { getBuildArtifacts, getBuildArtifactsLegacy } from '../dal/appDeployArtifacts.js';

export default async function buildEventsProcessor(payload) {
  global.logger = new Logger();
  const buildDetails = await getBuildDetailsFromPayloadService(payload);
  if (buildDetails.status === 'SUCCEEDED') {
    const appStacks = await getAllAppStacks();
    const k8sApplications = appStacks.filter((appStack) => appStack.Platform.S === Platform.K8S).map((appStack) => appStack.Name.S);
    let appName = buildDetails.projectName;
    logger.info('k8sApplications ', { k8sApplications });
    if (k8sApplications.includes(appName)) {
      if (!appName.includes('monarch')) {
        appName = `monarch-${appName}`;
      }
    }
    let bucketKey = '';
    appName = removeDaeSuffix(appName);
    logger.info(`appName ${appName}`);
    const builds = await getBuildArtifacts(appName);
    const branchName = buildDetails.gitBranch.replace('/', '_');
    logger.info(`branchName ${branchName}`);

    if (builds.length > 0) {
      const results = builds.filter((build) => {
        if (buildDetails.outputPath) {
          return build.name === buildDetails.outputPath;
        }
        return build.name === branchName;
      }).sort((build) => build.lastModified);
      if (results.length === 0) {
        logger.info(`No build found for branch ${buildDetails.gitBranch}`);
        return;
      }
      bucketKey = results[0].key;
    } else {
      const legacyBuilds = await getBuildArtifactsLegacy(appName);
      if (legacyBuilds.length > 0) {
        let results = legacyBuilds.filter((build) => build.name.includes(buildDetails.buildVersion));
        if (results.length === 0) {
          results = legacyBuilds.filter((build) => build.name.includes(branchName));
        }
        if (results.length === 0) {
          logger.info(`No build found for branch ${buildDetails.gitBranch}`);
          return;
        }
        bucketKey = results.sort((build) => build.lastModified)[0].key;
      }
    }
    logger.info(`bucketKey ${bucketKey}`);

    if (bucketKey === '') {
      logger.info('bucketKey not found');
    }
    await saveBuilds(buildDetails, bucketKey);
    await releaseBuilds(buildDetails.projectName, branchName);
  } else {
    logger.info('Build failed', { buildDetails });
  }
}

export async function releaseBuilds(appName, branch) {
  const isDae = await isDaeApplication(appName);
  logger.info(`isDae ${isDae}`);
  if (!isDae) {
    logger.info(`Not a dae application ${appName}`);
    return;
  }
  logger.info(`releaseBuilds ${appName} ${branch}`);
  const stableEnvironments = await getAppEnvironmentsByStatus([HEALTHY, UNHEALTHY, DEPLOYING, TESTING, STABLE]);
  const nonReserveEnvironments = stableEnvironments.filter((env) => !RESERVED_APP_ENV_NAMES.includes(env.Name.S));
  logger.info(`nonReserveEnvironments ${nonReserveEnvironments.map((env) => env.Name.S).join(', ')}`);
  for (const appEnvironment of nonReserveEnvironments) {
    await releaseBuild(appEnvironment, appName, branch);
  }
}

async function releaseBuild(appEnvironment, appName, branch) {
  const appEnvName = appEnvironment.Name.S;
  logger.info(`appEnvName ${appEnvName}`);
  const applications = await getApplicationByKey(`${ApplicationType.DAE}-${appEnvName}-${appName}`);
  logger.info(`applications length ${applications.length}`);
  if (applications.length === 0) {
    logger.info(`No application found for ${appEnvName}-${appName}`);
    return;
  }
  const application = applications[0];

  if (application.DeployBranch.S.replace('/', '_') !== branch) {
    logger.info(`branch ${branch} not exist, skip`);
    return;
  }
  logger.info(`branch ${branch} exist, deploy`);
  const deploymentId = await initDeployment(appEnvName, ApplicationType.DAE, appName, 'auto-deployment');
  logger.info(`deploymentId ${deploymentId} initiated`);
}
