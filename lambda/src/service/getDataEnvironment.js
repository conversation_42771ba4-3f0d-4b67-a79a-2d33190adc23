import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getAppsList } from 'common/dal/appEnvironments.js';
import { formatDataEnvironments, getDataEnvironmentByName, getDataStoreList } from 'common/dal/dataEnvironments.js';
import { ApplicationType } from 'common/enums/applicationType.js';

export async function getDataEnvironment(name) {
  const existingDataEnvironments = await getDataEnvironmentByName(name);

  logger.info(`Found ${existingDataEnvironments.length} existing environments`);
  const dataStoreList = await getDataStoreList(existingDataEnvironments);

  const applicationsList = await getAppsList(existingDataEnvironments, ApplicationType.DDE);
  logger.info(`Found ${applicationsList.length} existing applications`);

  const environment = formatDataEnvironments(existingDataEnvironments, dataStoreList, applicationsList)[0];
  return { status: STATUS_SUCCESS, environment };
}
