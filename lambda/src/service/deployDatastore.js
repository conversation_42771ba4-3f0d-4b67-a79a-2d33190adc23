import { ARTIFACT_FILE_NAME, DB_SCRIPTS, DEPLOY_BUCKET_NAME } from 'common/util/const.js';
import { STATUS_MISSING, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { copyFile } from '../dal/appDeployArtifacts.js';

export async function deployDatastore(envName, datastore) {
  logger.info(`deployDatastore   ${envName} ${datastore}`);

  const queryResults = await getDataEnvironmentByName(envName);
  logger.info(`Found ${queryResults.length} environments with name: ${envName}`);
  if (queryResults.length === 0) {
    return { status: STATUS_MISSING, message: `No environment found with name: ${envName}` };
  }

  const dataEnvironment = queryResults[0];
  const branch = dataEnvironment.Branch.S;
  logger.info(`branch: ${branch}`);
  const from = `${DEPLOY_BUCKET_NAME}/${DB_SCRIPTS}/build/${branch.replaceAll('/', '_')}/${datastore}.zip`;
  const to = `${DB_SCRIPTS}/deploy/${envName}/${datastore}/${ARTIFACT_FILE_NAME}`;
  logger.info(`copying ${from} to ${to}`);
  await copyFile(from, to);

  return {
    status: STATUS_SUCCESS,
    message: `Datastore deployment started for ${envName} ${datastore}`,
  };
}
