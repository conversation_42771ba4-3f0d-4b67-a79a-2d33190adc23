//
// Functions to get the user details from the access token and event
//
// This is based on the event request context structure passed into the lambda function
// "requestContext": {
//     "accountId": "************",
//     "apiId": "3e1qk5lga1",
//     "authorizer": {
//       "jwt": {
//         "claims": {
//           "aud": "[https://costbuilder.qv.co.nz https://qvmonarch-dev.au.auth0.com/userinfo]",
//           "azp": "mhdlNtbWXKoLwwH7Qjl3dWd1z6rR7iTl",
//           "exp": "**********",
//           "scope": "openid profile email",
//           "https://qv.co.nz/email": "<EMAIL>",
//           "iat": "**********",
//           "iss": "https://qvmonarch-dev.au.auth0.com/",
//           "permissions": "[admin data user]",
//           "sub": "ad|QVLDAPConnection|bafa9586-1c3e-4dcf-94b7-55d95ed7d966"
//         },
//         "scopes": null
//       }
//     }
// }

import jwt from 'jsonwebtoken';
import getConfig from 'common/config/getConfig.js';
import { parsePermission } from 'common/util/permissionUtil.js';

export async function hasPermission(claims) {
  const functionPermissions = (process.env.PERMISSIONS || '')
    .split(' ')
    .filter((permission) => permission.trim());
  const permissionsRelation = process.env.PERMISSIONS_RELATION || 'OR';
  if (!functionPermissions.length) {
    return true;
  }

  const userPermissions = claims?.permissions;
  if (!userPermissions) {
    return false;
  }
  const userPermissionList = Array.isArray(userPermissions) ? userPermissions : userPermissions.substring(1, userPermissions.length - 1).split(' ');
  const permissions = [...new Set(
    userPermissionList
      .flatMap((permission) => {
        const [action, resource] = permission.split(':');
        return parsePermission(action, resource);
      }),
  )];

  if (permissionsRelation === 'OR') {
    for (const permission of functionPermissions) {
      if (permissions.includes(permission)) {
        return true;
      }
    }
    return false;
  }
  if (permissionsRelation === 'AND') {
    for (const permission of functionPermissions) {
      if (!permissions.includes(permission)) {
        return false;
      }
    }
    return true;
  }
  for (const permission of functionPermissions) {
    if (userPermissionList.includes(permission)) {
      return true;
    }
  }

  return false;
}

async function isLocal() {
  const auth = await getConfig('LOCAL_DEV_WITHOUT_AUTH');
  return (auth === 'true');
}

export async function userInfo(event) {
  if (await isLocal()) {
    return { email: '<EMAIL>', userId: 'local' };
  }

  const userId = event?.requestContext?.authorizer?.jwt?.claims?.sub;
  if (!userId) {
    return { email: 'unknown', userId: 'unknown' };
  }

  let email = event?.requestContext?.authorizer?.jwt?.claims?.email;
  if (!email) {
    try {
      let url = event?.requestContext?.authorizer?.jwt?.claims?.iss;
      if (url) {
        url = `${url}${url.endsWith('/') ? '' : '/'}userinfo`;
        const response = await fetch(url, {
          headers: {
            Authorization: event.headers.authorization,
            'Content-Type': 'application/json',
          },
        });
        const data = await response.json();
        email = data?.email;
      }
    } catch (error) {
      logger.error('ERR-CICD-143', 'Error fetching user email', error);
    }
  }

  return {
    email: email || userId,
    userId,
  };
}
function isTokenExpired(exp) {
  const currentTime = Math.floor(Date.now() / 1000);
  logger.info(`Token expiry time: ${exp}, current time: ${currentTime}`);
  return exp < currentTime;
}

function getTokenFromCookie(cookies) {
  if (!cookies) return null;
  for (let cookie of cookies) {
    cookie = cookie.trim();
    if (cookie.startsWith('bearer=')) {
      return cookie.split('=')[1];
    }
  }
  return null;
}

export function getClaims(event) {
  let claims;
  const authorizer = event?.requestContext?.authorizer;
  const bearerFromQuery = event.queryStringParameters?.bearer;
  const bearerFromCookie = getTokenFromCookie(event.cookies);

  if (authorizer && authorizer.jwt && authorizer.jwt.claims) {
    logger.info('Authorizer provided in request context', { authorizer });
    claims = authorizer?.jwt.claims;
  } else if (bearerFromQuery) {
    logger.info('Bearer token provided in query string', { bearer: bearerFromQuery });
    claims = jwt.decode(bearerFromQuery);
    if (isTokenExpired(claims?.exp)) {
      logger.info('Bearer token expired');
      return null;
    }
  } else if (bearerFromCookie != null) {
    logger.info('Bearer token provided in cookie', { bearer: bearerFromCookie });
    claims = jwt.decode(bearerFromCookie);
    if (isTokenExpired(claims?.exp)) {
      logger.info('Bearer token expired');
      return null;
    }
  } else {
    logger.info('No bearer token provided');
    return null;
  }
  return claims;
}
