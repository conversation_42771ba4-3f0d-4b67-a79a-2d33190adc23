import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getBuildArtifacts, getBuildArtifactsLegacy, getEnvironments } from '../dal/appDeployArtifacts.js';

export default async function getAppBuilds(appName, includeDae) {
  const builds = await getBuildArtifacts(appName);
  if (builds.length) {
    const environments = await getEnvironments(appName, includeDae);
    return {
      status: STATUS_SUCCESS,
      builds,
      environments,
    };
  }

  const legacyBuilds = await getBuildArtifactsLegacy(appName);
  if (legacyBuilds.length) {
    return {
      status: STATUS_SUCCESS,
      legacyBuilds,
    };
  }

  return {
    status: STATUS_INVALID,
    message: 'No artifacts found',
  };
}
