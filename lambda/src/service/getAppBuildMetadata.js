import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getArtifactMetadata, getArtifactMetadataLegacy } from '../dal/appDeployArtifacts.js';

export default async function getAppBuildMetadata(appName, options = {}) {
  const { buildNames = [], envNames = [], isLegacy } = options;
  if (isLegacy) {
    if (!buildNames?.length) {
      return {
        status: STATUS_INVALID,
        message: 'build name is required.',
      };
    }
    const buildRequests = buildNames.map((name) => getArtifactMetadataLegacy(appName, name));
    const env = await getArtifactMetadataLegacy(appName);
    const builds = await Promise.all(buildRequests);
    return {
      status: STATUS_SUCCESS,
      builds,
      envs: [env],
    };
  }
  const buildRequests = buildNames.map((name) => getArtifactMetadata(appName, name, 'build'));
  const envRequests = envNames.map((name) => getArtifactMetadata(appName, name, 'deploy'));
  const builds = await Promise.all(buildRequests);
  const envs = await Promise.all(envRequests);
  return {
    status: STATUS_SUCCESS,
    builds,
    envs,
  };
}
