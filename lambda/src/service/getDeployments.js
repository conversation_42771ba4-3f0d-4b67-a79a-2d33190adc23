import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { formatDeployments, getAllDeployments, getDeploymentByEnvironmentKeyword } from 'common/dal/deployments.js';
import { paginateArray } from 'common/dal/common.js';
import { DEFAULT_PAGE_SIZE } from 'common/util/const.js';
import { descByFieldFn } from '../util/comparators.js';

export async function getDeployments(queryStringParameters) {
  const {
    limit = DEFAULT_PAGE_SIZE, offset = '0', env: appEnv, type,
  } = queryStringParameters;
  logger.info(`Getting deployments with limit ${limit} and offset ${offset} and env ${appEnv}`);
  let deployments;
  if (appEnv) {
    deployments = await getDeploymentByEnvironmentKeyword(appEnv);
  } else {
    deployments = await getAllDeployments();
  }
  deployments = deployments.filter((deployment) => deployment.Type?.S === type);
  deployments.sort(descByFieldFn('CreatedAt'));
  const pagedDeployments = paginateArray(deployments, parseInt(limit, 10), parseInt(offset, 10));
  const result = formatDeployments(pagedDeployments.currentPageData);

  return {
    status: STATUS_SUCCESS,
    deployments: result,
    total: deployments.length,
  };
}
