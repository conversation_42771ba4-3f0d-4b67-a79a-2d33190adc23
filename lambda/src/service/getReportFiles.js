import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { DEPLOYMENT_REPORTS_BUCKET, TEST_REPORTS } from 'common/util/const.js';
import { listAllFiles } from 'common/util/s3util.js';
import { getItemById } from 'common/dal/common.js';
import { formatTestReport } from 'common/service/testReports.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function getReportFiles(reportId) {
  const testReport = await getItemById(TEST_REPORTS, { ReportId: reportId });
  if (!testReport) {
    return { status: STATUS_INVALID };
  }

  const files = await listAllFiles(cicdS3Client, [`s3://${DEPLOYMENT_REPORTS_BUCKET}/${testReport.folder}`]);
  const testReportFiles = extractReportFiles(files, testReport);
  Object.assign(testReport, formatTestReport(testReport.testResults));
  return { status: STATUS_SUCCESS, testReport, testReportFiles };
}

export function extractReportFiles(files, testReport) {
  return files.map((file) => {
    const { Key } = file;
    const splitKey = Key.split('/');
    return {
      key: Key,
      filename: splitKey[splitKey.length - 1],
    };
  }).filter((item) => {
    const allowedExtensions = ['.html', '.txt', '.json'];
    const disallowedPaths = ['assets/', 'jacoco-resources/', 'com.qv', 'handler', 'services', 'dal', 'common', 'util', 'enums'];
    const disallowedFileNames = ['jacoco-sessions.html', 'stats.txt'];
    const extension = item.filename.substring(item.filename.lastIndexOf('.'));
    const relativePath = item.key.substring(testReport.folder.length + 1);
    const isAllowedExtension = allowedExtensions.includes(extension);
    const isDisallowedPath = disallowedPaths.some((path) => relativePath.startsWith(path));
    const isDisallowedFileName = disallowedFileNames.includes(item.filename);
    return isAllowedExtension && !isDisallowedPath && !isDisallowedFileName;
  });
}
