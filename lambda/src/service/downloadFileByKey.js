import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { downloadFileToString } from 'common/util/s3util.js';
import { DEPLOYMENT_REPORTS_BUCKET } from 'common/util/const.js';
import { cicdS3Client } from '../util/awsConfig.js';

export async function downloadFileByKey(key) {
  const result = await downloadFileToString(cicdS3Client, DEPLOYMENT_REPORTS_BUCKET, key);
  return { status: STATUS_SUCCESS, result };
}
