import { STATUS_DELETED, STATUS_INVALID } from 'common/enums/crudStatus.js';
import { deleteBuildArtifact, deleteBuildArtifactLegacy } from '../dal/appDeployArtifacts.js';

export default async function getAppBuildMetadata(appName, builds, isLegacy) {
  const deleteArtifacts = isLegacy ? deleteBuildArtifactLegacy : deleteBuildArtifact;
  const buildRequests = builds.map((name) => deleteArtifacts(appName, name));
  const deletedBuilds = await Promise.all(buildRequests);
  return {
    status: deletedBuilds?.every((build) => build.status === STATUS_DELETED) ? STATUS_DELETED : STATUS_INVALID,
    builds: deletedBuilds,
  };
}
