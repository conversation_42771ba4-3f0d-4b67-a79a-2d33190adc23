import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { DEPLOYMENT_REPORTS_BUCKET, TEST_REPORTS } from 'common/util/const.js';
import { saveItem } from 'common/dal/common.js';

export async function createTestReport(payload) {
  const {
    appName, env, testType, branch, buildVersion, commitSha,
  } = payload;
  if (!appName) {
    return {
      status: STATUS_INVALID,
      message: 'appName is required',
    };
  }
  if (!env) {
    return {
      status: STATUS_INVALID,
      message: 'env is required',
    };
  }
  if (!testType) {
    return {
      status: STATUS_INVALID,
      message: 'testType is required',
    };
  }
  const reportId = `${Date.now()}`;
  const folder = `${appName}/${env}/${reportId}/${testType}`;

  const currentDate = new Date();
  currentDate.setFullYear(currentDate.getFullYear() + 1);
  const nextYearDate = currentDate;
  const nextYearTimestamp = Math.floor((nextYearDate.getTime()) / 1000);

  const item = {
    reportId,
    appName,
    env,
    testType,
    branch,
    buildVersion,
    commitSha,
    folder,
    createdAt: reportId,
    expiresAt: nextYearTimestamp,
  };
  await saveItem(TEST_REPORTS, item);
  return {
    status: STATUS_SUCCESS,
    data: {
      reportId,
      folder: `${DEPLOYMENT_REPORTS_BUCKET}/${folder}`,
    },
  };
}

export default createTestReport;
