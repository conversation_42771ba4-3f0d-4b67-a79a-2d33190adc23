service: api-dynamic-environments
plugins:
  - serverless-offline
  - serverless-domain-manager

custom:
  name: api-dynamic-environments
  infra: ${file(./infra.yml)}
  customDomain:
    domainName: ${self:custom.infra.domainName}
    stage: ${self:provider.stage}
    endpointType: 'regional'
    createRoute53Record: true
    certificateName: cicd.qvapi.co.nz
    apiType: http
    autoDomain: true
  serverless-offline: ${self:custom.infra.localOffline, false}

provider:
  name: aws
  runtime: nodejs20.x
  endpointType: REGIONAL
  apiGateway:
    apiKeySourceType: HEADER
    metrics: true
  deploymentBucket:
    name: 'qv-deployment'
  deploymentPrefix: 'api-dynamic-environments/serverless'
  apiName: api-dynamic-environments-${self:provider.stage}
  timeout: 240

  vpc:
    securityGroupIds: ${self:custom.infra.securityGroupIds}
    subnetIds: ${self:custom.infra.subnetIds}

  stage: ${opt:stage, 'local'}
  versionFunctions: false
  region: ${self:custom.infra.region}
  role: ${self:custom.infra.role}
  httpApi:
    cors: ${self:custom.infra.localCors, self:custom.infra.cors}
    shouldStartNameWithService: true
    disableDefaultEndpoint: true
    authorizers:
      auth0JwtAuthorizer:
        type: jwt
        identitySource: $request.header.Authorization
        issuerUrl: ${self:custom.infra.apiIssuer}
        audience:
          - 'https://api.cicd.qvapi.co.nz'
  environment:
    LOCAL_DEBUG: ${self:custom.infra.localDebug, false}
    ENV: ${self:provider.stage}
    PARAM_STORE_KEY: /${self:provider.stage}/api-dynamic-environments
    TZ: 'Pacific/Auckland'

  tracing:
    apiGateway: true
    lambda: true

  logs:
    restApi:
      level: INFO
      executionLogging: true

package:
  patterns:
    - '!test/**'
    - '!**/*.test*.js'
    - '!*.config.js'
    - '!**.yml'
    - '!**.md'
    - '!coverage/**'
    - '!**.sample.**'
  excludeDevDependencies: true

functions:
  getDataEnvironments:
    handler: src/handler/getDataEnvironments.handle
    environment:
      PERMISSIONS: 'list:data-env'
    events:
      - httpApi:
          path: /data-environments
          method: get
          authorizer:
            name: auth0JwtAuthorizer
  getDataEnvironmentByName:
    handler: src/handler/getDataEnvironmentByName.handle
    environment:
      PERMISSIONS: 'read:data-env'
    description: Returns a data environment by name
    events:
      - httpApi:
          path: /data-environment/{name}
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  getApplicationsByDataEnvironment:
    handler: src/handler/getApplicationsByDataEnvironment.handle
    environment:
      PERMISSIONS: 'read:data-env'
    description: Returns applications by a data environment name
    events:
      - httpApi:
          path: /data-environment/{name}/applications
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  ddeApplicationHealthCheck:
    handler: src/handler/getDdeApplicationHealthCheck.handle
    description: Health check of an application in a data environment
    events:
      - httpApi:
          path: /data-environment/{name}/heath-check/{applicationName}
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  updateDataEnvironment:
    handler: src/handler/updateDataEnvironment.handle
    environment:
      PERMISSIONS: 'update:data-env'
    events:
      - httpApi:
          path: /data-environment/{name}
          method: put
          authorizer:
            name: auth0JwtAuthorizer

  getApiStreamCount:
    handler: src/handler/getApiStreamCount.handle
    events:
      - httpApi:
          path: /data-environments/{name}/api-stream/count
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  deleteDataEnvironment:
    handler: src/handler/deleteDataEnvironment.handle
    environment:
      PERMISSIONS: 'delete:data-env'
    events:
      - httpApi:
          path: /data-environment/{name}
          method: delete
          authorizer:
            name: auth0JwtAuthorizer

  createDataEnvironment:
    handler: src/handler/createDataEnvironment.handle
    environment:
      PERMISSIONS: 'create:data-env'
    events:
      - httpApi:
          path: /data-environment/{name}
          method: post
          authorizer:
            name: auth0JwtAuthorizer
  deployDataEnvironmentApplication:
    handler: src/handler/deployDdeApplication.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /data-environment/{name}/deploy/{applicationName}
          method: post
          authorizer:
            name: auth0JwtAuthorizer
  restoreDatastore:
    handler: src/handler/restoreDatastore.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /data-environment/{name}/{datastore}/restore
          method: post
          authorizer:
            name: auth0JwtAuthorizer
  deployDatastore:
    handler: src/handler/deployDatastore.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /data-environment/{name}/{datastore}/deploy
          method: post
          authorizer:
            name: auth0JwtAuthorizer
  getDatabaseBuild:
    handler: src/handler/getDatabaseBuild.handle
    events:
      - httpApi:
          path: /data-environment/{name}/{datastore}/build
          method: get
          authorizer:
            name: auth0JwtAuthorizer
  getAppEnvironments:
    handler: src/handler/getAppEnvironments.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /app-environments
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getAppEnvironmentByName:
    handler: src/handler/getAppEnvironmentByName.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: Returns an app environment by name
    events:
      - httpApi:
          path: /app-environment/{name}
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  getApplicationsByAppEnvironment:
    handler: src/handler/getApplicationsByAppEnvironment.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: Returns applications by an app environmentname
    events:
      - httpApi:
          path: /app-environment/{name}/applications
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  daeApplicationHealthCheck:
      handler: src/handler/getDaeApplicationHealthCheck.handle
      description: Health check of an application in an app environment
      events:
        - httpApi:
            path: /app-environment/{name}/heath-check/{applicationName}
            method: GET
            authorizer:
              name: auth0JwtAuthorizer

  getApplicationContext:
    handler: src/handler/getApplicationContext.handle
    description: Health check of an application in an app environment
    events:
      - httpApi:
          path: /app-environment/{name}/context
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  updateAppEnvironment:
    handler: src/handler/updateAppEnvironment.handle
    environment:
      PERMISSIONS: 'update:app-env'
    events:
      - httpApi:
          path: /app-environment/{name}
          method: put
          authorizer:
            name: auth0JwtAuthorizer

  deleteAppEnvironment:
    handler: src/handler/deleteAppEnvironment.handle
    environment:
      PERMISSIONS: 'delete:app-env'
    events:
      - httpApi:
          path: /app-environment/{name}
          method: delete
          authorizer:
            name: auth0JwtAuthorizer

  createAppEnvironment:
    handler: src/handler/createAppEnvironment.handle
    environment:
      PERMISSIONS: 'create:app-env'
    events:
      - httpApi:
          path: /app-environment/{name}
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  deployDaeApplication:
    handler: src/handler/deployDaeApplication.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /app-environment/{name}/deploy/{applicationName}
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  deployAllApplication:
    handler: src/handler/deployAllDaeApplications.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /app-environment/{name}/deploy
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  getDefaultAppEnvConfig:
    handler: src/handler/getDefaultAppEnvConfig.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: Returns an app environment by name
    events:
      - httpApi:
          path: /app-environment/{name}/{dataEnv}/default-config
          method: POST
          authorizer:
            name: auth0JwtAuthorizer

  getAppEnvConfig:
    handler: src/handler/getAppEnvConfig.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: Retrieve an existing Application Environment configuration
    events:
      - httpApi:
          path: /app-environment/{name}/config
          method: POST
          authorizer:
            name: auth0JwtAuthorizer

  regenerateAppEnvConfig:
    handler: src/handler/regenerateAppEnvConfig.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: adds/replaces keys/values with defaults ,reset to default
    events:
      - httpApi:
          path: /app-environment/{name}/{dataEnv}/regenerate-config
          method: POST
          authorizer:
            name: auth0JwtAuthorizer

  getBuilds:
    handler: src/handler/getBuilds.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /builds
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  checkBranchExistence:
    handler: src/handler/checkBranchExistence.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /repositories/branches/existence
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  getApps:
    handler: src/handler/getApps.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /app
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getAppBuilds:
    handler: src/handler/getAppBuilds.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /app/{appName}/builds
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getAppBuildMetadata:
    handler: src/handler/getAppBuildMetadata.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /app/{appName}/builds/metadata
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  deleteAppBuild:
    handler: src/handler/deleteAppBuild.handle
    environment:
      PERMISSIONS: 'delete:app-env'
    events:
      - httpApi:
          path: /app/{appName}/builds
          method: delete
          authorizer:
            name: auth0JwtAuthorizer

  deployAppBuild:
    handler: src/handler/deployAppBuild.handle
    environment:
      PERMISSIONS: 'deploy:dev-aws-account deploy:prod-aws-account'
      PERMISSIONS_RELATION: 'OR'
    events:
      - httpApi:
          path: /app/{appName}/deploy
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  ## static configs in DB
  getPodsConfigurations:
    handler: src/handler/getPodsConfiguration.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /pods-configuration
          method: get
          authorizer:
            name: auth0JwtAuthorizer
  getAppStacks:
    handler: src/handler/getAppStacks.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /app-stacks
          method: get
          authorizer:
            name: auth0JwtAuthorizer
  getDatastoreStacks:
    handler: src/handler/getDatastoreStacks.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /datastore-stacks
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getDeployments:
    handler: src/handler/getDeployments.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /deployments
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getDeploymentBuilds:
    handler: src/handler/getDeploymentBuilds.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /deployment/{deploymentId}/builds
          method: get
          authorizer:
            name: auth0JwtAuthorizer


  getTestReports:
    handler: src/handler/getTestReports.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /test-reports
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getTestReportFiles:
    handler: src/handler/getTestReportFiles.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /test-report/{reportId}/files
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  createTestReport:
    handler: src/handler/createTestReport.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /test-reports
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  getReports:
    handler: src/handler/getReports.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /reports
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  startCypressTest:
    handler: src/handler/startCypressTest.handle
    environment:
      PERMISSIONS: 'create:background-test'
    events:
      - httpApi:
          path: /start-cypress-test
          method: get
          authorizer:
            name: auth0JwtAuthorizer

  getCypressTestBuilds:
    handler: src/handler/getCypressTestBuilds.handle
    environment:
      PERMISSIONS: 'list:background-tests'
    events:
      - httpApi:
          path: /app/{appName}/get-cypress-test-builds
          method: get
          authorizer:
            name: auth0JwtAuthorizer
  getIndex:
    handler: src/handler/getIndexByName.handle
    environment:
      PERMISSIONS: 'read:app-env'
    description: Returns index environment by name
    events:
      - httpApi:
          path: /app-environment/{name}/index
          method: GET
          authorizer:
            name: auth0JwtAuthorizer

  reindexIndex:
    handler: src/handler/reindex.handle
    environment:
      PERMISSIONS: 'create:app-env'
    events:
      - httpApi:
          path: /app-environment/{name}/reindex
          method: post
          authorizer:
            name: auth0JwtAuthorizer

  reindexIndexAll:
    handler: src/handler/reindexAll.handle
    environment:
      PERMISSIONS: 'create:app-env'
    events:
      - httpApi:
          path: /app-environment/{name}/reindex-all
          method: post
          authorizer:
            name: auth0JwtAuthorizer



    ## static file & download file token validation will be handled by wrapHttpHandler, auth0JwtAuthorizer will not be used
  ## auth0 token will be passed as a query parameter or cookie instead of header which used by auth0JwtAuthorizer
  staticFileHandler:
    handler: src/handler/staticFileHandler.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /{file+}
          method: get

  downloadFileHandler:
    handler: src/handler/downloadFileHandler.handle
    environment:
      PERMISSIONS: 'list:app-env'
    events:
      - httpApi:
          path: /download
          method: get

  ## Schedule tasks
  verifyAppEnvironment:
    handler: src/handler/verifyAppEnvironment.handle
    environment:
      DDE: ${self:custom.infra.appEnvVerify.dde}
      BRANCH: ${self:custom.infra.appEnvVerify.branch}
    events:
      - schedule:
          rate: ${self:custom.infra.appEnvVerify.rate}
          enabled: ${self:custom.infra.appEnvVerify.enabled}

  ## CloudWatch Events
  buildEventsSubscriber:
    handler: src/handler/buildEventsSubscriber.handle
    events:
      - sns:
          arn: arn:aws:sns:ap-southeast-2:948396734470:${self:custom.infra.topicName}
          topicName: ${self:custom.infra.topicName}

  reportsEventHandler:
    handler: src/handler/reportsEventsSubscriber.handle
    events:
      - s3:
          bucket: ${self:custom.infra.s3Bucket}
          event: s3:ObjectCreated:*
          existing: true
          rules:
            - suffix: mochawesome.json
      - s3:
          bucket: ${self:custom.infra.s3Bucket}
          event: s3:ObjectCreated:*
          existing: true
          rules:
            - suffix: coverage-summary.json
      - s3:
          bucket: ${self:custom.infra.s3Bucket}
          event: s3:ObjectCreated:*
          existing: true
          rules:
            - suffix: coverage/stats.txt

resources:
  Resources:
    DevLambdaInvokePermission:
      Type: AWS::Lambda::Permission
      Properties:
        FunctionName:
          Fn::GetAtt: [GetApplicationContextLambdaFunction, Arn]
        Action: lambda:InvokeFunction
        Principal: arn:aws:iam::373100629421:role/monarch-kubernetes-deployment-dev-lambdaRole
    ProdLambdaInvokePermission:
      Type: AWS::Lambda::Permission
      Properties:
        FunctionName:
          Fn::GetAtt: [ GetApplicationContextLambdaFunction, Arn]
        Action: lambda:InvokeFunction
        Principal: arn:aws:iam::466814882768:role/monarch-kubernetes-deployment-prod-lambdaRole