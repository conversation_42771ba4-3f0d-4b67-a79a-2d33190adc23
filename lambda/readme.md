## Serverless Offline

Note: I installed this on Ubuntu WSL. Had issues on Windows.

1. Use the [AWS MFA](https://pypi.org/project/aws-mfa/) script to generate a token for your default profile.
   This role will need to access parameter store to get the database credentials.

```shell
aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account
```

2. Create a local [infra.yml](infra.yml) file, by copying [infra.sample.yml](infra.sample.yml) and uncommenting the
   local variables and localDebug

3. Run the lambda locally

```shell
cd lambda
npm run start
```

4. Create a local [ui/.env](.env) file, by copying [ui/.env.sample](ui/.env.sample) and get VITE_AUTH0_PUBLIC_KEY from
   SSM.

5. To make the UI use your local instance, set the `VITE_API_BASE_URL` env var in [ui/.env](../ui/.env)

```shell
VITE_API_BASE_URL=http://localhost:3003/
```

6. To make the UI use your local test factory, set the `VITE_TESTFACTORY_URL` env var in [ui/.env](../ui/.env)

```shell
VITE_TESTFACTORY_URL=http://localhost:3082/jwt
```

7. Run the ui locally

``
cd ui
npm run start
``

## Using Postman to call the lambda locally
   1. run the UI and lambda locally
   2. copy the jwt token from the UI (login and copy the token from a request in the network tab)
   3. create a new request in postman for the endpoint you want to query (eg. GET http://localhost:3003/pods)
   4. set the query string parameter for Bearer as the jwt token you copied
   5. you are now ready to query the endpoint locally. Remember refresh the token if it expires