version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - COMMIT_MESSAGE
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - cd common
      - npm install
      - cd ../lambda
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --region ap-southeast-2 --query authorizationToken --output text`
      - npm install --include=dev

  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'

  build:
    on-failure: CONTINUE
    commands:
      - echo $BUILD_VERSION > version.txt
      - echo $COMMIT_SHA > sha.txt
      - echo $BUILD_BRANCH > branch.txt
      - VERSION=$(git describe --tags) || true
      - cp version.txt /artifact_output/
      - npm run lint > $REPORT_OUTPUT/linter.txt || true
      - git diff develop > $REPORT_OUTPUT/diff-develop.txt || true
      - git diff HEAD~ HEAD > $REPORT_OUTPUT/diff-last.txt || true
      - cp config.sample.json config.json
      - npm run test

      - echo "uploading unit test report started"
      - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
      - . ./create_test_report.sh "$PROJECT_NAME" "build" "unit" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
      - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
      - aws s3 cp mochawesome-report/ s3://$REPORT_FOLDER --recursive || true
      - echo "uploading unit test report completed"

      - mkdir -p /artifact_temp
      - cp -r --parents .npmrc version.txt sha.txt branch.txt serverless.yml package.json package-lock.json src test  /artifact_temp
      - echo "copying common"
      - cp -r  ../common  /artifact_temp
      - rm -rf /artifact_temp/common/node_modules

      - cd /artifact_temp
      - ls -al
      - zip -r /artifact_output/deploy.zip .
      - METADATA=$(printf '{"codepipeline-artifact-revision-summary":"%s","commit-sha":"%s"}' "$BUILD_VERSION $BUILD_BRANCH" "$COMMIT_SHA")
      - aws s3 cp /artifact_output/deploy.zip $BUILD_BASE_S3_URL/$CLEAN_BRANCH/deploy.zip --metadata="$METADATA"

  post_build:
    commands:
      - echo Build $BUILD_VERSION completed on `date`

artifacts:
  files:
    - '**/*'
  base-directory: /reports
