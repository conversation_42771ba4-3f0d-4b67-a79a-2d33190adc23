import { getCypressTestBuilds } from '../../src/handler/getCypressTestBuilds.js';
import { expect } from 'chai';

describe('getCypressTestBuilds.test.js', () => {
  let res;
  const validInputBody = {
    pathParameters: {
      appName: 'monarch-web-cypress',
    },
  };

  before('calls the handler function', async () => {
    res = await getCypressTestBuilds(validInputBody);
  });

  it.skip('should return the expected result (builds list)', () => {
    expect(res).to.have.property('body');
    expect(res.body).to.be.an('object');
    expect(res.body).to.have.property('builds');
    const builds = res.body.builds;
    expect(builds).to.be.an('array');
    const build = builds[0];
    expect(build).to.have.property('arn');
    expect(build).to.have.property('endTime');
    expect(build).to.have.property('buildStatus');
    expect(build.arn).to.be.a('string');
    expect(build.endTime).to.be.a('date');
    expect(build.buildStatus).to.be.a('string');
    expect(builds.length).to.be.at.least(1);
  });
});
