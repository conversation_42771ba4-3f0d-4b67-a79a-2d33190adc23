import getCodeBuildHistory from '../../src/dal/getCodeBuildHistory.js';
import { expect } from 'chai';

describe('getCodeBuildHistory.test.js', () => {
  let res;
  const appName = 'monarch-web-cypress';

  before('calls the handler function', async () => {
    res = await getCodeBuildHistory(appName);
  });

  it('should return the expected result (builds list)', async () => {
    const builds = res;
    expect(builds).to.be.an('array');
    const build = builds[0];
    expect(build).to.have.property('arn');
    expect(build).to.have.property('startTime');
    expect(build).to.have.property('buildStatus');
    expect(build.arn).to.be.a('string');
    expect(build.startTime).to.be.a('date');
    expect(build.buildStatus).to.be.a('string');
    expect(builds.length).to.be.at.least(1);
  });
});
