import Logger from "common/util/Logger.js";
import path from "path";
import {fileURLToPath} from "url";
import fs from "fs";
import {buildEventsSubscriber} from "../../src/handler/buildEventsSubscriber.js";

describe('Build event subscriber', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should process the event', async () => {
        // given
        const __dirname = path.dirname(fileURLToPath(import.meta.url));
        const eventPayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/public-website-dae.json'), 'utf8'));
        // when
        await buildEventsSubscriber(eventPayload);
    });
});
