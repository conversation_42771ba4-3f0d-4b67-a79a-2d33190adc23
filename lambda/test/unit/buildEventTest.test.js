import chai from 'chai';
import * as path from "path";
import {fileURLToPath} from 'url';

import translateLambdaEvent from "../../src/util/translateLambdaEvent.js";
import fs from "fs";
import getBuildDetailsFromPayloadService from '../../src/service/getBuildDetailsFromPayload.js';

const {expect} = chai;

describe('service', () => {
    it('should process the event correctly', async () => {
        const __dirname = path.dirname(fileURLToPath(import.meta.url));
        const samplePayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/sns.json'), 'utf8'));
        const translatedPayload = translateLambdaEvent(samplePayload);
        const results = await getBuildDetailsFromPayloadService(translatedPayload);
        expect(results).to.be.an('object');
        expect(results.gitRepo).to.equal('https://github.com/Quotable-Value/dynamic-environments.git');
        expect(results.gitSha).to.equal('49ca888b09f2751014edc5170e377f5e271e7a72');
        expect(results.gitBranch).to.equal('HEAD');
        expect(results.projectName).to.equal('api-dynamic-environments');
    });
});