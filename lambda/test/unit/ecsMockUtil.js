import {getTask} from "common/dal/getResourceName.js";

export function mockGetRevision(ecsMock, task) {
    ecsMock.onAnyCommand({
            taskDefinition: `${task}-task`
        }
    ).resolves({
            taskDefinition: {
                taskDefinitionArn: `arn:aws:ecs:ap-southeast-2:948396734470:task-definition/${task}-task:1`,
                taskRoleArn: 'arn:aws:iam::948396734470:role/dynamic_environments_ecs_task_role',
                revision: 1,
            }
        }
    );
}

export async function mockRunTask(ecsMock, task, options) {
    const taskName = await getTask(task);
    let input = {
        cluster: "dynamic-environments",
        taskDefinition: `arn:aws:ecs:ap-southeast-2:948396734470:task-definition/${taskName}-task:1`,
        overrides: {
            containerOverrides: [
                {
                    name: `${taskName}-task`,
                    command: buildCommand(task, options),
                }
            ]
        }

    };
    ecsMock.onAnyCommand(input).resolves({
          "tasks": [
              {
                  "taskArn": "arn:aws:ecs:ap-southeast-2:948396734470:task/dynamic-environments/fakeTaskId",
              }
          ]
      }
    );
}

function buildCommand(task, options) {
    const envOptions = Object.entries(options)
        .filter(([, value]) => value)
        .map(([key, value]) => (key === 'payload' ? `--${key} ${JSON.stringify(value)}` : `--${key} ${value}`))
        .join(' ');

    logger.info(`envOption: ${envOptions}`);
    return ['sh', '-c', `cd /app/cmd && node src/index.js --name ${task} ${envOptions}`];
}