import {expect} from 'chai';
import {STATUS_INVALID} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {deployDatastoreHandler} from "../../src/handler/deployDatastore.js";

describe('Deploy Datastore Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid name is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: '',
                datastore: 'qv_apihub'
            },
        };
        // when
        const result = await deployDatastoreHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return an error when a valid datastore is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'testdde',
                datastore: ''
            },
        };
        // when
        const result = await deployDatastoreHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

});
