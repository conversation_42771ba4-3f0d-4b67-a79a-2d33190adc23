import {expect} from 'chai';
import {STATUS_INVALID} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {updateAppEnvironmentHandler} from "../../src/handler/updateAppEnvironment.js";
import {mockDaeDeploymentInProgress} from "./dbMockUtil.js";

describe('Deploy Application Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when another deployment is in progress', async () => {
        // given
        mockDaeDeploymentInProgress();
        const mockEvent = {
            pathParameters: {
                name: 'AppEnv',
                applicationName: 'MyApplication',
            },
            body: JSON.stringify({dataEnv: 'dataEnv'})
        };
        // when
        const result = await updateAppEnvironmentHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
});

