import {expect} from 'chai';
import {STATUS_INVALID, STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {deployAllDaeApplicationsHandler} from "../../src/handler/deployAllDaeApplications.js";
import {
    mockDaeDeploymentInProgress,
    mockGetAppEnvironmentByName,
    mockGetApplicationByKey, mockGetApplicationsByEnv,
    mockNoDaeDeployment
} from "./dbMockUtil.js";
import sinon from "sinon";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {ApplicationType} from "common/enums/applicationType.js";
import {mockGetRevision, mockRunTask} from "./ecsMockUtil.js";
import {getTableName, getTask} from "common/dal/getResourceName.js";
import {CmdAppTasks} from "common/enums/cmdAppTasks.js";
import {ecsClient} from "common/service/ecsTools.js";
import {ssmClient} from "common/dal/ssm.js";
import {DEPLOYMENTS} from "common/util/const.js";
import {DeploymentStatus} from "common/enums/deploymentStatus.js";
import {mockSsmParameter} from "./ssmMockUtil.js";

describe('Deploy All DAE Applications Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid name is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: null, // Triggering the error condition
                applicationName: 'validApplicationName',
            },
        };
        // when
        const result = await deployAllDaeApplicationsHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return an error when another deployment is in progress', async () => {
        // given
        mockDaeDeploymentInProgress();
        const mockEvent = {
            pathParameters: {
                name: 'AppEnv',
            },
        };
        // when
        const result = await deployAllDaeApplicationsHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should init a new deployment', async () => {
        // setup
        const FIXED_TIMESTAMP = 1487076708000;
        const clock = sinon.useFakeTimers({now: FIXED_TIMESTAMP});

        // given
        const appEnv = 'AppEnv';
        const applications = ['MyApplication', 'MyApplication2'];
        const ddbMock = mockClient(dynamoDbClient);
        const ecsMock = mockClient(ecsClient);
        const ssmMock = mockClient(ssmClient);
        mockSsmParameter(ssmMock);
        mockNoDaeDeployment(ddbMock);
        mockGetApplicationsByEnv(ddbMock, appEnv);
        applications.forEach(appName => {
            mockGetApplicationByKey(ddbMock, `${ApplicationType.DAE}-${appEnv}-${appName}`);
        });
        mockGetAppEnvironmentByName(ddbMock, appEnv);
        mockGetRevision(ecsMock, await getTask(CmdAppTasks.APPLICATION_DEPLOY));
        await mockRunTask(ecsMock, CmdAppTasks.APPLICATION_DEPLOY, {
            deploymentId: FIXED_TIMESTAMP,
            payload: JSON.stringify({
                type: ApplicationType.DAE,
                envName: appEnv,
                applicationName: ''
            }),
        });
        const mockEvent = {
            pathParameters: {
                name: appEnv,
            },
        };
        // when
        const result = await deployAllDaeApplicationsHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);

        // cleanup
        clock.restore();
    });

});

