import {expect} from 'chai';
import {STATUS_INVALID} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {createAppEnvironmentHandler} from "../../src/handler/createAppEnvironment.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {ApplicationStack} from "common/enums/applicationStack.js";
import {RESERVED_APP_ENV_NAMES} from "common/util/const.js";

describe('Create App Environment Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when public website already created in the same DDE', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'testdae',
            },
            body: JSON.stringify({
                defaultBranch: 'master',
                contextConfig: '{}',
                dataEnv: 'test',
                stacks: [ApplicationStack.PUBLIC_WEBSITE],
            }),
        };
        mockDb();
        // when
        const result = await createAppEnvironmentHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return an error for invalid app environment name', async () => {
        const mockEvent = {
            pathParameters: {
                name: 'invalid_name!', // Invalid name with special character
            },
            body: JSON.stringify({
                defaultBranch: 'master',
                contextConfig: '{}',
                dataEnv: 'test',
                stacks: [ApplicationStack.PUBLIC_WEBSITE],
            }),
        };
        mockDb();
        const result = await createAppEnvironmentHandler(mockEvent);
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should return an error when cost centre is missing', async () => {
        const mockEvent = {
            pathParameters: {
                name: 'validname',
            },
            body: JSON.stringify({
                defaultBranch: 'master',
                contextConfig: '{}',
                dataEnv: 'test',
                stacks: [ApplicationStack.PUBLIC_WEBSITE],
                // Omitting costCentre
            }),
        };
        mockDb();
        const result = await createAppEnvironmentHandler(mockEvent);
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should return an error for reserved app environment name', async () => {
        const mockEvent = {
            pathParameters: {
                name: RESERVED_APP_ENV_NAMES[0],
            },
            body: JSON.stringify({
                defaultBranch: 'master',
                contextConfig: '{}',
                dataEnv: 'test',
                stacks: [ApplicationStack.PUBLIC_WEBSITE],
                costCentre: '1234',
            }),
        };
        mockDb();
        const result = await createAppEnvironmentHandler(mockEvent);
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

});

function mockDb() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand(
        {
            "TableName": "local_data_environments",
            "KeyConditionExpression": "#name = :value",
            "ExpressionAttributeNames": {"#name": "Name"},
            "ExpressionAttributeValues": {":value": {"S": "test"}}
        }
    ).resolves({
        "Items": [
            {
                "Name": {"S": "test"},
                "Status": {"S": "stable"}
            }
        ]
    });

    ddbMock.onAnyCommand(
        {
            TableName: 'local_app_environments',
            FilterExpression: '#status = :status1 OR #status = :status2 OR #status = :status3',
            ExpressionAttributeNames: {'#status': 'Status'},
            ExpressionAttributeValues: {
                ':status1': {S: 'stable'},
                ':status2': {S: 'provisioning'},
                ':status3': {S: 'deleted'}
            }
        }
    ).resolves({
        "Items": [
            {
                "Name": {"S": "test2"},
                "Status": {"S": "deleted"},
                "DataEnv": {"S": "test"},
                "Stacks": {"L": [{"S": "Public Website"}]}
            },
            {
                "Name": {"S": "dae_with_public_website"},
                "Status": {"S": "stable"},
                "DataEnv": {"S": "test"},
                "Stacks": {"L": [{"S": "Public Website"}]}
            }
        ]
    });

}
