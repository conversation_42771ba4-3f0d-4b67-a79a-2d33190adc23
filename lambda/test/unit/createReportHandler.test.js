import {expect} from 'chai';
import {STATUS_INVALID, STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {DEPLOYMENT_REPORTS_BUCKET} from "common/util/const.js";
import {createTestReportHandler} from "../../src/handler/createTestReport.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";

describe('Create Report Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid appName is not provided', async () => {
        // given
        const mockEvent = {
            body: JSON.stringify({
                appName: null,
                env: 'cicd',
                testType: 'unit'
            }),
        };
        // when
        const result = await createTestReportHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
    });
    it('should return an error when a valid env is not provided', async () => {
        // given
        const mockEvent = {
            body: JSON.stringify({
                appName: 'testApp',
                env: null,
                testType: 'unit'
            }),
        };
        // when
        const result = await createTestReportHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
    });
    it('should return an error when a valid testType is not provided', async () => {
        // given
        const mockEvent = {
            body: JSON.stringify({
                appName: 'testApp',
                env: 'cicd',
                testType: null
            }),
        };
        // when
        const result = await createTestReportHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
    });


    it('should create a record', async () => {
        // given
        mockDb()

        const mockEvent = {
            body: JSON.stringify({
                appName: 'dae123',
                env: 'api-stats',
                testType: 'integration'
            }),
        };
        // when
        const result = await createTestReportHandler(mockEvent);
        console.log(result);
        // then
        expect(result.body.data.reportId).exist;
        const folder = `dae123/api-stats/${result.body.data.reportId}/integration`;
        expect(result.body.data.folder).to.equal(DEPLOYMENT_REPORTS_BUCKET + '/' + folder);
        expect(result.body.status).to.equal(STATUS_SUCCESS);
    });

});

export function mockDb() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand(
        {
            "TableName": "local_test_reports",
            "Item": {
                "AppName": {"S": "dae123"},
                "Env": {"S": "api-stats"},
                "TestType": {"S": "integration"},
                "Folder": {"S": "dae123/api-stats/1707876704686/integration"}}
        }
    ).resolves({});
}


