// pagination.test.js
import chai from 'chai';
import {paginateArray} from "common/dal/common.js";

const {expect} = chai;

describe('paginateArray function', () => {
    it('should paginate the array correctly', () => {
        const inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        const offset = 0;
        const limit = 3;

        const result = paginateArray(inputArray, limit, offset);
        expect(result).to.have.property('count');
        expect(result).to.have.property('currentPageData');
        expect(result).to.have.property('pageNumber');
        expect(result).to.have.property('totalPages');

        expect(result.count).to.equal(10);
        expect(result.currentPageData).to.deep.equal([1, 2, 3]);
        expect(result.pageNumber).to.equal(1);
        expect(result.totalPages).to.equal(4);
    });
});
