import {expect} from 'chai';
import {STATUS_INVALID, STATUS_MISSING, STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {getDatabaseBuildHandler} from "../../src/handler/getDatabaseBuild.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {DATA_ENVIRONMENTS} from "common/util/const.js";
import {cicdS3Client} from "../../src/util/awsConfig.js";
import {sdkStreamMixin} from "@smithy/util-stream";
import {createReadStream} from 'fs';
import {fileURLToPath} from "url";
import * as path from "path";

describe('Get Database Build Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid name is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: '',
                datastore: 'qv_apihub'
            },
        };
        // when
        const result = await getDatabaseBuildHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return an error when a valid datastore is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'testdde',
                datastore: ''
            },
        };
        // when
        const result = await getDatabaseBuildHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return error', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'testdde',
                datastore: 'qv_apihub'
            },
        };
        setUpDb()
        setUpS3FileNoExist()
        // when
        const result = await getDatabaseBuildHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_MISSING);
        expect(result.body).to.have.property('message');

    });
    it('should return diff', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'testdde',
                datastore: 'qv_apihub'
            },
        };
        setUpDb()
        setUpS3()
        // when
        const result = await getDatabaseBuildHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.diff).to.have.length(1);
        expect(result.body.diff[0]).to.equal('cicd/qv_apihub/tables/query.sql');
    });
});

function setUpDb() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(DATA_ENVIRONMENTS),
        ExpressionAttributeNames: {
            "#name": "Name"
        },
        ExpressionAttributeValues: {
            ":value": {
                "S": "testdde"
            }
        }
    }).resolves({
        Items: [
            {
                Name: {S: 'testdde'},
                Branch: {S: 'master'},
                StartTime: {S: ''},
                EndTime: {S: ''},
                Drive: {S: ''},
                CostCentre: {S: ''},
                Status: {S: ''},
            }
        ]
    });
}
function setUpS3() {
    const s3Mock = mockClient(cicdS3Client);

    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const filePath = path.resolve(__dirname, './files/qv_apihub.zip')

    const stream = createReadStream(filePath);
    const sdkStream = sdkStreamMixin(stream);
    s3Mock.onAnyCommand({
        Bucket: "qv-deployment",
        Key: "db-scripts/build/master/qv_apihub.zip"
    }).resolves({Body: sdkStream});
}
function setUpS3FileNoExist() {
    const s3Mock = mockClient(cicdS3Client);
    s3Mock.onAnyCommand({
        Bucket: "qv-deployment",
        Key: "db-scripts/build/master/qv_apihub.zip"
    }).rejects(new Error('The specified key does not exist.'));
}



