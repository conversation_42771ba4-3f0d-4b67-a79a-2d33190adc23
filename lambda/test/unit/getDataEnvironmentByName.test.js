import {expect} from 'chai';
import {STATUS_INVALID, STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {getDataEnvironmentByNameHandler} from "../../src/handler/getDataEnvironmentByName.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {APPLICATIONS, DATA_ENVIRONMENTS, DATASTORES} from "common/util/const.js";
import {ApplicationType} from "common/enums/applicationType.js";

describe('getDataEnvironmentByName Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid name is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: null,
            },
        };
        // when
        const result = await getDataEnvironmentByNameHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should return a data environment', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'mydde',
            },
        };
        setupDb();
        // when
        const result = await getDataEnvironmentByNameHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.environment.name).to.equal('mydde');
        expect(result.body.environment.dataStores).to.have.length(1)
        expect(result.body.environment.applications).to.have.length(1)
    });
});


function setupDb() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(DATA_ENVIRONMENTS),
        ExpressionAttributeNames:{
            "#name":"Name"
        },
        ExpressionAttributeValues:{
            ":value":{
                "S":"mydde"
            }
        }
    }).resolves({
        Items: [
            {
                Name: {S: 'mydde'},
                Branch: {S: ''},
                StartTime: {S: ''},
                EndTime: {S: ''},
                Drive: {S: ''},
                CostCentre: {S: ''},
                Status: {S: ''},
            }
        ]
    }).onAnyCommand(
        {
             TableName :getTableName(DATASTORES),
             FilterExpression :"#env = :env1",
             ExpressionAttributeNames :{
                "#env":"Env"
            },
             ExpressionAttributeValues:{
                ":env1":{
                    "S":"mydde"
                }
            }
        }
    ).resolves({
        Items: [
            {
                Name: {S: 'datastore1'},
                Env: {S: 'mydde'},
            }
        ]
    }).onAnyCommand(
        {
             TableName :getTableName(APPLICATIONS),
             FilterExpression:"#env = :env1",
             ExpressionAttributeNames:{
                "#env":"Env"
            },
            ExpressionAttributeValues:{
                ":env1":{
                    "S":"mydde"
                }
            }
        }
    ).resolves({
        Items: [
            {
                Name: {S: 'app1'},
                Env: {S: 'mydde'},
                Type: {S: ApplicationType.DDE},
            }
        ]
    });

}
