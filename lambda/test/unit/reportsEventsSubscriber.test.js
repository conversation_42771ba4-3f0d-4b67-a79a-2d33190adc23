import Logger from "common/util/Logger.js";
import path from "path";
import {fileURLToPath} from "url";
import fs, {createReadStream} from "fs";
import {reportsEventsSubscriber} from "../../src/handler/reportsEventsSubscriber.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {TEST_REPORTS} from "common/util/const.js";
import {cicdS3Client} from "../../src/util/awsConfig.js";
import {sdkStreamMixin} from "@smithy/util-stream";

describe('S3 event subscriber for mochawesome', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should handle an invalid report id', async () => {
        // given
        const ddbMock = mockClient(dynamoDbClient);
        ddbMock.onAnyCommand(
            {
                TableName: getTableName(TEST_REPORTS),
                FilterExpression: 'contains(#reportId, :reportId)',
                ExpressionAttributeNames: {'#reportId': 'ReportId'},
                ExpressionAttributeValues: {':reportId': {S: '1707565388606'}}
            }
        ).resolvesOnce({
            Items: []
        });

        const __dirname = path.dirname(fileURLToPath(import.meta.url));
        const eventPayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/s3_report.json'), 'utf8'));
        // when
        const result = await reportsEventsSubscriber(eventPayload);
        logger.info('result:', result);
        // then
        // no assertion, just checking if it runs without error
    });

    it('should parse the mochawesome report', async () => {
        // given
        mockDB()
        setUpS3()
        const __dirname = path.dirname(fileURLToPath(import.meta.url));
        const eventPayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/s3_report.json'), 'utf8'));
        // when
        const result = await reportsEventsSubscriber(eventPayload);
        logger.info('result:', result);
        // then
        // no assertion, just checking if it runs without error
    });

});

export function mockDB() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand(
        {
            TableName: getTableName(TEST_REPORTS),
            FilterExpression: 'contains(#reportId, :reportId)',
            ExpressionAttributeNames: {'#reportId': 'ReportId'},
            ExpressionAttributeValues: {':reportId': {S: '1707565388606'}}
        }
    ).resolvesOnce({
        Items: [
            {
                ReportId: {S: '1707804982084'},
                Folder: {S: 'api-dynamic-environments/build/1707804982084'},
            }
        ]
    });
    ddbMock.onAnyCommand(
        {
            TableName: getTableName(TEST_REPORTS),
            Key: {ReportId: {S: '1707565388606'}},
            UpdateExpression: 'set TestResults = :TestResults',
        }
    ).resolvesOnce({});
}
function setUpS3() {
    const s3Mock = mockClient(cicdS3Client);

    // list objects in the folder of the report
    s3Mock.onAnyCommand({
        Bucket: 'qv-deployment-reports',
        Prefix: 'api-dynamic-environments/build/1707804982084'
    }).resolvesOnce({
        Contents: [
            {Key: 'api-dynamic-environments/build/1707804982084/unit/assets/roboto-regular-webfont.woff2'},
            {Key: 'api-dynamic-environments/build/1707804982084/unit/mochawesome.html'},
            {Key: 'api-dynamic-environments/build/1707804982084/unit/mochawesome.json'}
        ]
    })
    // mock get the mochawesome.json, only the mochawesome.json is needed for parsing
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const filePath = path.resolve(__dirname, './files/mochawesome.json')
    const stream = createReadStream(filePath);
    const sdkStream = sdkStreamMixin(stream);
    s3Mock.onAnyCommand(
        {
            Bucket: 'qv-deployment-reports',
            Key: 'api-dynamic-environments/build/1707804982084/unit/mochawesome.json'
        }
    ).resolvesOnce({Body: sdkStream});
}