import Logger from "common/util/Logger.js";
import {getApplicationContextHandler} from "../../src/handler/getApplicationContext.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {APP_ENVIRONMENTS, APP_STACKS, APPLICATIONS} from "common/util/const.js";
import {STABLE} from "common/enums/environmentStatus.js";
import {ApplicationType} from "common/enums/applicationType.js";
import {Platform} from "common/enums/platform.js";
import {expect} from "chai";
import {ApplicationStack} from "common/enums/applicationStack.js";

describe('getApplicationContextHandler Tests', () => {
  global.logger = new Logger();
  global.localDebug = true;

  it('should return empty context when DAE environment does not exist', async () => {
    const nonExistentEnv = 'no_exist_env';
    const mockEvent = {
      pathParameters: {name: nonExistentEnv},
    };
    const ddbMock = mockClient(dynamoDbClient);
    mockDaeEnvironmentDoesNotExist(ddbMock);

    const result = await getApplicationContextHandler(mockEvent);
    expect(result.body).to.deep.equal({});
  });

  it('should return context when DAE environment exists', async () => {
    const existingEnv = 'test';
    const mockEvent = {
      pathParameters: {name: existingEnv},
    };
    const ddbMock = mockClient(dynamoDbClient);
    mockDaeEnvironmentExists(ddbMock);

    const result = await getApplicationContextHandler(mockEvent);
    expect(result.body).to.deep.equal({
      KEY1: 'VALUE1',
      KEY2: 'VALUE2',
      KEY3: 'VALUE3',
      KEY4: 'VALUE4',
      COST_CENTRE: 'costCentreTag',
      APP1_PODS: 2,
      SHOULD_USE_TEST_FOLLOWERS: true
    });
  });

});

function mockDaeEnvironmentDoesNotExist(ddbMock) {
  ddbMock.onAnyCommand({
    TableName: APP_ENVIRONMENTS,
    ExpressionAttributeValues: {':value': {S: 'no_exist_env'}}
  }).resolves({Items: []});
}

function mockDaeEnvironmentExists(ddbMock) {
  const contextConfig = {
    "KEY1": "VALUE1",
    "KEY2": "VALUE2",
  };
  const tfConfig = {
    "KEY3": "VALUE3",
    "KEY4": "VALUE4",
  };

  ddbMock.onAnyCommand({
    TableName: APP_ENVIRONMENTS,
    ExpressionAttributeValues: {':value': {S: 'test'}}
  }).resolves({
    Items: [
      {
        Status: {S: STABLE},
        Name: {S: 'test'},
        ContextConfig: {S: JSON.stringify(contextConfig)},
        TfDeterminedConfig: {S: JSON.stringify(tfConfig)},
        CostCentre: {S: 'costCentreTag'},
        Stacks: {L: [{S: ApplicationStack.PUBLIC_WEBSITE}]}
      }
    ]
  }).onAnyCommand({
    TableName: getTableName(APPLICATIONS),
    IndexName: 'EnvIndex',
    KeyConditionExpression: '#env = :value',
    ExpressionAttributeNames: {'#env': 'Env'},
    ExpressionAttributeValues: {':value': {S: 'test'}}
  }).resolves({
    Items: [
      {
        Type: {"S": ApplicationType.DAE},
        Name: {"S": "app1"},
        Pods: {"S": "2"}
      },
      {
        Type: {"S": ApplicationType.DAE},
        Name: {"S": "app2"}
      }
    ]
  }).onAnyCommand({
    TableName: getTableName(APP_STACKS),
  }).resolves({
    Items: [
      {
        Name: {S: 'app1'},
        Pipeline: {S: 'app1'},
        Platform: {S: Platform.K8S},
        DeployDefault: {BOOL: true},
        Selectable: {BOOL: true},
        Stacks: {L: [{S: 'Monarch'}]},
        Enabled: {BOOL: true},
      },
      {
        Name: {S: 'app2'},
        Pipeline: {S: 'app2'},
        Platform: {S: Platform.LAMBDA},
        DeployDefault: {BOOL: true},
        Selectable: {BOOL: true},
        Stacks: {L: [{S: 'Monarch'}]},
        Enabled: {BOOL: true},
      }
    ]
  });
}
