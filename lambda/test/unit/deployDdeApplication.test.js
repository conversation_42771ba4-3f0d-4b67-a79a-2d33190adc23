import {expect} from 'chai';
import {STATUS_INVALID, STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {
    mockDdeDeploymentInProgress,
    mockGetApplicationByKey, mockGetDataEnvironmentByName, mockNoDdeDeployment
} from "./dbMockUtil.js";
import {deployDdeApplicationHandler} from "../../src/handler/deployDdeApplication.js";
import sinon from "sinon";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {ecsClient} from "common/service/ecsTools.js";
import {ApplicationType} from "common/enums/applicationType.js";
import {mockGetRevision, mockRunTask} from "./ecsMockUtil.js";
import {getTask} from "common/dal/getResourceName.js";
import {CmdAppTasks} from "common/enums/cmdAppTasks.js";
import {ssmClient} from "common/dal/ssm.js";
import {mockSsmParameter} from "./ssmMockUtil.js";

describe('Deploy Application Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when a valid name is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: null, // Triggering the error condition
                applicationName: 'validApplicationName',
            },
        };
        // when
        const result = await deployDdeApplicationHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
    it('should return an error when a valid application is not provided', async () => {
        // given
        const mockEvent = {
            pathParameters: {
                name: 'DataEnv',
                applicationName: null, // Triggering the error condition
            },
        };
        // when
        const result = await deployDdeApplicationHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should return an error when another deployment is in progress', async () => {
        // given
        mockDdeDeploymentInProgress();
        const mockEvent = {
            pathParameters: {
                name: 'DataEnv',
                applicationName: 'MyApplication',
            },
        };
        // when
        const result = await deployDdeApplicationHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });

    it('should init a new deployment', async () => {
        // setup
        const FIXED_TIMESTAMP = 1487076708000;
        const clock  = sinon.useFakeTimers({ now: FIXED_TIMESTAMP });

        // given
        const dataEnv = 'DataEnv';
        const applicationName = 'MyApplication';
        const ddbMock = mockClient(dynamoDbClient);
        const ecsMock = mockClient(ecsClient);
        const ssmMock = mockClient(ssmClient);

        mockSsmParameter(ssmMock);
        mockNoDdeDeployment(ddbMock);
        mockGetApplicationByKey(ddbMock,`${ApplicationType.DDE}-${dataEnv}-${applicationName}`);
        mockGetDataEnvironmentByName(ddbMock,dataEnv);
        mockGetRevision(ecsMock,await getTask(CmdAppTasks.APPLICATION_DEPLOY));
        await mockRunTask(ecsMock, CmdAppTasks.APPLICATION_DEPLOY, {
            deploymentId: FIXED_TIMESTAMP,
            payload: JSON.stringify({
                type: ApplicationType.DDE,
                envName: dataEnv,
                applicationName: applicationName,
            }),
        });
        const mockEvent = {
            pathParameters: {
                name: dataEnv,
                applicationName: applicationName,
            },
        };
        // when
        const result = await deployDdeApplicationHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);

        // cleanup
        clock.restore();
    });

});

