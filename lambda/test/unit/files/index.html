<?xml version="1.0" encoding="utf-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <link rel="stylesheet" href="jacoco-resources/report.css" type="text/css"/>
    <link rel="shortcut icon" href="jacoco-resources/report.gif" type="image/gif"/>
    <title>Jacoco Coverage Report</title>
    <script type="text/javascript" src="jacoco-resources/sort.js"></script>
</head>
<body onload="initialSort(['breadcrumb', 'coveragetable'])">
<div class="breadcrumb" id="breadcrumb"><span class="info"><a href="jacoco-sessions.html"
                                                              class="el_session">Sessions</a></span><span
        class="el_report">Jacoco Coverage Report</span></div>
<h1>Jacoco Coverage Report</h1>
<table class="coverage" cellspacing="0" id="coveragetable">
    <thead>
    <tr>
        <td class="sortable" id="a" onclick="toggleSort(this)">Element</td>
        <td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td>
        <td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td>
        <td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td>
        <td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td>
        <td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td>
        <td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td>
        <td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td>
        <td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td>
        <td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td>
        <td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td>
        <td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td>
        <td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td>
    </tr>
    </thead>
    <tfoot>
    <tr>
        <td>Total</td>
        <td class="bar">3,104 of 3,394</td>
        <td class="ctr2">8%</td>
        <td class="bar">60 of 74</td>
        <td class="ctr2">18%</td>
        <td class="ctr1">131</td>
        <td class="ctr2">157</td>
        <td class="ctr1">571</td>
        <td class="ctr2">629</td>
        <td class="ctr1">97</td>
        <td class="ctr2">120</td>
        <td class="ctr1">18</td>
        <td class="ctr2">28</td>
    </tr>
    </tfoot>
    <tbody>
    <tr>
        <td id="a4"><a href="com.qv.sale.search/index.html" class="el_package">com.qv.sale.search</a></td>
        <td class="bar" id="b0"><img src="jacoco-resources/redbar.gif" width="120" height="10" title="1,727"
                                     alt="1,727"/></td>
        <td class="ctr2" id="c4">0%</td>
        <td class="bar" id="d0"><img src="jacoco-resources/redbar.gif" width="120" height="10" title="38" alt="38"/>
        </td>
        <td class="ctr2" id="e2">0%</td>
        <td class="ctr1" id="f0">73</td>
        <td class="ctr2" id="g0">73</td>
        <td class="ctr1" id="h0">303</td>
        <td class="ctr2" id="i0">303</td>
        <td class="ctr1" id="j0">54</td>
        <td class="ctr2" id="k0">54</td>
        <td class="ctr1" id="l0">10</td>
        <td class="ctr2" id="m1">10</td>
    </tr>
    <tr>
        <td id="a1"><a href="com.qv.sale.impl/index.html" class="el_package">com.qv.sale.impl</a></td>
        <td class="bar" id="b1"><img src="jacoco-resources/redbar.gif" width="85" height="10" title="1,237"
                                     alt="1,237"/><img src="jacoco-resources/greenbar.gif" width="8" height="10"
                                                       title="118" alt="118"/></td>
        <td class="ctr2" id="c3">8%</td>
        <td class="bar" id="d1"><img src="jacoco-resources/redbar.gif" width="34" height="10" title="11" alt="11"/><img
                src="jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td>
        <td class="ctr2" id="e1">8%</td>
        <td class="ctr1" id="f1">45</td>
        <td class="ctr2" id="g1">57</td>
        <td class="ctr1" id="h1">245</td>
        <td class="ctr2" id="i1">264</td>
        <td class="ctr1" id="j1">39</td>
        <td class="ctr2" id="k1">51</td>
        <td class="ctr1" id="l1">7</td>
        <td class="ctr2" id="m0">13</td>
    </tr>
    <tr>
        <td id="a0"><a href="com.qv.sale/index.html" class="el_package">com.qv.sale</a></td>
        <td class="bar" id="b2"><img src="jacoco-resources/redbar.gif" width="3" height="10" title="52" alt="52"/></td>
        <td class="ctr2" id="c5">0%</td>
        <td class="bar" id="d4"/>
        <td class="ctr2" id="e4">n/a</td>
        <td class="ctr1" id="f4">1</td>
        <td class="ctr2" id="g5">1</td>
        <td class="ctr1" id="h2">8</td>
        <td class="ctr2" id="i5">8</td>
        <td class="ctr1" id="j3">1</td>
        <td class="ctr2" id="k5">1</td>
        <td class="ctr1" id="l2">1</td>
        <td class="ctr2" id="m3">1</td>
    </tr>
    <tr>
        <td id="a5"><a href="com.qv.sale.utility/index.html" class="el_package">com.qv.sale.utility</a></td>
        <td class="bar" id="b3"><img src="jacoco-resources/redbar.gif" width="3" height="10" title="51" alt="51"/><img
                src="jacoco-resources/greenbar.gif" width="9" height="10" title="142" alt="142"/></td>
        <td class="ctr2" id="c1">73%</td>
        <td class="bar" id="d2"><img src="jacoco-resources/redbar.gif" width="22" height="10" title="7" alt="7"/><img
                src="jacoco-resources/greenbar.gif" width="41" height="10" title="13" alt="13"/></td>
        <td class="ctr2" id="e0">65%</td>
        <td class="ctr1" id="f2">9</td>
        <td class="ctr2" id="g2">17</td>
        <td class="ctr1" id="h3">8</td>
        <td class="ctr2" id="i2">36</td>
        <td class="ctr1" id="j2">2</td>
        <td class="ctr2" id="k2">7</td>
        <td class="ctr1" id="l3">0</td>
        <td class="ctr2" id="m2">2</td>
    </tr>
    <tr>
        <td id="a3"><a href="com.qv.sale.migration/index.html" class="el_package">com.qv.sale.migration</a></td>
        <td class="bar" id="b4"><img src="jacoco-resources/redbar.gif" width="2" height="10" title="37" alt="37"/></td>
        <td class="ctr2" id="c2">11%</td>
        <td class="bar" id="d3"><img src="jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td>
        <td class="ctr2" id="e3">0%</td>
        <td class="ctr1" id="f3">3</td>
        <td class="ctr2" id="g3">5</td>
        <td class="ctr1" id="h4">7</td>
        <td class="ctr2" id="i3">9</td>
        <td class="ctr1" id="j4">1</td>
        <td class="ctr2" id="k4">3</td>
        <td class="ctr1" id="l4">0</td>
        <td class="ctr2" id="m4">1</td>
    </tr>
    <tr>
        <td id="a2"><a href="com.qv.sale.impl.domain/index.html" class="el_package">com.qv.sale.impl.domain</a></td>
        <td class="bar" id="b5"><img src="jacoco-resources/greenbar.gif" width="1" height="10" title="25" alt="25"/>
        </td>
        <td class="ctr2" id="c0">100%</td>
        <td class="bar" id="d5"/>
        <td class="ctr2" id="e5">n/a</td>
        <td class="ctr1" id="f5">0</td>
        <td class="ctr2" id="g4">4</td>
        <td class="ctr1" id="h5">0</td>
        <td class="ctr2" id="i4">9</td>
        <td class="ctr1" id="j5">0</td>
        <td class="ctr2" id="k3">4</td>
        <td class="ctr1" id="l5">0</td>
        <td class="ctr2" id="m5">1</td>
    </tr>
    </tbody>
</table>
<div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.7.9.201702052155</span>
</div>
</body>
</html>