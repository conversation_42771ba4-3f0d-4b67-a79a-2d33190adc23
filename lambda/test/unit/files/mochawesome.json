{"stats": {"suites": 15, "tests": 30, "passes": 29, "pending": 1, "failures": 0, "start": "2024-02-14T21:41:11.349Z", "end": "2024-02-14T21:41:11.379Z", "duration": 30, "testsRegistered": 30, "passPercent": 100, "pendingPercent": 3.3333333333333335, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "19f8f8f8-5bde-440b-8215-686354aa8243", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "5c70d87e-adf7-499a-acd7-b9bda1401ea0", "title": "service", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/buildEventTest.test.js", "file": "/test/unit/buildEventTest.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should process the event correctly", "fullTitle": "service should process the event correctly", "timedOut": false, "duration": 2, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const __dirname = path.dirname(fileURLToPath(import.meta.url));\nconst samplePayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/sns.json'), 'utf8'));\nconst translatedPayload = translateLambdaEvent(samplePayload);\nconst results = await getBuildDetailsFromPayloadService(translatedPayload);\nexpect(results).to.be.an('object');\nexpect(results.gitRepo).to.equal('https://github.com/Quotable-Value/dynamic-environments.git');\nexpect(results.gitSha).to.equal('49ca888b09f2751014edc5170e377f5e271e7a72');\nexpect(results.gitBranch).to.equal('HEAD');\nexpect(results.projectName).to.equal('api-dynamic-environments');", "err": {}, "uuid": "d103e38d-def8-410d-9123-f67bbc63ac66", "parentUUID": "5c70d87e-adf7-499a-acd7-b9bda1401ea0", "isHook": false, "skipped": false}], "suites": [], "passes": ["d103e38d-def8-410d-9123-f67bbc63ac66"], "failures": [], "pending": [], "skipped": [], "duration": 2, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "b522fa79-3300-4615-9d73-1b234e045542", "title": "Build event subscriber", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/buildEventsSubscriber.test.js", "file": "/test/unit/buildEventsSubscriber.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should process the event", "fullTitle": "Build event subscriber should process the event", "timedOut": false, "duration": 0, "state": "pending", "speed": null, "pass": false, "fail": false, "pending": true, "context": null, "code": "", "err": {}, "uuid": "3c4e1c36-12a5-4bd6-a789-34eb62ab2305", "parentUUID": "b522fa79-3300-4615-9d73-1b234e045542", "isHook": false, "skipped": false}], "suites": [], "passes": [], "failures": [], "pending": ["3c4e1c36-12a5-4bd6-a789-34eb62ab2305"], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "b6d6dd40-f224-4fad-88cb-7f0ebe6fed64", "title": "paginateArray function", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/common.test.js", "file": "/test/unit/common.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should paginate the array correctly", "fullTitle": "paginateArray function should paginate the array correctly", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nconst offset = 0;\nconst limit = 3;\nconst result = paginateArray(inputArray, limit, offset);\nexpect(result).to.have.property('count');\nexpect(result).to.have.property('currentPageData');\nexpect(result).to.have.property('pageNumber');\nexpect(result).to.have.property('totalPages');\nexpect(result.count).to.equal(10);\nexpect(result.currentPageData).to.deep.equal([1, 2, 3]);\nexpect(result.pageNumber).to.equal(1);\nexpect(result.totalPages).to.equal(4);", "err": {}, "uuid": "52498b5d-ce6a-464b-b8ad-f6749f139269", "parentUUID": "b6d6dd40-f224-4fad-88cb-7f0ebe6fed64", "isHook": false, "skipped": false}], "suites": [], "passes": ["52498b5d-ce6a-464b-b8ad-f6749f139269"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "abfe18e3-77eb-4df0-89a3-d3b6da9e226f", "title": "Create Report Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/createReportHandler.test.js", "file": "/test/unit/createReportHandler.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid appName is not provided", "fullTitle": "Create Report Handler should return an error when a valid appName is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    body: JSON.stringify({\n        appName: null,\n        env: 'cicd',\n        testType: 'unit'\n    }),\n};\n// when\nconst result = await createTestReportHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);", "err": {}, "uuid": "a2a712d3-50a5-4e83-b5d2-18b3a8435d49", "parentUUID": "abfe18e3-77eb-4df0-89a3-d3b6da9e226f", "isHook": false, "skipped": false}, {"title": "should return an error when a valid env is not provided", "fullTitle": "Create Report Handler should return an error when a valid env is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    body: JSON.stringify({\n        appName: 'testApp',\n        env: null,\n        testType: 'unit'\n    }),\n};\n// when\nconst result = await createTestReportHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);", "err": {}, "uuid": "4a269f14-8926-4c2b-832a-c753b977c852", "parentUUID": "abfe18e3-77eb-4df0-89a3-d3b6da9e226f", "isHook": false, "skipped": false}, {"title": "should return an error when a valid testType is not provided", "fullTitle": "Create Report Handler should return an error when a valid testType is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    body: JSON.stringify({\n        appName: 'testApp',\n        env: 'cicd',\n        testType: null\n    }),\n};\n// when\nconst result = await createTestReportHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);", "err": {}, "uuid": "72240b49-6ed8-4b03-adb1-0552208ea4f0", "parentUUID": "abfe18e3-77eb-4df0-89a3-d3b6da9e226f", "isHook": false, "skipped": false}, {"title": "should create a record", "fullTitle": "Create Report Handler should create a record", "timedOut": false, "duration": 3, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDb()\nconst mockEvent = {\n    body: JSON.stringify({\n        appName: 'dae123',\n        env: 'api-stats',\n        testType: 'integration'\n    }),\n};\n// when\nconst result = await createTestReportHandler(mockEvent);\nconsole.log(result);\n// then\nexpect(result.body.data.reportId).exist;\nconst folder = `dae123/api-stats/${result.body.data.reportId}/integration`;\nexpect(result.body.data.folder).to.equal(DEPLOYMENT_REPORTS_BUCKET + '/' + folder);\nexpect(result.body.status).to.equal(STATUS_SUCCESS);", "err": {}, "uuid": "d234d5e1-8d7b-44c0-a97f-7437d84c8232", "parentUUID": "abfe18e3-77eb-4df0-89a3-d3b6da9e226f", "isHook": false, "skipped": false}], "suites": [], "passes": ["a2a712d3-50a5-4e83-b5d2-18b3a8435d49", "4a269f14-8926-4c2b-832a-c753b977c852", "72240b49-6ed8-4b03-adb1-0552208ea4f0", "d234d5e1-8d7b-44c0-a97f-7437d84c8232"], "failures": [], "pending": [], "skipped": [], "duration": 3, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "0dd37380-f966-46df-ae5a-8d5d1fa22957", "title": "Deploy All DAE Applications Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/deployAllDaeApplications.test.js", "file": "/test/unit/deployAllDaeApplications.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "Deploy All DAE Applications Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: null, // Triggering the error condition\n        applicationName: 'validApplicationName',\n    },\n};\n// when\nconst result = await deployAllDaeApplicationsHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "8f440921-24d3-4aab-911a-fe5f2bd75ffc", "parentUUID": "0dd37380-f966-46df-ae5a-8d5d1fa22957", "isHook": false, "skipped": false}, {"title": "should return an error when another deployment is in progress", "fullTitle": "Deploy All DAE Applications Handler should return an error when another deployment is in progress", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDaeDeploymentInProgress();\nconst mockEvent = {\n    pathParameters: {\n        name: 'AppEnv',\n    },\n};\n// when\nconst result = await deployAllDaeApplicationsHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "526c7c67-fd65-4651-b75a-e1b2673a3882", "parentUUID": "0dd37380-f966-46df-ae5a-8d5d1fa22957", "isHook": false, "skipped": false}], "suites": [], "passes": ["8f440921-24d3-4aab-911a-fe5f2bd75ffc", "526c7c67-fd65-4651-b75a-e1b2673a3882"], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "57d552a9-55ea-4cf6-abad-6c5fe69c586d", "title": "Deploy Application Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/deployDaeApplication.test.js", "file": "/test/unit/deployDaeApplication.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "Deploy Application Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: null, // Triggering the error condition\n        applicationName: 'validApplicationName',\n    },\n};\n// when\nconst result = await deployDaeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "40dff397-2e22-40e6-ba32-c2864967593d", "parentUUID": "57d552a9-55ea-4cf6-abad-6c5fe69c586d", "isHook": false, "skipped": false}, {"title": "should return an error when a valid application is not provided", "fullTitle": "Deploy Application Handler should return an error when a valid application is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'AppEnv',\n        applicationName: null, // Triggering the error condition\n    },\n};\n// when\nconst result = await deployDaeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "a25e87fc-ad82-45ea-a440-7d73d46a97c6", "parentUUID": "57d552a9-55ea-4cf6-abad-6c5fe69c586d", "isHook": false, "skipped": false}, {"title": "should return an error when another deployment is in progress", "fullTitle": "Deploy Application Handler should return an error when another deployment is in progress", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDaeDeploymentInProgress();\nconst mockEvent = {\n    pathParameters: {\n        name: 'AppEnv',\n        applicationName: 'MyApplication',\n    },\n};\n// when\nconst result = await deployDaeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "2154c23b-7a00-4daa-a32e-5f879b9e8f97", "parentUUID": "57d552a9-55ea-4cf6-abad-6c5fe69c586d", "isHook": false, "skipped": false}], "suites": [], "passes": ["40dff397-2e22-40e6-ba32-c2864967593d", "a25e87fc-ad82-45ea-a440-7d73d46a97c6", "2154c23b-7a00-4daa-a32e-5f879b9e8f97"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "32a5515b-7bd2-4d06-87fb-be9d0cdb6f85", "title": "Deploy Datastore Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/deployDatastore.test.js", "file": "/test/unit/deployDatastore.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "Deploy Datastore Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: '',\n        datastore: 'qv_apihub'\n    },\n};\n// when\nconst result = await deployDatastoreHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "7e469cbd-c247-40a7-b364-406a067e339c", "parentUUID": "32a5515b-7bd2-4d06-87fb-be9d0cdb6f85", "isHook": false, "skipped": false}, {"title": "should return an error when a valid datastore is not provided", "fullTitle": "Deploy Datastore Handler should return an error when a valid datastore is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'testdde',\n        datastore: ''\n    },\n};\n// when\nconst result = await deployDatastoreHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "050a047f-9781-4f60-8f14-cabf49e6765b", "parentUUID": "32a5515b-7bd2-4d06-87fb-be9d0cdb6f85", "isHook": false, "skipped": false}], "suites": [], "passes": ["7e469cbd-c247-40a7-b364-406a067e339c", "050a047f-9781-4f60-8f14-cabf49e6765b"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "a9b29dfe-452d-44d1-8eb3-1c2e837d3d1c", "title": "Deploy Application Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/deployDdeApplication.test.js", "file": "/test/unit/deployDdeApplication.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "Deploy Application Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: null, // Triggering the error condition\n        applicationName: 'validApplicationName',\n    },\n};\n// when\nconst result = await deployDdeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "35db7f2c-7a96-44b7-bf8b-21ea1201dc7e", "parentUUID": "a9b29dfe-452d-44d1-8eb3-1c2e837d3d1c", "isHook": false, "skipped": false}, {"title": "should return an error when a valid application is not provided", "fullTitle": "Deploy Application Handler should return an error when a valid application is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'DataEnv',\n        applicationName: null, // Triggering the error condition\n    },\n};\n// when\nconst result = await deployDdeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "310d05d8-832e-4ff1-ae82-6eb6387e7aae", "parentUUID": "a9b29dfe-452d-44d1-8eb3-1c2e837d3d1c", "isHook": false, "skipped": false}, {"title": "should return an error when another deployment is in progress", "fullTitle": "Deploy Application Handler should return an error when another deployment is in progress", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDdeDeploymentInProgress();\nconst mockEvent = {\n    pathParameters: {\n        name: 'DataEnv',\n        applicationName: 'MyApplication',\n    },\n};\n// when\nconst result = await deployDdeApplicationHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "3fd2175b-d1c3-4d3e-9e88-7cb7941b9fd1", "parentUUID": "a9b29dfe-452d-44d1-8eb3-1c2e837d3d1c", "isHook": false, "skipped": false}], "suites": [], "passes": ["35db7f2c-7a96-44b7-bf8b-21ea1201dc7e", "310d05d8-832e-4ff1-ae82-6eb6387e7aae", "3fd2175b-d1c3-4d3e-9e88-7cb7941b9fd1"], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "24eba74f-14dc-4f54-b53d-b28671841448", "title": "Get App Stacks Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/getAppStacks.test.js", "file": "/test/unit/getAppStacks.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return app stacks with query parameter ", "fullTitle": "Get App Stacks Handler should return app stacks with query parameter ", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    queryStringParameters: {\n        stack: ApplicationStack.MONARCH,\n    },\n};\nsetupAppStacks();\n// when\nconst result = await getAppStacksHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.appStacks).to.have.length(2)", "err": {}, "uuid": "1328c51a-3ffe-4d03-a658-8933415a926b", "parentUUID": "24eba74f-14dc-4f54-b53d-b28671841448", "isHook": false, "skipped": false}, {"title": "should return all app stacks", "fullTitle": "Get App Stacks Handler should return all app stacks", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n};\nsetupAppStacks();\n// when\nconst result = await getAppStacksHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.appStacks).to.have.length(3)", "err": {}, "uuid": "84be118a-e53b-4006-9393-54f13b4b7954", "parentUUID": "24eba74f-14dc-4f54-b53d-b28671841448", "isHook": false, "skipped": false}], "suites": [], "passes": ["1328c51a-3ffe-4d03-a658-8933415a926b", "84be118a-e53b-4006-9393-54f13b4b7954"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "9b04ea2e-d065-4158-af74-8609648e3b97", "title": "getDataEnvironmentByName Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/getDataEnvironmentByName.test.js", "file": "/test/unit/getDataEnvironmentByName.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "getDataEnvironmentByName Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: null,\n    },\n};\n// when\nconst result = await getDataEnvironmentByNameHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "b579d772-1f3e-4242-82a4-bc4e000a82f3", "parentUUID": "9b04ea2e-d065-4158-af74-8609648e3b97", "isHook": false, "skipped": false}, {"title": "should return a data environment", "fullTitle": "getDataEnvironmentByName Handler should return a data environment", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'mydde',\n    },\n};\nsetupDb();\n// when\nconst result = await getDataEnvironmentByNameHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.environments.name).to.equal('mydde');\nexpect(result.body.environments.dataStores).to.have.length(1)\nexpect(result.body.environments.applications).to.have.length(1)", "err": {}, "uuid": "4c0478b4-9dc0-4e50-aa34-3ae5a6bada80", "parentUUID": "9b04ea2e-d065-4158-af74-8609648e3b97", "isHook": false, "skipped": false}], "suites": [], "passes": ["b579d772-1f3e-4242-82a4-bc4e000a82f3", "4c0478b4-9dc0-4e50-aa34-3ae5a6bada80"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "d2663dfb-fd7d-4848-940e-9665fffb9e96", "title": "Get Database Build Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/getDatabaseBuild.test.js", "file": "/test/unit/getDatabaseBuild.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when a valid name is not provided", "fullTitle": "Get Database Build Handler should return an error when a valid name is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: '',\n        datastore: 'qv_apihub'\n    },\n};\n// when\nconst result = await getDatabaseBuildHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "a2daa289-07c8-4758-a16a-ecf0aeb36270", "parentUUID": "d2663dfb-fd7d-4848-940e-9665fffb9e96", "isHook": false, "skipped": false}, {"title": "should return an error when a valid datastore is not provided", "fullTitle": "Get Database Build Handler should return an error when a valid datastore is not provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'testdde',\n        datastore: ''\n    },\n};\n// when\nconst result = await getDatabaseBuildHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "4cce8d7c-1c07-4383-b109-46b9173bfb2d", "parentUUID": "d2663dfb-fd7d-4848-940e-9665fffb9e96", "isHook": false, "skipped": false}, {"title": "should return error", "fullTitle": "Get Database Build Handler should return error", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'testdde',\n        datastore: 'qv_apihub'\n    },\n};\nsetUpDb()\nsetUpS3FileNoExist()\n// when\nconst result = await getDatabaseBuildHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_MISSING);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "a98716c1-51d5-41d3-9f1d-efe9c09f51f5", "parentUUID": "d2663dfb-fd7d-4848-940e-9665fffb9e96", "isHook": false, "skipped": false}, {"title": "should return diff", "fullTitle": "Get Database Build Handler should return diff", "timedOut": false, "duration": 8, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    pathParameters: {\n        name: 'testdde',\n        datastore: 'qv_apihub'\n    },\n};\nsetUpDb()\nsetUpS3()\n// when\nconst result = await getDatabaseBuildHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.diff).to.have.length(1);\nexpect(result.body.diff[0]).to.equal('cicd/qv_apihub/tables/query.sql');", "err": {}, "uuid": "e70e1209-485b-464e-9fa0-bfe63decfb11", "parentUUID": "d2663dfb-fd7d-4848-940e-9665fffb9e96", "isHook": false, "skipped": false}], "suites": [], "passes": ["a2daa289-07c8-4758-a16a-ecf0aeb36270", "4cce8d7c-1c07-4383-b109-46b9173bfb2d", "a98716c1-51d5-41d3-9f1d-efe9c09f51f5", "e70e1209-485b-464e-9fa0-bfe63decfb11"], "failures": [], "pending": [], "skipped": [], "duration": 9, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "cb5a7c78-aa2b-4749-b718-0b0e3c214410", "title": "Get Datastore Stacks Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/getDatastoreStacks.test.js", "file": "/test/unit/getDatastoreStacks.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return datastore stacks with query parameter ", "fullTitle": "Get Datastore Stacks Handler should return datastore stacks with query parameter ", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n    queryStringParameters: {\n        stacks: ApplicationStack.MONARCH,\n    },\n};\nsetupDatastoreStacks();\n// when\nconst result = await getDatastoreStacksHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.datastoreStacks).to.have.length(2)", "err": {}, "uuid": "02969e64-5bbe-440b-aeee-454d02b3cf0a", "parentUUID": "cb5a7c78-aa2b-4749-b718-0b0e3c214410", "isHook": false, "skipped": false}, {"title": "should return all datastore stacks", "fullTitle": "Get Datastore Stacks Handler should return all datastore stacks", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst mockEvent = {\n};\nsetupDatastoreStacks();\n// when\nconst result = await getDatastoreStacksHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_SUCCESS);\nexpect(result.body.datastoreStacks).to.have.length(3)", "err": {}, "uuid": "ea9ca34d-fc42-4c5e-9744-ea6d2e03a53d", "parentUUID": "cb5a7c78-aa2b-4749-b718-0b0e3c214410", "isHook": false, "skipped": false}], "suites": [], "passes": ["02969e64-5bbe-440b-aeee-454d02b3cf0a", "ea9ca34d-fc42-4c5e-9744-ea6d2e03a53d"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "51dd9c4a-37a9-46c7-914f-97bb56619180", "title": "getQivsStreamCount", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/getQivsStream.test.js", "file": "/test/unit/getQivsStream.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return the correct Qivs stream count", "fullTitle": "getQivsStreamCount should return the correct Qivs stream count", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nconst ddbMock = mockClient(dynamoDbClient);\nmockDb(ddbMock);\nconst dataEnv = 'DataEnv1';\n// when\nconst result = await getQivsStreamCount(dataEnv);\n// then\nexpect(result.status).to.equal(STATUS_SUCCESS);\nexpect(result.count).to.equal(1);", "err": {}, "uuid": "78b6a1e7-d0a2-4b24-971d-c191cb27563a", "parentUUID": "51dd9c4a-37a9-46c7-914f-97bb56619180", "isHook": false, "skipped": false}], "suites": [], "passes": ["78b6a1e7-d0a2-4b24-971d-c191cb27563a"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "79353915-3d53-4e32-825e-95307c5f48db", "title": "Build event subscriber", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/reportsEventsSubscriber.test.js", "file": "/test/unit/reportsEventsSubscriber.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should process the event", "fullTitle": "Build event subscriber should process the event", "timedOut": false, "duration": 2, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDB()\nsetUpS3()\nconst __dirname = path.dirname(fileURLToPath(import.meta.url));\nconst eventPayload = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/s3_report.json'), 'utf8'));\n// when\nawait reportsEventsSubscriber(eventPayload);", "err": {}, "uuid": "e1481ef6-9633-47c2-a978-31b7937aabf7", "parentUUID": "79353915-3d53-4e32-825e-95307c5f48db", "isHook": false, "skipped": false}], "suites": [], "passes": ["e1481ef6-9633-47c2-a978-31b7937aabf7"], "failures": [], "pending": [], "skipped": [], "duration": 2, "root": false, "rootEmpty": false, "_timeout": 15000}, {"uuid": "6a14ccd2-1791-43fa-853f-1326cf76db16", "title": "Deploy Application Handler", "fullFile": "/Users/<USER>/qv/dynamic-environments/lambda/test/unit/updateAppEnvironment.test.js", "file": "/test/unit/updateAppEnvironment.test.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an error when another deployment is in progress", "fullTitle": "Deploy Application Handler should return an error when another deployment is in progress", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// given\nmockDaeDeploymentInProgress();\nconst mockEvent = {\n    pathParameters: {\n        name: 'AppEnv',\n        applicationName: 'MyApplication',\n    },\n    body: JSON.stringify({dataEnv: 'dataEnv'})\n};\n// when\nconst result = await updateAppEnvironmentHandler(mockEvent);\n// then\nexpect(result.body.status).to.equal(STATUS_INVALID);\nexpect(result.body).to.have.property('message');", "err": {}, "uuid": "b9d5dfa2-2766-44e7-8002-083476f9c788", "parentUUID": "6a14ccd2-1791-43fa-853f-1326cf76db16", "isHook": false, "skipped": false}], "suites": [], "passes": ["b9d5dfa2-2766-44e7-8002-083476f9c788"], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 15000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 15000}], "meta": {"mocha": {"version": "10.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}