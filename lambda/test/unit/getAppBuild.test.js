import { expect } from 'chai';
import { STATUS_INVALID } from "common/enums/crudStatus.js";
import { deployAppHandler } from "../../src/handler/deployAppBuild.js";

describe('Deploy App Handler', () => {
  it('should return an error when appName is not provided', async () => {
    const mockEvent = {
      pathParameters: {
        appName: null,
      },
    };

    const result = await deployAppHandler(mockEvent);

    expect(result.body.status).to.equal(STATUS_INVALID);
    expect(result.body).to.have.property('message');
  });

  it('should return an error when build and env are not provided', async () => {
    const mockEvent = {
      pathParameters: {
        appName: 'api-ta',
      },
      queryStringParameters: {
        build: null,
        env: null
      },
    };

    const result = await deployAppHandler(mockEvent);

    expect(result.body.status).to.equal(STATUS_INVALID);
    expect(result.body).to.have.property('message');
  });

  it('should return an error when env is not provided', async () => {
    const mockEvent = {
      pathParameters: {
        appName: 'api-ta',
      },
      queryStringParameters: {
        build: 'master',
        env: null
      },
    };

    const result = await deployAppHandler(mockEvent);

    expect(result.body.status).to.equal(STATUS_INVALID);
    expect(result.body).to.have.property('message');
  });

  it('should return an error when an invalid env is provided', async () => {
    const mockEvent = {
      pathParameters: {
        appName: 'api-ta',
      },
      queryStringParameters: {
        build: 'audit',
        env: 'mydae'
      },
    };

    const result = await deployAppHandler(mockEvent);

    expect(result.body.status).to.equal(STATUS_INVALID);
    expect(result.body.message).to.include('Invalid env name provided');
  });

});
