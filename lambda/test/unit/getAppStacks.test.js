import {expect} from 'chai';
import {STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {getAppStacksHandler} from "../../src/handler/getAppStacks.js";
import {ApplicationStack} from "common/enums/applicationStack.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {APP_STACKS} from "common/util/const.js";

describe('Get App Stacks Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return app stacks with query parameter ', async () => {
        // given
        const mockEvent = {
            queryStringParameters: {
                stack: ApplicationStack.MONARCH,
            },
        };
        setupAppStacks();
        // when
        const result = await getAppStacksHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.appStacks).to.have.length(2)
    });
    it('should return all app stacks', async () => {
        // given
        const mockEvent = {
        };
        setupAppStacks();
        // when
        const result = await getAppStacksHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.appStacks).to.have.length(3)
    });
});

function setupAppStacks() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(APP_STACKS),
    }).resolves({
        Items: [
            {
                Name: {S: 'app1'},
                Pipeline: {S: ''},
                Repo: {S: ''},
                Platform: {S: ''},
                CodePath: {S: ''},
                TestPath: {S: ''},
                DeployDefault: {BOOL: true},
                Selectable: {BOOL: true},
                Stacks: {L: [{S: 'Monarch'}]},
                Enabled: {BOOL: true},

            },
            {
                Name: {S: 'app2'},
                Pipeline: {S: ''},
                Repo: {S: ''},
                Platform: {S: ''},
                CodePath: {S: ''},
                TestPath: {S: ''},
                DeployDefault: {BOOL: true},
                Selectable: {BOOL: true},
                Stacks: {L: [{S: 'Monarch'}]},
                Enabled: {BOOL: false},
            },
            {
                Name: {S: 'app3'},
                Pipeline: {S: ''},
                Repo: {S: ''},
                Platform: {S: ''},
                CodePath: {S: ''},
                TestPath: {S: ''},
                DeployDefault: {BOOL: true},
                Selectable: {BOOL: true},
                Stacks: {L: [{S: 'Public Website'}]},
                Enabled: {BOOL: true},
            }
        ]
    });
}

