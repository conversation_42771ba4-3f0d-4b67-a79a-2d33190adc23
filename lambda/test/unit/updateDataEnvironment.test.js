import {expect} from 'chai';
import {STATUS_INVALID} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {mockDdeDeploymentInProgress} from "./dbMockUtil.js";
import {updateDataEnvironmentHandler} from "../../src/handler/updateDataEnvironment.js";

describe('Update Data Environment Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return an error when another deployment is in progress', async () => {
        // given
        mockDdeDeploymentInProgress();
        const mockEvent = {
            pathParameters: {
                name: 'DataEnv',
                applicationName: 'MyApplication',
            },
            body: JSON.stringify({dataEnv: 'dataEnv'})
        };
        // when
        const result = await updateDataEnvironmentHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_INVALID);
        expect(result.body).to.have.property('message');
    });
});

