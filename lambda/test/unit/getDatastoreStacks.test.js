import {expect} from 'chai';
import {STATUS_SUCCESS} from "common/enums/crudStatus.js";
import Logger from "common/util/Logger.js";
import {getDatastoreStacksHandler} from "../../src/handler/getDatastoreStacks.js";
import {ApplicationStack} from "common/enums/applicationStack.js";
import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {DATASTORE_STACKS} from "common/util/const.js";

describe('Get Datastore Stacks Handler', () => {
    global.logger = new Logger();
    global.localDebug = true;
    it('should return datastore stacks with query parameter ', async () => {
        // given
        const mockEvent = {
            queryStringParameters: {
                stacks: ApplicationStack.MONARCH,
            },
        };
        setupDatastoreStacks();
        // when
        const result = await getDatastoreStacksHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.datastoreStacks).to.have.length(2)
    });
    it('should return all datastore stacks', async () => {
        // given
        const mockEvent = {
        };
        setupDatastoreStacks();
        // when
        const result = await getDatastoreStacksHandler(mockEvent);
        // then
        expect(result.body.status).to.equal(STATUS_SUCCESS);
        expect(result.body.datastoreStacks).to.have.length(3)
    });
});

function setupDatastoreStacks() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(DATASTORE_STACKS),
    }).resolves({
        Items: [
            {
                Name: {S: 'qvnz'},
                Type: {S: 'QIVS'},
                Stacks: {L: [{S: 'Monarch'}]},
                Enabled: {BOOL: true},

            },
            {
                Name: {S: 'qvnz_history'},
                Type: {S: 'QIVS'},
                Stacks: {L: [{S: 'Monarch'}]},
                Enabled: {BOOL: false},

            },
            {
                Name: {S: 'website'},
                Type: {S: 'QIVS'},
                Stacks: {L: [{S: 'Public Website'}]},
                Enabled: {BOOL: false},

            }
        ]
    });
}

