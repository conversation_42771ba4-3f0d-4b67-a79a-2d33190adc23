import {mockClient} from "aws-sdk-client-mock";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {DeploymentStatus} from "common/enums/deploymentStatus.js";
import {ApplicationType} from "common/enums/applicationType.js";
import {getTableName} from "common/dal/getResourceName.js";
import {APP_ENVIRONMENTS, APPLICATIONS, DATA_ENVIRONMENTS, DEPLOYMENTS} from "common/util/const.js";
export function mockNoDaeDeployment(ddbMock) {
    ddbMock.onAnyCommand({
        TableName: getTableName(DEPLOYMENTS),
            IndexName: 'EnvIndex',
            KeyConditionExpression: '#Env = :Env',
            ExpressionAttributeNames: { '#Env': 'Env' },
            ExpressionAttributeValues: { ':Env': { S: 'AppEnv' } }
        }
    ).resolves({
        Items: [
            {
                Status: {S: DeploymentStatus.TESTS_COMPLETED},
                Type: {S: ApplicationType.DAE},
            }
        ]
    });
}


export function mockDaeDeploymentInProgress() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(DEPLOYMENTS),
        IndexName: 'EnvIndex',
        ExpressionAttributeValues: {':Env': {S: 'AppEnv'}}
    }).resolves({
        Items: [
            {
                Status: {S: DeploymentStatus.IN_PROGRESS},
                Type: {S: ApplicationType.DAE},
            }
        ]
    });
}

export function mockNoDdeDeployment(ddbMock) {
    ddbMock.onAnyCommand({
        TableName: getTableName(DEPLOYMENTS),
            IndexName: 'EnvIndex',
            KeyConditionExpression: '#Env = :Env',
            ExpressionAttributeNames: { '#Env': 'Env' },
            ExpressionAttributeValues: { ':Env': { S: 'DataEnv' } }
        }
    ).resolves({
        Items: [
            {
                Status: {S: DeploymentStatus.TESTS_COMPLETED},
                Type: {S: ApplicationType.DDE},
            }
        ]
    });
}


export function mockDdeDeploymentInProgress() {
    const ddbMock = mockClient(dynamoDbClient);
    ddbMock.onAnyCommand({
        TableName: getTableName(DEPLOYMENTS),
        IndexName: 'EnvIndex',
        ExpressionAttributeValues: {':Env': {S: 'DataEnv'}}
    }).resolves({
        Items: [
            {
                Status: {S: DeploymentStatus.IN_PROGRESS},
                Type: {S: ApplicationType.DDE},
            }
        ]
    });
}

export function mockGetApplicationByKey(ddbMock,key) {
    ddbMock.onAnyCommand({
            TableName: getTableName(APPLICATIONS),
            KeyConditionExpression: '#key = :value',
            ExpressionAttributeNames: { '#key': 'Key' },
            ExpressionAttributeValues: { ':value': { S: key } }
        }
    ).resolves({
        Items: [
            {
                Name: { S: 'MyApplication' },
                DeployBranch: { S: 'master' },
            }
        ]
    });
}

export function  mockGetDataEnvironmentByName(ddbMock,dataEnv){
    ddbMock.onAnyCommand({
            "TableName": getTableName(DATA_ENVIRONMENTS),
            "KeyConditionExpression": "#name = :value",
            "ExpressionAttributeNames": {
                "#name": "Name"
            },
            "ExpressionAttributeValues": {
                ":value": {
                    "S": dataEnv
                }
            }
        }
    ).resolves({
        Items: [
            {
                Name: {
                    "S": dataEnv
                },
                DeployBranch: {
                    "S": "master"
                },
                Logs:{
                    L:[
                        {
                            Url: {
                                S: "https://example.com"
                            },
                            Task:{
                                S: "task"
                            }
                        }
                    ]
                }
            }
        ]
    });
}

export function  mockGetAppEnvironmentByName(ddbMock,appEnv){
    ddbMock.onAnyCommand({
            "TableName": getTableName(APP_ENVIRONMENTS),
            "KeyConditionExpression": "#name = :value",
            "ExpressionAttributeNames": {
                "#name": "Name"
            },
            "ExpressionAttributeValues": {
                ":value": {
                    "S": appEnv
                }
            }
        }
    ).resolves({
        Items: [
            {
                Name: {
                    "S": appEnv
                },
                DeployBranch: {
                    "S": "master"
                },
                Logs:{
                    L:[
                        {
                            Url: {
                                S: "https://example.com"
                            },
                            Task:{
                                S: "task"
                            }
                        }
                    ]
                }
            }
        ]
    });
}


export function mockGetApplicationsByEnv(ddbMock,env){
    ddbMock.onAnyCommand({
            TableName: getTableName(APPLICATIONS),
            IndexName: 'EnvIndex',
            KeyConditionExpression: '#env = :value',
            ExpressionAttributeNames: { '#env': 'Env' },
            ExpressionAttributeValues: { ':value': { S: env } }
        }

    ).resolves({
        Items: [
            {
                Type: {
                    "S": ApplicationType.DAE
                },
                Name: {
                    "S": "MyApplication"
                },
                DeployBranch: {
                    "S": "master"
                }
            },
            {
                Type: {
                    "S": ApplicationType.DAE
                },
                Name: {
                    "S": "MyApplication2"
                },
                DeployBranch: {
                    "S": "master"
                }
            }
        ]
    });
}

