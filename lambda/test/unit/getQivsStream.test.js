import {describe} from 'mocha';
import {expect} from 'chai';
import {getApiStreamCount} from '../../src/service/getApiStreamCount.js';
import {STATUS_SUCCESS} from 'common/enums/crudStatus.js';
import {mockClient} from "aws-sdk-client-mock";
import {STABLE} from "common/enums/environmentStatus.js";
import Logger from "common/util/Logger.js";
import {dynamoDbClient} from "common/util/dynamodb.js";
import {getTableName} from "common/dal/getResourceName.js";
import {API_STREAM_APP, APP_ENVIRONMENTS, APPLICATIONS} from "common/util/const.js";
import {ApplicationType} from "common/enums/applicationType.js";

describe('getQivsStreamCount', () => {
  global.logger = new Logger();
  global.localDebug = true;
  it('should return the correct Qivs stream count', async () => {
    // given
    const ddbMock = mockClient(dynamoDbClient);
    mockDb(ddbMock);
    const dataEnv = 'DataEnv1';
    // when
    const result = await getApiStreamCount(dataEnv);
    // then
    expect(result.status).to.equal(STATUS_SUCCESS);
    expect(result.count[API_STREAM_APP]).to.equal(1);
  });
});

function mockDb(ddbMock) {
  ddbMock.onAnyCommand({
    IndexName: 'DataEnvIndex',
    TableName: getTableName(APP_ENVIRONMENTS),
    ExpressionAttributeValues: {':value': {S: 'DataEnv1'}}
  }).resolves({
    Items: [
      {
        Status: {S: STABLE},
        Name: {S: 'Env1'},
        DataEnv: {S: 'DataEnv1'},
      },
      {
        Status: {S: STABLE},
        Name: {S: 'Env2'},
        DataEnv: {S: 'DataEnv1'},
      }
    ]
  }).onAnyCommand({
    TableName: getTableName(APPLICATIONS),
    ExpressionAttributeValues: {':value': {S: `${ApplicationType.DAE}-Env1-api-stream`}}
  }).resolves({
    Items: [
      {
        Key: {S: 'Env1-api-stream'},
        Name: {S: 'api-stream'},
        Env: {S: 'Env1'},
      }
    ]
  }).onAnyCommand({
    TableName: getTableName(APPLICATIONS),
    ExpressionAttributeValues: {':value': {S: `${ApplicationType.DAE}-Env2-api-stream`}}
  }).resolves({
    Items: []
  });
}
