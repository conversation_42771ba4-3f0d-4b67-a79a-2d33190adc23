domainName: "api.cicd.qvapi.co.nz"
apiIssuer: https://login.dev.qvmonarch.co.nz/
cors:
  maxAge: 6000
securityGroupIds:
  - "sg-04664b2b75c9fc53a"
subnetIds:
  - "subnet-0dd620c23992d88ae"
  - "subnet-097383e7e4f76f0ec"
role: "arn:aws:iam::948396734470:role/API-Lambda-Execution-CICD"
region: "ap-southeast-2"
apiKeys:
  - "cicd-qv"

# for serverless offline only, exclude everywhere else
localCors: false
localOffline:
  httpPort: 3003
  allowCache: true
  noAuth: true
  noTimeout: true

# schedule for the dae lambda to run
appEnvVerify:
  enabled: false
  rate: cron(0 22 ? * SUN *)
  branch: master
  dde: ''
s3Bucket: "qv-deployment-reports"
topicName: "build-notification"