locals {
  severity = "ERROR"

  # Use the input variable for cloudwatch_groups
  cloudwatch_groups       = var.cloudwatch_groups
  cloudwatch_groups_source = join(" | ", [for group in local.cloudwatch_groups : "SOURCE '${group}'"])
}

resource "aws_cloudwatch_dashboard" "log_dashboard" {
  dashboard_name = "${var.env}-${var.app_name}-LogDashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "log",
        x      = 0,
        y      = 0,
        width  = 24,
        height = 6,
        properties = {
          query = <<EOF
${local.cloudwatch_groups_source}
| fields @timestamp, @message
| parse @message /"severity":\s*\"(?<severity>[^\"]+)\".*"errorCode":\s*\"(?<errorCode>[^\"]+)\".*"message":\s*\"(?<message_detail>[^\"]+)\"/
| filter severity = '${local.severity}'
| display @timestamp, severity, errorCode, message_detail, @message
| sort @timestamp desc
EOF
          title    = "${var.env} ${var.app_name} Log Events - ${local.severity}",
          stacked  = false,
          view     = "table"
        }
      },
      {
        type   = "log",
        x      = 0,
        y      = 12,
        width  = 24,
        height = 6,
        properties = {
          query = <<EOF
${local.cloudwatch_groups_source}
| fields @timestamp, @message
| parse @message /"severity":\s*\"(?<severity>[^\"]+)\".*"errorCode":\s*\"(?<errorCode>[^\"]+)\".*"message":\s*\"(?<message_detail>[^\"]+)\"/
| filter severity = '${local.severity}'
| stats count(*) as error_count by errorCode
| sort error_count desc
EOF
          title    = "Error Count by errorCode",
          stacked  = false,
          view     = "table"
        }
      }
    ]
  })
}

resource "aws_sns_topic" "alert_topic" {
  name = "${var.env}-${var.app_name}-error-log"

  tags = {
    costCentre = var.env
  }
}

resource "aws_cloudwatch_metric_alarm" "error_alarm" {
  alarm_name          = "${var.env}-${var.app_name}-service-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "Errors"
  namespace           = "LogMetrics"
  period              = 60
  statistic           = "Sum"
  threshold           = 1

  alarm_description = "Alert for ${var.env} ${var.app_name}: Service errors detected in logs."
  alarm_actions     = [aws_sns_topic.alert_topic.arn]

  dimensions = {
    LogGroup = local.cloudwatch_groups[0]
  }

  tags = {
    costCentre = var.env
  }
}

resource "aws_cloudwatch_log_metric_filter" "error_metric_filter" {
  name           = "${var.env}-${var.app_name}-error-filter"
  log_group_name = local.cloudwatch_groups[0]
  pattern        = "{ $.severity = \"${local.severity}\" }"

  metric_transformation {
    name      = "Errors"
    namespace = "LogMetrics"
    value     = "1"
  }
}
