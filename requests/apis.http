###
GET {{host}}/data


###
POST {{host}}/data/en3
Content-Type: application/json

{
  "branch": "master",
  "includes": [
    "PGMonarch"
  ]
}


###
DELETE {{host}}/data/en2

###
POST http://localhost:3003/app-environment/app99/deploy/monarch-web


### run tests
POST http://localhost:3003/app-environment/verify12/test
Content-Type: application/json

{
  "applications": [
    "user-profile"
  ],
  "branch": "feature/DEV3-522-generic-config",
  "testTypes": [
  ]
}

### deploy all
POST http://localhost:3003/app-environment/verify20/deploy
Content-Type: application/json


### deploy a single app
POST http://localhost:3003/app-environment/verify20/deploy/api-objection
Content-Type: application/json

### deploy a single app
POST http://localhost:3003/app-environment/verify20/deploy/audit
Content-Type: application/json

### deploy a single app
POST http://localhost:3003/app-environment/all5/deploy/monarch-web
Content-Type: application/json