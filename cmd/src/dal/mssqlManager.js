import mssql from 'mssql';
import { ERROR_CODES } from 'common/util/const.js';
import getConfig from 'common/config/getConfig.js';

export default async function getConnection(overrideConfig = {}) {
  const mssqlConfig = {
    user: await getConfig('MSSQL_USERNAME'),
    password: await getConfig('MSSQL_PASSWORD'),
    server: await getConfig('MSSQL_SERVER'),
    options: {
      encrypt: true,
      trustServerCertificate: true,
      requestTimeout: 60 * 1000 * 60 * 5, // timeout in milliseconds
    },
  };

  const config = { ...mssqlConfig, ...overrideConfig };
  try {
    return mssql.connect(config);
  } catch (e) {
    logger.error(ERROR_CODES.DATABASE_CONNECTION_ERROR.code, ERROR_CODES.DATABASE_CONNECTION_ERROR.message, e);
    throw e;
  }
}
