import runQuery from './pgManager.js';

export async function terminateSessions(dbName) {
  const query = `
        SELECT pg_terminate_backend(pg_stat_activity.pid)
        FROM pg_stat_activity
        WHERE pg_stat_activity.datname = '${dbName}';
    `;
  await runQuery(query);
}

export async function destroyPostgresDb(dbName) {
  const deleteDatabaseQuery = `DROP DATABASE IF EXISTS ${dbName}`;
  await runQuery(deleteDatabaseQuery);
}

export async function destroyPostgresTb(tablespaceName) {
  const deleteDatabaseQuery = `DROP TABLESPACE IF EXISTS ${tablespaceName};`;
  await runQuery(deleteDatabaseQuery);
}
