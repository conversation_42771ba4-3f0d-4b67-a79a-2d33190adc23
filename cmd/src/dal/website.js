import { hashedPassword } from 'common/util/const.js';
import runQuery from './pgManager.js';
import { pgPreparedInsert } from './pgCommon.js';

export async function getStringColumns(dbName, schema) {
  const query = `
      SELECT table_name, column_name
      FROM information_schema.columns
      WHERE table_schema = '${schema}'
        AND data_type IN ('varchar', 'text');
  `;

  const { rows } = await runQuery(query, dbName);
  return rows;
}

export async function getQvconzHostname(dbName) {
  const query = 'select hostname from website.wagtailcore_site where is_default_site = true';
  const result = await runQuery(query, dbName);
  const { hostname } = result.rows[0];
  return hostname;
}

export async function setupUserRoleForPublicWebsite(users, dbName) {
  for (const user of users) {
    await updatePublicUser(user, dbName);
  }
  const usersWithId = await getAuthUserIds(users, dbName);

  for (const user of usersWithId) {
    await updateAccount(user.authUserId, dbName);
  }
  for (const user of usersWithId) {
    const groupIds = await getGroupIds(user.auth_groups, dbName);

    await updateUserGroups(user.authUserId, groupIds, dbName);
  }
}

async function getAuthUserIds(users, dbName) {
  return Promise.all(users.map(async (user) => {
    const userResult = await getAuthUserByUsername(user, dbName);
    return {
      ...user,
      authUserId: userResult[0]?.id,
    };
  }));
}

async function updatePublicUser(user, dbName) {
  const existingUsers = await getAuthUserByUsername(user, dbName);
  if (existingUsers.length > 0) {
    logger.info(`User ${user.email} already exists`);
    return;
  }

  await insertPublicUser(user, dbName);
}

async function updateAccount(userId, dbName) {
  const existingAccounts = await getAccountByAuthUserId(userId, dbName);
  if (existingAccounts.length > 0) {
    logger.info(`Account ${userId} already exists`);
    return;
  }

  await insertAccount(userId, dbName);
}

async function insertAccount(userId, dbName) {
  const createAccountQuery = pgPreparedInsert('website.account_account', {
    phone: '+64 27 123 4567',
    address: 'test street',
    user_id: userId,
    costbuilder_subscription_id: '7c5f0ce0-8603-4e64-bd57-114a3d257852',
    is_subscribed: false,
    is_staff: true,
    costbuilder_is_license_holder: true,
    is_sent_to_mailchimp_qv: true,
    is_sent_to_mailchimp_cb: true,
  });
  await runQuery(createAccountQuery, dbName);
}

async function insertPublicUser(user, dbName) {
  const [firstname, lastname] = user.name.split(' ');
  const createUserQuery = pgPreparedInsert('website.auth_user', {
    password: hashedPassword,
    is_superuser: true,
    username: user.email,
    first_name: firstname,
    last_name: lastname,
    email: user.email,
    is_staff: true,
    is_active: true,
    date_joined: new Date(),
  });

  await runQuery(createUserQuery, dbName);
}

async function getGroupIds(groupNames, dbName) {
  const groupQuery = `SELECT id
                      FROM website.auth_group
                      WHERE name IN (${groupNames.map((name) => `'${name}'`).join(', ')})`;
  const groupResult = await runQuery(groupQuery, dbName);
  return groupResult.rows.map((row) => row.id);
}

async function updateUserGroups(userId, groupIds, dbName) {
  for (const groupId of groupIds) {
    const userGroupQuery = `SELECT *
                            FROM website.auth_user_groups
                            WHERE user_id = ${userId}
                              AND group_id = ${groupId}`;
    const userGroupResult = await runQuery(userGroupQuery, dbName);
    if (userGroupResult.rows.length === 0) {
      await insertUserGroup(userId, groupId, dbName);
    }
  }
}

async function insertUserGroup(userId, groupId, dbName) {
  const userGroupQueryInsert = `INSERT INTO website.auth_user_groups (user_id, group_id)
                                VALUES (${userId}, ${groupId})`;
  await runQuery(userGroupQueryInsert, dbName);
}

export async function getAuthUserByUsername(user, dbName) {
  const query = `SELECT *
                 FROM website.auth_user
                 WHERE username = '${user.email}'`;
  const results = await runQuery(query, dbName);
  return results.rows;
}

export async function getAccountByAuthUserId(userId, dbName) {
  const query = `SELECT *
                 FROM website.account_account
                 WHERE user_id = '${userId}'`;
  const results = await runQuery(query, dbName);
  return results.rows;
}
