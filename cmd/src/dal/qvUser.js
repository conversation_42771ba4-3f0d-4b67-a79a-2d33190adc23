import sql from 'mssql';
import getConnection from './mssqlManager.js';

export async function updateQvUser(dbName) {
  const conn = await getConnection();
  const updateSql = `
      UPDATE [${dbName}].[dbo].[QV_User]
      SET [Password] = 'xfr6KZGBnfLuIAJevlo4pn98vrU=', locked_out = 0, last_password_change = DATEADD(year, 50, getdate())
      WHERE [Password] IS NOT NULL
  `;
  conn.query(updateSql);
}

export async function setupUserRoleForQivs(users, dbName) {
  for (const user of users) {
    await updateUser(user, dbName);
  }
  const updatedUsers = await getUserIds(users, dbName);
  for (const user of updatedUsers) {
    await updateUserGroup(user, dbName);
  }
}

async function updateUser(user, dbName) {
  const records = await getQvUserByName(user, dbName);
  if (records.length === 0) {
    logger.info(`User not found: ${user.nt_username}, creating new user`);
    user.employeeId = await getNextEmployeeId(dbName);
    await insertEmployee(user, dbName);
    await insertUser(user, dbName);
  }
}

async function insertUser(user, dbName) {
  const name = user.nt_username.replace('\\', '-');
  const conn = await getConnection();

  const insertUserSql = `
      INSERT INTO [${dbName}].[dbo].[QV_User]
      ([QV_User], [Employee_id], [NT_SID], [Password], [locked_out], [Active], [last_password_change],
       [Cannot_Change_Password], [Must_Change_password])
      VALUES (@name, @employeeId, @sid, @password, 0, 1, DATEADD(year, 50, GETDATE()), 1, 0)
  `;

  await conn.request()
    .input('name', sql.VarChar, name)
    .input('employeeId', sql.Int, user.employeeId)
    .input('password', sql.VarChar, user.password)
    .input('sid', sql.VarChar, user.sid)
    .query(insertUserSql);
}

async function getNextEmployeeId(dbName) {
  const conn = await getConnection();
  await conn.request().query(`USE ${dbName}`);
  const result = await conn
    .request()
    .input('table_name', sql.VarChar, 'Employee')
    .output('id', sql.Int)
    .execute('spd_parameters_get_next_id');
  return result.output.id;
}

async function insertEmployee(user, dbName) {
  logger.info(`Inserting employee: ${user.name}`);
  const conn = await getConnection();

  const insertEmployeeSql = `
      INSERT INTO [${dbName}].[dbo].[Employee]
      ([Employee_Id],[QV_Office_Id], [Full_Name], [Employee_Group_Id], [Registered], [NT_User_Name], [Active], [Email_Address])
      VALUES (@employeeId,17, @fullName, 4, 1, @ntUsername, 1, @email)
  `;

  await conn.request()
    .input('employeeId', sql.Int, user.employeeId)
    .input('fullName', sql.VarChar, user.name)
    .input('ntUsername', sql.VarChar, user.samAccountName)
    .input('email', sql.VarChar, user.email)
    .query(insertEmployeeSql);
}

async function getUserIds(users, dbName) {
  return Promise.all(users.map(async (user) => {
    const userResult = await getQvUserByName(user, dbName);
    return {
      ...user,
      qvUserId: userResult[0]?.QV_User_ID,
    };
  }));
}

async function updateUserGroup(user, dbName) {
  const conn = await getConnection();
  const deleteUserGroupSql = `
      DELETE
      FROM [${dbName}].[dbo].[QV_User_Group]
      WHERE [QV_User_ID] = @userId
  `;

  await conn.request()
    .input('userId', sql.Int, user.qvUserId)
    .query(deleteUserGroupSql);
  await insertUserGroup(user, dbName);
}

async function insertUserGroup(user, dbName) {
  const conn = await getConnection();
  const insertUserGroupSql = `
      INSERT INTO [${dbName}].[dbo].[QV_User_Group] ([QV_User_ID], [QV_Group_ID])
      VALUES (@userId, @groupId)
  `;

  for (const groupId of user.qv_user_group_ids) {
    await conn.request()
      .input('userId', sql.Int, user.qvUserId)
      .input('groupId', sql.Int, groupId)
      .query(insertUserGroupSql);
  }
}

export async function getQvUserByName(user, dbName) {
  logger.info(`Getting user: ${user.nt_username}`);
  const name = user.nt_username.replace('\\', '-');
  const conn = await getConnection();

  const query = `
      SELECT *
      FROM [${dbName}].[dbo].[QV_User]
      WHERE [QV_User] = @name
  `;

  const results = await conn.request()
    .input('name', sql.VarChar, name)
    .query(query);
  return results.recordset;
}
