import { PROPERTY_SERVICE_DB_NAME, QVCONZ_DB_NAME } from 'common/util/const.js';
import runQuery from './pgManager.js';
import { setupUserRoleForPublicWebsite } from './website.js';
import { setupUserRoleForQivs } from './qvUser.js';
import { setupUserRoleForMonarch } from './userProfile.js';

const sanitiseFunctions = {
  [QVCONZ_DB_NAME]: sanitiseQvconz,
  [PROPERTY_SERVICE_DB_NAME]: sanitisePropertyService,
  qvnz: sanitiseQvnz,
};

export async function sanitiseDb(dataEnv, name, users) {
  logger.info('sanitiseDb', { dataEnv, name });
  const sanitiseFunction = sanitiseFunctions[name];
  if (sanitiseFunction) {
    await sanitiseFunction(dataEnv, name, users);
  } else {
    logger.warn('No sanitise function found for', name);
  }
}

export async function sanitiseQvconz(dataEnv, name, users) {
  logger.info('sanitiseQvconz', { dataEnv, name });
  const dbName = `${dataEnv}_${name}`;
  const wagtailAdminUserIdsSQL = 'SELECT DISTINCT ON (group_id) group_id, user_id as id FROM website.auth_user_groups ORDER BY group_id, user_id';
  const wagtailAdminUserIds = (await runQuery(wagtailAdminUserIdsSQL, dbName)).rows.map((row) => row.id);
  const adminUserId = wagtailAdminUserIds[0];

  const websiteUsersSql = 'select id from website.account_account where subscription_id is not null limit 1';
  const websiteUsersIds = (await runQuery(websiteUsersSql, dbName)).rows.map((row) => row.id);

  const costBuildUsersSql = 'select id from website.account_account where costbuilder_subscription_id is not null limit 1';
  const costBuildUsersIds = (await runQuery(costBuildUsersSql, dbName)).rows.map((row) => row.id);

  const testUserIds = [...wagtailAdminUserIds, ...websiteUsersIds, ...costBuildUsersIds];
  logger.info('testUserIds', { testUserIds });
  const hashedPassword = 'pbkdf2_sha256$260000$OsJQF9ZIoCReD8dnVyaetN$QXXdNOwdyC6U60a3UWVCrXulJfzhVCd+7HysJgrrlhU=';

  const sanitiseUsers = [
    `update website.wagtailcore_page set owner_id  = ${adminUserId} where owner_id  not in ( ${testUserIds.join(',')} )`,
    `update website.wagtailcore_revision set user_id = ${adminUserId} where user_id  not in ( ${testUserIds.join(',')} )`,
    `update website.wagtaildocs_document set uploaded_by_user_id = ${adminUserId} where uploaded_by_user_id  not in ( ${testUserIds.join(',')} )`,
    `update website.wagtailimages_image set uploaded_by_user_id = ${adminUserId} where uploaded_by_user_id not in ( ${testUserIds.join(',')} )`,
    `delete from website.wagtailcore_pagesubscription where user_id not in ( ${testUserIds.join(',')} )`,
    `delete from website.account_account where user_id not in ( ${testUserIds.join(',')} )`,
    `delete from website.auth_user_groups where user_id not in ( ${testUserIds.join(',')} )`,
    `delete from website.auth_user where id not in ( ${testUserIds.join(',')} )`,
    `update website.auth_user set password = '${hashedPassword}'`,
    'update website.costbuilder_costbuildersubscription set end_date = CURRENT_DATE + INTERVAL \'1 year\', start_date = CURRENT_DATE - INTERVAL \'1 year\',subscription_status = 2, enforce_single_session = false',
  ];

  const deleteTableDemomode = 'delete from website.costbuilder_costbuilderdemomode';

  const sanitiseSqls = [
    ...sanitiseUsers,
    deleteTableDemomode,
  ];
  for (const sql of sanitiseSqls) {
    await runQuery(sql, dbName);
  }
  const staffUsers = users.filter((user) => user.is_staff);
  await setupUserRoleForPublicWebsite(staffUsers, dbName);
}

async function sanitisePropertyService(dataEnv, name, users) {
  const dbName = `${dataEnv}_${name}`;
  await setupUserRoleForMonarch(users, dbName);
}

async function sanitiseQvnz(dataEnv, name, users) {
  const dbName = `${dataEnv}_${name}`;
  await setupUserRoleForQivs(users, dbName);
}
