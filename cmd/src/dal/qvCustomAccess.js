import { DEFAULT_QIVS_ENV, QIVS_EXTERNAL_DOMAINS } from 'common/util/const.js';
import getConnection from './mssqlManager.js';

export async function getQvCustomAccess(dbName, newEnv) {
  const conn = await getConnection();
  const querySql = `
      SELECT [rating_authority_id]
              , [region_id]
              , [url]
              , [logo]
      FROM [${dbName}].[dbo].[qv_custom_access]
  `;
  const results = await conn.query(querySql);
  const processedData = generateUrlForNewEnv(results.recordset, newEnv);
  logger.info(`Processed data: ${JSON.stringify(processedData)}`);
  return processedData;
}

export function generateUrlForNewEnv(recordset, newEnv) {
  const currentEnv = getCurrentDataEnv(recordset);
  return recordset.map((item) => {
    let name = '';
    let type = '';
    let newUrl = '';
    const matchedDomain = QIVS_EXTERNAL_DOMAINS.find((domain) => item.url.includes(domain));
    if (matchedDomain) {
      name = matchedDomain.includes('.external.quotablevalue.co.nz')
        ? item.url.split('.')[0]
        : item.url.split(`${currentEnv}`)[1].split(matchedDomain)[0];
      type = item.url.includes('.acuity.') ? 'acuity' : 'qivs';
      newUrl = `${name}.${newEnv}.${type}.external.quotablevalue.co.nz`;
    }
    return {
      ...item,
      name,
      type,
      newUrl,
    };
  });
}

function getCurrentDataEnv(results) {
  const f = results[0].url;
  if (!f.startsWith(DEFAULT_QIVS_ENV)) {
    return f.split('.')[1];
  }
  return DEFAULT_QIVS_ENV;
}

export async function updateQvCustomAccess(dbName, data) {
  const conn = await getConnection();
  data.forEach((item) => {
    let updateSql = '';
    if (item.rating_authority_id == null) {
      updateSql = `
          UPDATE [${dbName}].[dbo].[qv_custom_access]
          SET [url] = '${item.newUrl}'
          WHERE region_id = ${item.region_id}
      `;
    } else {
      updateSql = `
          UPDATE [${dbName}].[dbo].[qv_custom_access]
          SET [url] = '${item.newUrl}'
          WHERE rating_authority_id = ${item.rating_authority_id}
      `;
    }
    conn.query(updateSql);
  });
}
