import { PG_EXTENSIONS, PG_USER } from 'common/util/const.js';
import getConfig from 'common/config/getConfig.js';
import runQuery from './pgManager.js';

export function getTablespaceName(dbName) {
  return `${dbName}_tb`;
}

export async function createTb(tablespaceName, location) {
  const sqlQuery = `CREATE TABLESPACE ${tablespaceName} LOCATION '${location}';`;
  logger.info(`${sqlQuery}`);
  await runQuery(sqlQuery);

  return tablespaceName;
}

export async function createPgDb(dbName, tablespaceName) {
  const dbuser = await getConfig('PG_USERNAME');
  const sqlQuery = `
    CREATE DATABASE ${dbName}
    WITH OWNER = ${dbuser}
         ENCODING = 'UTF8'
         TABLESPACE = ${tablespaceName}
         CONNECTION LIMIT = -1;
    `;
  await runQuery(sqlQuery);
}

export async function createPgExtensions(dbName) {
  for (const extension of PG_EXTENSIONS) {
    const sqlQuery = `CREATE EXTENSION IF NOT EXISTS ${extension};`;
    await runQuery(sqlQuery, dbName);
  }
}

export async function createPgUser(username, password) {
  await runQuery(`CREATE USER ${username} WITH PASSWORD '${password}'`);
  logger.info(`User ${username} created`);
}

export async function grantPrivileges(database, username, role) {
  await runQuery(`GRANT ALL PRIVILEGES ON DATABASE ${database} TO ${username}`);

  await runQuery(`GRANT ${role} TO ${username}`);
  logger.info(`User ${username} granted ${role} role`);

  const schemas = await runQuery(`SELECT schema_name
                                  FROM information_schema.schemata
                                  WHERE schema_name NOT LIKE 'pg_%'
                                    AND schema_name != 'information_schema'`, database);
  for (const schema of schemas.rows) {
    logger.info(`Granting privileges to user ${username} for schema ${schema.schema_name}`);
    // for existing tables
    await runQuery(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ${schema.schema_name} TO ${username}`, database);
    await runQuery(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA ${schema.schema_name} TO ${username}`, database);
    await runQuery(`GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA ${schema.schema_name} TO ${username}`, database);
    await runQuery(`GRANT USAGE ON SCHEMA ${schema.schema_name} TO ${username}`, database);

    // for new tables
    await runQuery(`ALTER DEFAULT PRIVILEGES IN SCHEMA ${schema.schema_name} GRANT ALL PRIVILEGES ON TABLES TO ${username}`, database);
    await runQuery(`ALTER DEFAULT PRIVILEGES IN SCHEMA ${schema.schema_name} GRANT ALL PRIVILEGES ON SEQUENCES TO ${username}`, database);

    // Change the ownership of all schema and tables to the new user
    await runQuery(`ALTER SCHEMA ${schema.schema_name} OWNER TO ${username}`, database);
    await changeTableOwners(schema.schema_name, username, database);
    // Grant CREATE permission for each schema
    await runQuery(`GRANT CREATE ON SCHEMA ${schema.schema_name} TO ${username}`, database);
  }
  logger.info(`User ${username}created and privileges granted`);
}

export async function dropPgUserIfExists(env) {
  logger.info(`Dropping user ${env}_${PG_USER} if it exists`);
  await runQuery(`DROP USER IF EXISTS ${env}_${PG_USER}`);
}

async function changeTableOwners(schemaName, username, database) {
  const tablesResult = await runQuery(`SELECT table_name
                                       FROM information_schema.tables
                                       WHERE table_schema = '${schemaName}'`, database);

  const tables = tablesResult.rows;
  for (let i = 0; i < tables.length; i++) {
    const tableName = tables[i].table_name;
    logger.info(`Changing ownership of table ${tableName} to ${username}`);
    const result = await runQuery(`ALTER TABLE ${schemaName}.${tableName} OWNER TO ${username}`, database);
    logger.info(result);
  }
}
