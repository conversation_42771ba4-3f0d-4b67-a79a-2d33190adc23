import {
  MSSQL_USER_ROLE, QIVS_USER, QVNZ_REFRESH_JOB_NAME, STATIC_DB_BAK_FILE_LOCATION,
} from 'common/util/const.js';
import getConnection from './mssqlManager.js';
import { deleteLogin, isLoginExists } from './createMssqlObjects.js';

const RESERVED_USERS = ['dbo', 'guest', 'INFORMATION_SCHEMA', 'sys'];

const RESERVED_SCHEMAS = ['dbo', 'guest', 'INFORMATION_SCHEMA', 'MAPINFO', 'sys', 'db_accessadmin', 'db_backupoperator',
  'db_ddladmin', 'db_datareader', 'db_datawriter', 'db_denydatareader', 'db_denydatawriter', 'db_owner',
  'db_securityadmin', 'monarch', 'QVWMS', 'QVWMS3',
];

export async function destroyDb(dbName) {
  const conn = await getConnection();
  const query = `
      IF EXISTS (SELECT 1 FROM sys.databases WHERE name = '${dbName}')
      BEGIN
        ALTER DATABASE [${dbName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
        DROP DATABASE [${dbName}];
        SELECT '1' AS Result;
      END
      ELSE
      BEGIN
        SELECT '0' AS Result;
      END;
    `;
  const { recordset } = await conn.query(query);
  return recordset[0].Result;
}

export async function destroyAgentJob(env) {
  const conn = await getConnection();
  const query = `
        DECLARE @jobExists BIT = 0;
        IF EXISTS(SELECT 1
                  FROM msdb.dbo.sysjobs
                  WHERE name = N'${env} ${QVNZ_REFRESH_JOB_NAME}')
            BEGIN
                SET @jobExists = 1;
                EXEC msdb.dbo.sp_delete_job @job_name = N'${env} ${QVNZ_REFRESH_JOB_NAME}', @delete_unused_schedule = 1;
            END

        SELECT @jobExists AS JobExists;
    `;
  logger.info(query);
  const { recordset } = await conn.query(query);
  return recordset[0].Result;
}

export async function destroySchedule(env) {
  const conn = await getConnection();
  const query = `
        DELETE
        FROM msdb.dbo.sysschedules
        where name = '${env}';
    `;
  await conn.query(query);
}

export async function deleteMssqlLoginIfExist(env) {
  const users = [QIVS_USER, ...MSSQL_USER_ROLE.keys()];
  for (const item of users) {
    const loginName = `${env}_${item}`;
    if (await isLoginExists(loginName)) {
      await deleteLogin(loginName);
      logger.info(`delete login ${loginName}`);
    }
  }
}

export async function dropUserOnDb(dbName, username) {
  const conn = await getConnection();
  const sql = `
            USE ${dbName};
            DROP USER IF EXISTS [${username}];
        `;
  logger.info(sql);
  await conn.query(sql);
}

export async function dropUsers(dataEnv, key) {
  const dbName = `${dataEnv}_${key}`;

  const conn = await getConnection();
  const sql = `
          USE ${dbName};
          SELECT name
            FROM sys.database_principals
            WHERE (type_desc = 'SQL_USER' or type_desc = 'WINDOWS_USER' or type_desc = 'WINDOWS_GROUP')
            AND name NOT IN (${RESERVED_USERS.map((user) => `'${user}'`).join(',')})
            AND name NOT LIKE '${dataEnv}%';
        `;
  const { recordset } = await conn.query(sql);
  for (const user of recordset) {
    logger.info(`drop user ${user.name} on ${dbName}`);
    await dropUserOnDb(dbName, user.name);
  }
  return recordset;
}

export async function dropSchema(dbName, schemaName) {
  const conn = await getConnection();
  const sql = `
           USE ${dbName};
           DROP SCHEMA [${schemaName}];
        `;
  logger.info(sql);
  await conn.query(sql);
}

export async function dropSchemas(dataEnv, key) {
  const dbName = `${dataEnv}_${key}`;

  const conn = await getConnection();
  const sql = `
          USE ${dbName};
          SELECT schema_name FROM information_schema.schemata as s
          WHERE s.schema_name not in (${RESERVED_SCHEMAS.map((schema) => `'${schema}'`).join(',')});
        `;
  const { recordset } = await conn.query(sql);
  for (const user of recordset) {
    logger.info(`drop schema ${user.schema_name} on ${dbName}`);
    await dropSchema(dbName, user.schema_name);
  }
}

export function getUsersByDataEnv(dataEnv) {
  return Array.from(MSSQL_USER_ROLE.keys()).map((key) => `${dataEnv}_${key}`);
}

export async function dropUsersOnStaticDbs(dataEnv) {
  await dropUserOnDb('ESAM', `${dataEnv}_${QIVS_USER}`);
  await dropUserOnDb('qvnz_resources', `${dataEnv}_${QIVS_USER}`);
  await dropUserOnDb('msdb', `${dataEnv}_monarch_services`);

  const dbNames = STATIC_DB_BAK_FILE_LOCATION.keys();
  for (const dbName of dbNames) {
    const users = getUsersByDataEnv(dataEnv);
    logger.info(`drop users ${users} on ${dbName}`);
    for (const user of users) {
      await dropUserOnDb(dbName, `${user}`);
    }
  }
}
