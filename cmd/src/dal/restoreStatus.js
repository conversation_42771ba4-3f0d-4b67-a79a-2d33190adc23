import path from 'path';
import { MSSQL_DATA_PATH, MSSQL_INDEXES_PATH, MSSQL_LOG_PATH } from 'common/util/const.js';
import { sleep } from 'common/util/sleepUtil.js';
import getConnection from './mssqlManager.js';
import runQuery from './pgManager.js';

export async function getBakFileInfo(filePath) {
  const conn = await getConnection();
  const sqlQuery = `RESTORE FILELISTONLY FROM DISK = '${filePath}'`;
  const result = await conn.query(sqlQuery);
  return result.recordset;
}

export async function restoreFromBakFile(bakFileInfo, drive, env, key, value) {
  const conn = await getConnection();
  const move = bakFileInfo.map((item) => {
    const logicalName = item.LogicalName;
    const fileExtension = path.extname(item.PhysicalName);
    const physicalNameName = `${getFolder(item.FileGroupName ?? 'LOG', drive)}\\${logicalName}${fileExtension}`;
    logger.info(`MOVE '${logicalName}' TO '${physicalNameName}'`);
    return `MOVE '${logicalName}' TO '${physicalNameName}'`;
  }).join(',\n');
  const dbName = env ? `${env}_${key}` : `${key}`;
  const sql = `
    RESTORE DATABASE [${dbName}]
    FROM DISK = '${value}'
    WITH REPLACE,
    ${move},
    STATS = 5;
    `;
  return conn.query(sql);
}

export async function getRestoreStatus() {
  const conn = await getConnection();
  const querySql = `
        SELECT r.session_id,
               r.percent_complete,
               r.estimated_completion_time,
               r.total_elapsed_time,
               s.login_name,
               s.program_name
        FROM sys.dm_exec_requests r
                 INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
        WHERE r.command LIKE 'RESTORE DATABASE%'
          AND s.login_name = 'cicd_sql_user'
          AND s.program_name = 'node-mssql'

    `;
  return conn.query(querySql);
}

export async function getRestoreHistory() {
  const conn = await getConnection();
  const querySql = `
        SELECT TOP 1 rh.restore_date,rh.destination_database_name
        FROM msdb.dbo.restorehistory rh
        ORDER BY restore_date DESC;
    `;
  return conn.query(querySql);
}

function getFolder(fileGroup, drive) {
  if (fileGroup.toLowerCase() === 'log') {
    return `${drive}:${MSSQL_LOG_PATH}`;
  }
  if (fileGroup.toLowerCase() === 'data') {
    return `${drive}:${MSSQL_DATA_PATH}`;
  }
  if (fileGroup.toLowerCase() === 'indexes') {
    return `${drive}:${MSSQL_INDEXES_PATH}`;
  }
  return `${drive}:${MSSQL_DATA_PATH}`;
}
