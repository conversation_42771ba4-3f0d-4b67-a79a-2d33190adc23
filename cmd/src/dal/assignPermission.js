import { MSSQL_DB_ROLES, MSSQL_USER_ROLE } from 'common/util/const.js';
import { isDatabaseExists, isDatabaseRoleExist } from './createMssqlObjects.js';
import getConnection from './mssqlManager.js';

export async function setUpUserRole(env, dbName) {
  if (!await isDatabaseExists(dbName)) {
    logger.info(`database ${dbName} not exist`);
    return;
  }
  for (const [key, value] of MSSQL_USER_ROLE) {
    logger.info(`check role ${value} in ${dbName}`);
    const roles = [];
    if (await isDatabaseRoleExist(dbName, value)) {
      logger.info(`role ${value} exist in ${dbName}, add login to this db and assign the role`);
      roles.push(value);
    }
    if (value === 'monarch_services_role') {
      roles.push(...MSSQL_DB_ROLES);
    }
    if (value === 'qvapihub_user_role') {
      roles.push('db_datawriter');
    }
    if (dbName === 'qvnz_resources') {
      roles.push('mapping_service_role');
    }
    const loginName = `${env}_${key}`;
    await assignRole(dbName, roles, loginName);
  }
}

export async function assignRole(dbName, roles, username) {
  const conn = await getConnection();
  logger.info(`assign permission to ${username}`);
  const roleSql = roles.map((roleName) => `ALTER ROLE [${roleName}] ADD MEMBER [${username}];`).join('\n');
  const sql = `
            USE ${dbName};
            DROP USER IF EXISTS [${username}];
            CREATE USER [${username}];
            ${roleSql}
        `;
  logger.info(sql);
  await conn.query(sql);
}
