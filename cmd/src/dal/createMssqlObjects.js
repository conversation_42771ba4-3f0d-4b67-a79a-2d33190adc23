import fs from 'fs';
import {
  CICDDB01_IP,
  DB_PASSWORD_LENGTH,
  ERROR_CODES,
  MSSQL_USER_ROLE,
  QIVS_USER,
  RECREATE_SYNONYM_DB_NAMES,
} from 'common/util/const.js';
import sql from 'mssql';
import getConnection from './mssqlManager.js';
import { deleteMssqlLoginIfExist } from './destroyMssqlObjects.js';
import { generatePassword } from '../util/stringUtil.js';

export async function createDb(dbName, drive) {
  const conn = await getConnection();
  const query = `
CREATE DATABASE [${dbName}]
CONTAINMENT = NONE
ON  PRIMARY
( NAME = N'qvnz_public_Primary', FILENAME = N'${drive}:\\MSSQL\\DATA\\${dbName}_Primary.mdf' , SIZE = 156288KB , MAXSIZE = UNLIMITED, FILEGROWTH = 10%),
FILEGROUP [CODETABLES]
( NAME = N'qvnz_public_Codetables1', FILENAME = N'${drive}:\\MSSQL\\DATA\\${dbName}_Codetables1.ndf' , SIZE = 2212480KB , MAXSIZE = UNLIMITED, FILEGROWTH = 10%),
FILEGROUP [DATA]  DEFAULT
( NAME = N'qvnz_public_Data1', FILENAME = N'${drive}:\\MSSQL\\DATA\\${dbName}_Data1.ndf' , SIZE = 45742400KB , MAXSIZE = UNLIMITED, FILEGROWTH = 10%),
FILEGROUP [INDEXES]
( NAME = N'qvnz_public_Indexes1', FILENAME = N'${drive}:\\MSSQL\\INDEXES\\${dbName}_Indexes1.ndf' , SIZE = 9372352KB , MAXSIZE = UNLIMITED, FILEGROWTH = 10%)
LOG ON
( NAME = N'qvnz_public_Log', FILENAME = N'${drive}:\\MSSQL\\LOG\\${dbName}_Log.ldf' , SIZE = 1024KB , MAXSIZE = 2048GB , FILEGROWTH = 10%),
( NAME = N'qvnz_public_Log2', FILENAME = N'${drive}:\\MSSQL\\LOG\\${dbName}_Log2.ldf' , SIZE = 1024KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)
    `;
  return conn.query(query);
}

export async function importSqlScript(dbName, filePath) {
  const conn = await getConnection();
  const buffer = fs.readFileSync(filePath);
  await conn.query(`USE ${dbName};`);
  const text = buffer.toString();
  const lines = text.split('\n');
  const statements = [];
  let currentString = '';
  let isMultiLineComment = false;
  for (const line of lines) {
    const trimmedLine = line.trim();
    // Check for start and end of multi-line comment
    if (trimmedLine.includes('/*')) {
      isMultiLineComment = true;
    }
    if (trimmedLine.includes('*/')) {
      isMultiLineComment = false;
    }
    // Process the line
    if (!isMultiLineComment && trimmedLine.toUpperCase() === 'GO') {
      if (currentString !== '') {
        statements.push(`${currentString}\n`);
        currentString = '';
      }
    } else {
      currentString += `${line}\n`;
    }
  }

  if (currentString !== '') {
    statements.push(currentString.trim());
  }

  const rowsAffected = [];
  const errors = [];
  for (const statement of statements) {
    try {
      const result = await conn.query(`${statement}`);
      rowsAffected.push(result.rowsAffected);
    } catch (e) {
      logger.error(ERROR_CODES.SQL_ERROR.code, ERROR_CODES.SQL_ERROR.message, e);
      errors.push(e.message);
    }
  }
  return { filePath, rowsAffected, errors };
}

/**
 * The scripts to create agent jobs are not able to be parameterised.
 * The workaround is to append the variable @env to the script.
 */
export async function createJobs(env, filePath) {
  const conn = await getConnection();
  const buffer = fs.readFileSync(filePath);
  const script = buffer.toString();
  await conn.query(`
        DECLARE @env NVARCHAR(100) = '${env}';
        ${script}
    `);
}

export async function recreateSynonym(envName, it) {
  const dbName = `${envName}_${it}`;
  const querySql = `
        SELECT name, base_object_name
        FROM ${dbName}.sys.synonyms
    `;
  const conn = await getConnection();
  const result = await conn.query(querySql);
  const records = result.recordset;
  for (const record of records) {
    const baseObjectName = record.base_object_name;
    const { name } = record;
    const regex = /\[([^\]]+)\]/g;
    const objectNames = [];
    let match;
    while ((match = regex.exec(baseObjectName)) !== null) {
      objectNames.push(match[1]);
    }
    logger.info(`objectNames: ${objectNames}`);
    let originalDbName = '';
    let synonymName = '';
    if (objectNames.length === 4) {
      [, originalDbName, , synonymName] = objectNames;
    } else if (objectNames.length === 3) {
      [originalDbName, , synonymName] = objectNames;
    } else if (objectNames.length === 2) {
      [originalDbName, synonymName] = objectNames;
    } else {
      logger.info('not able to create synonymName');
    }
    if (originalDbName !== '' && RECREATE_SYNONYM_DB_NAMES.includes(originalDbName.toLowerCase())) {
      logger.info(`recreate synonym ${name}`);
      if (originalDbName === 'qvnz_prod') {
        originalDbName = 'qvnz_public';
      }
      const sqlQuery = `
         USE ${dbName};
         DROP SYNONYM [${name}]; 
         CREATE SYNONYM [${name}] FOR ${envName}_${originalDbName}.dbo.[${synonymName}]
        `;
      logger.info(sqlQuery);
      await conn.query(sqlQuery);
    }
  }
}

export async function isDatabaseExists(dbName) {
  const conn = await getConnection();
  const querySql = `
        SELECT name
        FROM sys.databases
        WHERE name = @dbName
    `;
  const result = await conn
    .request()
    .input('dbName', sql.NVarChar, dbName)
    .query(querySql);

  return result.recordset.length > 0;
}

export async function isDatabaseRoleExist(dbName, roleName) {
  const conn = await getConnection();
  const querySql = `
        SELECT name
        FROM ${dbName}.sys.database_principals
        WHERE name = @roleName
          AND type = 'R'
    `;
  const result = await conn
    .request()
    .input('roleName', sql.NVarChar, roleName)
    .query(querySql);

  return await result.recordset.length > 0;
}

export async function isLoginExists(username) {
  const conn = await getConnection();
  const querySql = `
        SELECT name
        FROM sys.server_principals
        WHERE name = '${username}'
          AND type = 'S'
          AND type_desc = 'SQL_LOGIN'
    `;
  const result = await conn
    .request()
    .query(querySql);
  return result.recordset.length > 0;
}

export async function createMssqlLogin(username, password) {
  const conn = await getConnection();
  const querySql = `
        CREATE LOGIN [${username}] WITH PASSWORD = '${password}'; 
        `;
  await conn.query(querySql);
}

export async function deleteLogin(username) {
  const conn = await getConnection();
  const querySql = `
            DROP LOGIN [${username}]; 
            `;
  await conn.query(querySql);
}

export async function createMssqlLogins(env) {
  let sqlConfig = {
    mssqlServer: CICDDB01_IP,
  };
  await deleteMssqlLoginIfExist(env);
  const users = [QIVS_USER, ...MSSQL_USER_ROLE.keys()];
  for (const item of users) {
    const loginName = `${env}_${item}`;
    const password = generatePassword(DB_PASSWORD_LENGTH);

    logger.info(`create login ${loginName}`);
    await createMssqlLogin(loginName, password);
    sqlConfig = {
      ...sqlConfig,
      [`${item}_username`]: loginName,
      [`${item}_password`]: password,
    };
  }
  return sqlConfig;
}

export async function setRecoverySimple(dataEnv, key) {
  const dbName = `${dataEnv}_${key}`;
  const conn = await getConnection(dbName);
  const query = `ALTER DATABASE [${dbName}] SET RECOVERY SIMPLE WITH NO_WAIT`;
  await conn.query(query);
}
