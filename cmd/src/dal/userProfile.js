import { v4 as uuidv4 } from 'uuid';
import runQuery from './pgManager.js';
import { pgPreparedInsert } from './pgCommon.js';

async function getUserId(user, dbName) {
  const sqlQuery = `SELECT *
                    FROM user_profile.user
                    WHERE nt_username = '${user.nt_username}'`;
  const result = await runQuery(sqlQuery, dbName);

  if (result.rows.length === 0) {
    const userId = uuidv4();
    logger.info(`Monarch User not found: ${user.nt_username}, creating new user ${userId}`);
    const insertQuery = pgPreparedInsert('user_profile.user', {
      id: userId,
      email: user.email,
      office_id: user.office_id,
      nt_username: user.nt_username,
      name: user.name,
    });

    await runQuery(insertQuery, dbName);

    return userId;
  }
  return result.rows[0].id;
}

async function getRoleIds(roles, dbName) {
  const sqlQuery = `SELECT *
                    FROM user_profile.role
                    WHERE role IN ('${roles.join("', '")}')`;
  const result = await runQuery(sqlQuery, dbName);
  return result.rows.map((row) => row.id);
}

async function assignUserRole(userId, roleId, dbName) {
  const sqlQuery = `SELECT *
                    FROM user_profile.user_role
                    WHERE user_id = '${userId}'
                      AND role_id = '${roleId}'`;
  const result = await runQuery(sqlQuery, dbName);

  if (result.rows.length === 0) {
    logger.info(`User role not found, creating new user  ${userId} role ${roleId}`);
    const insertQuery = pgPreparedInsert('user_profile.user_role', {
      user_id: userId,
      role_id: roleId,
    });

    await runQuery(insertQuery, dbName);
  }
}

export async function setupUserRoleForMonarch(users, dbName) {
  for (const user of users) {
    const userId = await getUserId(user, dbName);

    const roleIds = await getRoleIds(user.monarch_roles, dbName);
    logger.info(`Assigning roles to user: ${userId}`, { roleIds });
    for (const roleId of roleIds) {
      await assignUserRole(userId, roleId, dbName);
    }
  }
}
