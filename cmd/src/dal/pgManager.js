import pkg from 'pg';
import { ERROR_CODES } from 'common/util/const.js';
import getConfig from 'common/config/getConfig.js';

export default async function runQuery(sqlQuery, dbName = 'postgres') {
  const { Client } = pkg;
  const user = await getConfig('PG_USERNAME');
  const password = encodeURIComponent(await getConfig('PG_PASSWORD'));

  const server = await getConfig('PG_SERVER');
  const url = `postgresql://${user}:${password}@${server}/${dbName}`;
  const client = new Client({
    connectionString: url,
  });

  try {
    if (sqlQuery.text) {
      logger.info('Running prepared query', { sqlQuery });
    } else {
      logger.info(`Running query: ${sqlQuery}`);
    }
    await client.connect();
    logger.info('Connected to postgresql');
    return await client.query(sqlQuery);
  } catch (error) {
    logger.error(ERROR_CODES.PG_DB_ERROR.code, ERROR_CODES.PG_DB_ERROR.message, error);
    throw error;
  } finally {
    logger.info('Closing connection to postgresql');
    try {
      await endClientWithTimeout(client);
    } catch (e) {
      if (e.message === 'Client end timeout') {
        logger.info('ignore client end time out error, dont break the process');
      } else {
        throw e;
      }
    }
  }
}

function endClientWithTimeout(client, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const endClient = client.end();
    // Set a timeout to reject the promise if it takes too long
    const timer = setTimeout(() => {
      reject(new Error('Client end timeout'));
    }, timeout);
    endClient.then(() => {
      clearTimeout(timer);
      resolve();
    }).catch((err) => {
      clearTimeout(timer);
      reject(err);
    });
  });
}
