import { getQvUserDetails, getQvUsers } from 'common/util/cmdUtil.js';
import { parseQvUserDetail, parseQvUsers } from './stringUtil.js';

const STAFF_GROUP_ID = 3;
const STAFF_ROLES = ['Receptionist Typist', 'RTV Valuer', 'Reporting - Manager'];
const AUTH_GROUP = [
  'Moderators', 'Editors', 'User Management', 'Subscription Admin',
  'CostBuilder Subscription Admin', 'CostBuilder Customer Care',
];
const EXTERNAL_USERS_IN_AD = ['Contractor-Deloitte', 'Contractor-AWS'];

export async function getAppUsers() {
  const data = await getQvUsers();
  const qvUsers = parseQvUsers(data);
  const activeQvUsers = [];
  for (const qvUser of qvUsers) {
    const detail = parseQvUserDetail(await getQvUserDetails(qvUser.samAccountName));
    if (!EXTERNAL_USERS_IN_AD.includes(qvUser.name) && detail.enabled === 'True') {
      activeQvUsers.push(qvUser);
    }
  }

  logger.info(`Found ${activeQvUsers.length} active users`);
  activeQvUsers.forEach((user) => {
    const [firstName, lastName] = user.name.split(' ');
    user.email = `${firstName}.${lastName}@qv.co.nz`.toLowerCase();
    user.nt_username = `QVNZ\\${user.samAccountName}`;
    user.office_id = '01a60c90-1349-4f42-9ae9-a5f870c0b8bb';
    user.password = null;
    user.qv_user_group_ids = [STAFF_GROUP_ID];
    user.monarch_roles = STAFF_ROLES;
    user.auth_groups = AUTH_GROUP;
    user.is_staff = true;
  });
  const users = [
    ...activeQvUsers,
    {
      email: '<EMAIL>',
      name: 'Dev Email',
      nt_username: 'QVNZ\\dev.email',
      office_id: '01a60c90-1349-4f42-9ae9-a5f870c0b8bb',
      password: null,
      qv_user_group_ids: [STAFF_GROUP_ID],
      monarch_roles: STAFF_ROLES,
      auth_groups: AUTH_GROUP,
      is_staff: true,
    },
    {
      email: '',
      office_id: '01a60c90-1349-4f42-9ae9-a5f870c0b8bb',
      nt_username: 'QVNZ\\TestUserInt1',
      password: null,
      name: 'TestUserInt1',
      qv_user_group_ids: [13],
      monarch_roles: ['Area Valuer', 'Customer Care', 'Managing Senior Valuer', 'Rating Valuer', 'Receptionist Typist', 'Registered Valuer'],
      is_staff: false,
    },
    {
      email: '',
      office_id: '01a60c90-1349-4f42-9ae9-a5f870c0b8bb',
      nt_username: 'QVNZ\\TestUserInt2',
      password: null,
      name: 'TestUserInt2',
      qv_user_group_ids: [13],
      monarch_roles: ['Registered Valuer', 'Senior Valuer', 'Valuer'],
      is_staff: false,
    },
    {
      email: '',
      office_id: '01a60c90-1349-4f42-9ae9-a5f870c0b8bb',
      nt_username: 'QVNZ\\TestUserInt3',
      password: null,
      name: 'TestUserInt3',
      qv_user_group_ids: [13],
      monarch_roles: ['Customer Care'],
      is_staff: false,
    },
    {
      email: '',
      office_id: '01a60c90-1349-4f42-9ae9-a5f870c0b8bb',
      nt_username: 'TestUserExt1',
      password: 'xfr6KZGBnfLuIAJevlo4pn98vrU=',
      name: 'TestUserExt1',
      qv_user_group_ids: [9, 13],
      monarch_roles: STAFF_ROLES,
      is_staff: false,
    },
  ];
  return users;
}
