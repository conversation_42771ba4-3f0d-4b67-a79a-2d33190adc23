export function generateSqlReport(sqlResults) {
  const lines = [];
  let code = 200;
  sqlResults.forEach((item) => {
    if (item.errors.length === 0) {
      lines.push(`${item.filePath} executed successfully.`);
      item.rowsAffected.forEach((row) => {
        lines.push(` - Rows affected: ${row}`);
      });
    } else {
      code = 500;
      lines.push(`${item.filePath} executed with errors:`);
      item.errors.forEach((error) => {
        lines.push(` - ${error}`);
      });
    }
  });

  const report = `code:${code}\n${lines.join('\n')}`;
  return report;
}
