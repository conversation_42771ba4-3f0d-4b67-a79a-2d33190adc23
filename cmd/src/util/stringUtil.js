import { randomBytes } from 'crypto';

export function generatePassword(length) {
  let raw = randomBytes(length * 2).toString('base64');
  raw = raw.replace(/[^a-z0-9]/gi, '');
  return raw.slice(0, length);
}

export function parseConsoleResult(text) {
  const lines = text.trim().split('\n');
  // Extract headers from the first line
  const headers = lines[0].split(/\s+/);

  return lines.slice(1).map((line) => {
    const values = line.split(/\s+/);
    const obj = {};

    // This loop will assign values to their respective headers
    for (let i = 0, j = 0; i < headers.length && j < values.length; i++, j++) {
      obj[headers[i]] = values[j];
    }
    return obj;
  });
}

export function getAppNameFromPodName(str) {
  const lastDashIndex = str.lastIndexOf('-');
  return str.substring(0, lastDashIndex);
}

export function parseQvUsers(data) {
  const users = [];
  const blocks = data.trim().split(/\n\s*\n/);
  blocks.forEach((block) => {
    const nameMatch = block.match(/name\s+:\s+(.+)/);
    const sidMatch = block.match(/SID\s+:\s+(.+)/);
    const samAccountMatch = block.match(/SamAccountName\s+:\s+(.+)/);

    users.push({
      name: nameMatch[1].trim(),
      sid: sidMatch[1].trim(),
      samAccountName: samAccountMatch[1].trim(),
    });
  });
  return users;
}

export function parseQvUserDetail(data) {
  const user = {};
  const enabledMatch = data.match(/Enabled\s+:\s+(.+)/);
  if (enabledMatch) {
    user.enabled = enabledMatch[1].trim();
  }
  return user;
}
