import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';

import { AppEnvServiceError, DataEnvServiceError } from './errors.js';
import { handleAppEnvServiceError, handleDataEnvServiceError, handleUnknownError } from './errorHandler.js';

dotenv.config({ debug: true });

export default function wrapCmdHandler(handler) {
  global.logger = new Logger();
  return async (...handlerArgs) => {
    let response;
    try {
      response = await handler(...handlerArgs);
    } catch (e) {
      if (e instanceof DataEnvServiceError) {
        await handleDataEnvServiceError(e);
      } else if (e instanceof AppEnvServiceError) {
        await handleAppEnvServiceError(e);
      } else {
        await handleUnknownError(e, handlerArgs[0]);
      }
    }
    return response;
  };
}
