import { getTfScriptByName } from 'common/dal/tfScripts.js';
import fs from 'fs';
import { runCmdSync } from 'common/util/cmdUtil.js';
import { createFolders } from 'common/util/fileUtil.js';
import { sleep } from 'common/util/sleepUtil.js';
import { ACCOUNT_CREDENTIALS } from './awsConfig.js';

export async function writeTfScriptsToDisk(env) {
  const items = await getTfScriptByName(env);
  items.forEach((tfScript) => {
    const { Path: { S: path }, Text: { S: text } } = tfScript;
    logger.info(`writing text to ${path}`);
    createFolders(path);
    fs.writeFileSync(path, text);
  });
}

export async function tfApply(env, account) {
  const credentials = ACCOUNT_CREDENTIALS[account];
  const cmd = `cd tf/${env}/${account} && ${credentials} terraform init --reconfigure && ${credentials} terraform apply -auto-approve`;
  logger.info(cmd);
  runCmdSync(cmd);
}

/**
 * detaching the volume sometime take quite a long time, which causes errors, retry to fix,
 * the error is "unexpected state 'busy', wanted target 'detached'"
 */
export async function tfDestroy(env, account) {
  const credentials = ACCOUNT_CREDENTIALS[account];
  const MAX_RETRIES = 3;
  const RETRY_INTERVAL = 60 * 1000;
  const cmd = `cd tf/${env}/${account} && ${credentials} terraform init -reconfigure && ${credentials} terraform destroy -auto-approve`;
  logger.info(cmd);
  for (let i = 0; i < MAX_RETRIES; i++) {
    try {
      runCmdSync(cmd);
      break; // If command is successful, we break out of the loop
    } catch (error) {
      logger.error(`Attempt ${i + 1} failed with error: ${error.message}`);
      // If we've reached the maximum number of retries, throw the error
      if (i === MAX_RETRIES - 1) {
        throw error;
      } else {
        logger.info(`Retrying in ${RETRY_INTERVAL / 1000} seconds...`);
        await sleep(RETRY_INTERVAL);
      }
    }
  }
}
