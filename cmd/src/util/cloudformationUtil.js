import { DeleteStackCommand, DescribeStacksCommand } from '@aws-sdk/client-cloudformation';
import { cloudformationClient } from './awsConfig.js';

export async function getStackStatus(stackName) {
  const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
  const response = await cloudformationClient.send(describeStacksCommand);
  return response.Stacks[0].StackStatus;
}

export async function deleteCloudFormationStack(stackName) {
  const deleteStackCommand = new DeleteStackCommand({ StackName: stackName });
  const response = await cloudformationClient.send(deleteStackCommand);
  return response;
}

export async function stackExist(stackName) {
  try {
    const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
    await cloudformationClient.send(describeStacksCommand);
    return true;
  } catch (error) {
    if (error.name === 'ValidationError') {
      return false;
    }
    throw error;
  }
}
