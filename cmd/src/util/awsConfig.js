import { SSMClient } from '@aws-sdk/client-ssm';
import { REGION } from 'common/util/const.js';
import { S3Client } from '@aws-sdk/client-s3';
import getConfig from 'common/config/getConfig.js';
import { CloudFormationClient } from '@aws-sdk/client-cloudformation';
import { EC2Client } from '@aws-sdk/client-ec2';
import { BatchClient } from '@aws-sdk/client-batch';
import { CloudWatchLogsClient } from '@aws-sdk/client-cloudwatch-logs';

export const DEV_AWS_ACCESS_KEY_ID = await getConfig('DEV_AWS_ACCESS_KEY_ID');
export const DEV_AWS_SECRET_ACCESS_KEY = await getConfig('DEV_AWS_SECRET_ACCESS_KEY');
export const DEV_AWS_CREDENTIALS = `AWS_ACCESS_KEY_ID=${DEV_AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${DEV_AWS_SECRET_ACCESS_KEY}`;

export async function sigV4(service) {
  return `--aws-sigv4 "${service}" --user "${await getConfig('DEV_AWS_ACCESS_KEY_ID')}:${await getConfig('DEV_AWS_SECRET_ACCESS_KEY')}"`;
}

export const DEV_AWS_JSON_CREDENTIALS = {
  accessKeyId: `${await getConfig('DEV_AWS_ACCESS_KEY_ID')}`,
  secretAccessKey: `${await getConfig('DEV_AWS_SECRET_ACCESS_KEY')}`,
};

export const devSsmClient = new SSMClient({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});
export const cicdSsmClient = new SSMClient({
  region: REGION,
});

export const cicdS3Client = new S3Client({
  region: REGION,
});

export const devS3Client = new S3Client({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});

export const ACCOUNT_CREDENTIALS = {
  dev: DEV_AWS_CREDENTIALS,
  cicd: '', // default account, no need to specify credentials
};

export const TF_S3_BUCKETS = {
  dev: 'qv-dynamic-environments-terraform',
  cicd: 'qv-terraform',
};

export const cloudformationClient = new CloudFormationClient({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});

export const devEc2Client = new EC2Client({
  region: REGION,
  credentials: DEV_AWS_JSON_CREDENTIALS,
});

export const devBatchClient = new BatchClient(
  {
    region: REGION,
    credentials: DEV_AWS_JSON_CREDENTIALS,
  },
);

export const cloudWatchLogsClient = new CloudWatchLogsClient({
  region: REGION,
});

export const devCloudWatchLogsClient = new CloudWatchLogsClient(
  {
    region: REGION,
    credentials: DEV_AWS_JSON_CREDENTIALS,
  },
);
