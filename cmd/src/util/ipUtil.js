import { runCmdSync } from 'common/util/cmdUtil.js';
import ip from 'ip';
import { DEV_AWS_CREDENTIALS } from './awsConfig.js';

export function getNextAvailableIp(startIp) {
  let nextIp = startIp;

  const maxIp = getMaxPrivateIPInSubnet(startIp);
  while (true) {
    if (ip.toLong(maxIp) < ip.toLong(nextIp)) {
      throw new Error('No more available IP');
    }
    const checkIpCmd = `${DEV_AWS_CREDENTIALS} aws ec2 describe-network-interfaces --filters Name=private-ip-address,Values=${nextIp}`;
    const networkInterfaces = JSON.parse(runCmdSync(checkIpCmd)).NetworkInterfaces;
    if (networkInterfaces.length === 0) {
      break;
    } else {
      logger.info(`IP ${nextIp} is in use, checking the next one`);
      nextIp = getNextIP(nextIp);
    }
  }
  return nextIp;
}

export function getNextIP(currentIP) {
  const parsedIP = ip.toLong(currentIP);
  return ip.fromLong(parsedIP + 1);
}

export function getMaxPrivateIPInSubnet(privateIP) {
  const parts = privateIP.split('.');
  if (parts.length === 4) {
    return `${parts[0]}.255.255.255`;
  }
  throw new Error('Invalid IPv4 address');
}
