import getConfig from 'common/config/getConfig.js';

export async function sendSlack(username, text, iconEmoji) {
  const { URL: url, CHANNEL: channel } = await getConfig('SLACK');
  const payload = {
    channel,
    username,
    text,
    icon_emoji: iconEmoji,
  };
  logger.info('Sending Slack message', payload);
  try {
    logger.info('Slack message sent successfully');
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      logger.info(`Failed to send Slack message: ${response.statusText}`);
    }
  } catch (error) {
    logger.error('', 'Error sending Slack message:', error);
  }
}
