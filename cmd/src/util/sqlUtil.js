import { DB_SCRIPTS_FOLDER, DB_SCRIPTS_ORDER } from 'common/util/const.js';
import fs from 'fs';
import { listFiles } from 'common/util/fileUtil.js';
import path from 'path';
import { createJobs, importSqlScript } from '../dal/createMssqlObjects.js';

export async function executeSqlScripts(env, db) {
  logger.info(`executeSqlScripts ${env} ${db}`);
  const dbName = `${env}_${db}`;
  const sqlScriptFolder = `${DB_SCRIPTS_FOLDER}/${db}`;
  if (!fs.existsSync(sqlScriptFolder)) {
    logger.info(`The folder ${sqlScriptFolder} does not exist.`);
    return [];
  }
  const result1 = await scriptsInOrderedFolder(sqlScriptFolder, dbName);
  const result2 = await otherScripts(sqlScriptFolder, dbName, env);
  return [...result1, ...result2];
}

async function scriptsInOrderedFolder(sqlScriptFolder, dbName) {
  logger.info('import scripts');
  const scriptFiles = await listFiles(
    sqlScriptFolder,
    (dir, filename) => {
      const folderName = path.basename(dir);
      return (DB_SCRIPTS_ORDER.includes(folderName) && filename.endsWith('.sql'));
    },
  );
  scriptFiles.sort((a, b) => {
    const folderNameA = getFolderName(a);
    const folderNameB = getFolderName(b);
    return DB_SCRIPTS_ORDER.indexOf(folderNameA) - DB_SCRIPTS_ORDER.indexOf(folderNameB);
  });

  const sqlResults = [];
  for (const file of scriptFiles) {
    logger.info(`importing ${file}`);
    sqlResults.push(await importSqlScript(dbName, `${file}`));
  }
  return sqlResults;
}

async function otherScripts(sqlScriptFolder, dbName, env) {
  logger.info('import otherScripts');
  const otherScriptFiles = await listFiles(
    sqlScriptFolder,
    (dir, filename) => {
      const folderName = path.basename(dir);
      return (!DB_SCRIPTS_ORDER.includes(folderName) && filename.endsWith('.sql'));
    },
  );
  otherScriptFiles.sort((a, b) => path.basename(a).localeCompare(path.basename(b)));
  const sqlResults = [];
  for (const file of otherScriptFiles) {
    logger.info(`importing ${file}`);
    if (file.includes('agent-jobs')) {
      await createJobs(`${env}`, `${file}`);
    } else {
      sqlResults.push(await importSqlScript(dbName, `${file}`));
    }
  }
  return sqlResults;
}

function getFolderName(a) {
  const directoryPath = path.dirname(a);
  return path.basename(directoryPath);
}
