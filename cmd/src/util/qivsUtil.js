import { runCmdSync } from 'common/util/cmdUtil.js';
import { DEV_AWS_CREDENTIALS } from './awsConfig.js';

export function networkName(envName) {
  const interfaceName = `${envName} QIVS Second Network Interface`;
  return interfaceName;
}

export function getQivsSecondNetwork(envName) {
  const interfaceName = networkName(envName);
  const getNetworkCmd = `${DEV_AWS_CREDENTIALS}  aws ec2 describe-network-interfaces --filters "Name=description,Values=${interfaceName}"`;
  const networkInterfaces = JSON.parse(runCmdSync(getNetworkCmd)).NetworkInterfaces;
  logger.info('networkInterfaces', { networkInterfaces });
  if (networkInterfaces.length === 0) {
    return null;
  }
  return networkInterfaces[0];
}
