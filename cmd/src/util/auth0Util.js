import getConfig from 'common/config/getConfig.js';

export async function getPropertyServiceClientId(accountName) {
  return getConfig(`${accountName.toUpperCase()}_PROPERTY_SERVICE_CLIENT_ID`);
}

export async function getAuthApiUrl(accountName) {
  return getConfig(`${accountName.toUpperCase()}_AUTH_API_BASE_URL`);
}

export async function getAuthConfig(accountName) {
  return {
    client_id: `${await getConfig(`${accountName.toUpperCase()}_AUTH_API_CLIENT_ID`)}`,
    client_secret: `${await getConfig(`${accountName.toUpperCase()}_AUTH_API_CLIENT_SECRET`)}`,
    audience: `${await getConfig(`${accountName.toUpperCase()}_AUTH_API_AUDIENCE`)}`,
    grant_type: 'client_credentials',
  };
}

export async function getAuth0Token(accountName) {
  const url = `${await getAuthApiUrl(accountName)}/oauth/token`;
  const bodyData = new URLSearchParams(await getAuthConfig(accountName));
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: bodyData,
  });

  if (!response.ok) {
    throw new Error(`Failed with status: ${response.statusText}`);
  }
  return response.json();
}

export async function createAuth0Config(token, clientId, appEnv, accountName) {
  const existingData = await getClientInfo(clientId, token, accountName);
  logger.info(existingData);
  const { callbacks, allowed_logout_urls: allowedLogoutUrls, web_origins: webOrigins } = existingData;
  const baseUrl = `https://${appEnv}.monarch.internal.quotablevalue.co.nz`;
  addUrl(webOrigins, baseUrl);
  addUrl(callbacks, `${baseUrl}/callback`);
  addUrl(allowedLogoutUrls, `${baseUrl}/authorize`);
  await updateClient(clientId, token, { callbacks, allowed_logout_urls: allowedLogoutUrls, web_origins: webOrigins }, accountName);
}

export async function deleteAuth0Config(token, clientId, appEnv, accountName) {
  const existingData = await getClientInfo(clientId, token, accountName);
  const { callbacks, allowed_logout_urls: allowedLogoutUrls, web_origins: webOrigins } = existingData;
  const baseUrl = `https://${appEnv}.monarch.internal.quotablevalue.co.nz`;
  deleteUrl(webOrigins, baseUrl);
  deleteUrl(callbacks, `${baseUrl}/callback`);
  deleteUrl(allowedLogoutUrls, `${baseUrl}/authorize`);
  await updateClient(clientId, token, { callbacks, allowed_logout_urls: allowedLogoutUrls, web_origins: webOrigins }, accountName);
}

export async function getClientInfo(client, token, accountName) {
  const url = `${await getAuthApiUrl(accountName)}/api/v2/clients/${client}`;
  const headers = {
    Accept: 'application/json',
    Authorization: `Bearer ${token}`,
  };

  const response = await fetch(url, {
    method: 'GET',
    headers,
  });
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  const data = await response.json();
  return data;
}

export async function updateClient(clientId, token, data, accountName) {
  const baseUrl = `${await getAuthApiUrl(accountName)}`;
  const url = `${baseUrl}/api/v2/clients/${clientId}`;
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    Authorization: `Bearer ${token}`,
  };

  const response = await fetch(url, {
    method: 'PATCH',
    headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const responseBody = await response.json();
    throw new Error(`HTTP Error: ${response.statusText}\nResponse Body: ${JSON.stringify(responseBody)}`);
  }
  const responseData = await response.json();
  logger.info(responseData);
}

function addUrl(urls, url) {
  if (!urls.includes(url)) {
    urls.push(url);
  }
}

function deleteUrl(urls, url) {
  const index = urls.indexOf(url);
  if (index !== -1) {
    urls.splice(index, 1);
  }
}
