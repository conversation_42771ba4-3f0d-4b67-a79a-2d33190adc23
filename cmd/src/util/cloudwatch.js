import { DeleteLogGroupCommand, DescribeLogGroupsCommand } from '@aws-sdk/client-cloudwatch-logs';

async function deleteCloudWatchLogGroup(client, logGroupName) {
  try {
    logger.info(`Deleting log group: ${logGroupName}`);

    const deleteCommand = new DeleteLogGroupCommand({
      logGroupName,
    });

    const response = await client.send(deleteCommand);
    logger.info(`Successfully deleted log group: ${logGroupName}`);
    return response;
  } catch (error) {
    logger.error('ERR-CMD-124', `Error deleting log group ${logGroupName}:`, error);
    throw error;
  }
}

export async function deleteCloudWatchLogGroups(client, suffixArr) {
  try {
    let nextToken;
    let deletedCount = 0;
    do {
      const describeCommand = new DescribeLogGroupsCommand({
        nextToken,
      });
      const response = await client.send(describeCommand);
      nextToken = response.nextToken;
      const matchingLogGroups = (response.logGroups || [])
        .filter((group) => suffixArr.some((suffix) => group.logGroupName.endsWith(suffix)));
      for (const group of matchingLogGroups) {
        await deleteCloudWatchLogGroup(client, group.logGroupName);
        deletedCount++;
      }
    } while (nextToken);
    logger.info(`Successfully deleted ${deletedCount} log groups ending with "${suffixArr}"`);
  } catch (error) {
    logger.error('ERR-CMD-125', 'Error in deletion process:', error);
    throw error;
  }
}
