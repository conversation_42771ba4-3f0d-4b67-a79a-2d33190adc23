import { ERROR } from 'common/enums/environmentStatus.js';
import { ERROR_CODES } from 'common/util/const.js';
import {
  getDataEnvironmentByName,
  updateDataEnvironmentStatus,
  updateEnvironmentStatusByName,
} from 'common/dal/dataEnvironments.js';
import { getDataStoresByEnv, updateDatastoreToError } from 'common/dal/datastores.js';
import { getAppEnvironmentByName, updateAppEnvironmentStatus } from 'common/dal/appEnvironments.js';
import { getDeploymentByDeploymentId, updateDeploymentStatus } from 'common/dal/deployments.js';
import { DeploymentStatus } from 'common/enums/deploymentStatus.js';
import { setEnvironmentStatusByDataStoreStatus } from '../service/dataEnvironment.js';

/**
 * Handle any app env related service error, update the app environment status
 * to prevent remaining in progress status forever
 */
export async function handleAppEnvServiceError(e) {
  logger.info(`Custom error: ${e.message}, appEnv: ${e.appEnv}, code: ${e.code}`);
  const queryResults = await getAppEnvironmentByName(e.appEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_APP_ENV_FOUND.code, ERROR_CODES.NO_APP_ENV_FOUND.message, e.appEnv);
    return;
  }
  const appEnvironment = queryResults[0];
  logger.info('handleAppEnvServiceError app env found: ', appEnvironment);
  await updateAppEnvironmentToErrorStatus(appEnvironment.Name.S);
}

/**
 * Handle any data env related service error, update the data environment status and data store status
 * to prevent remaining in progress status forever
 */
export async function handleDataEnvServiceError(e) {
  logger.info(`Custom error: ${e.message}, dataEnv: ${e.dataEnv}, code: ${e.code}, ds: ${e.datastore}`);
  const queryResults = await getDataEnvironmentByName(e.dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, e.dataEnv);
    return;
  }
  const dataEnvironment = queryResults[0];
  logger.info('handleDataEnvServiceError data env found: ', dataEnvironment);
  const datastores = await getDataStoresByEnv(dataEnvironment.Name.S);
  datastores.forEach((dataStore) => {
    if (dataStore.Name.S === e.datastore) {
      const dsUniqueKey = `${e.dataEnv}-${dataStore.Type.S}-${e.datastore}`;
      updateDatastoreToError(dsUniqueKey, e.message);
    }
  });
  await setEnvironmentStatusByDataStoreStatus(dataEnvironment);
  await updateEnvironmentStatusByName(e.dataEnv, dataEnvironment.Status.S, dataEnvironment.EndTime.N);
}

/**
 * Handle command invalid options error, update the data environment status
 * to prevent from remaining in progress status forever
 */
async function updateDataEnvironmentToErrorStatus(env) {
  const queryResults = await getDataEnvironmentByName(env);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, env);
    return;
  }
  const dataEnvironment = queryResults[0];
  logger.info('updateDataEnvironmentToErrorStatus,data env found: ', dataEnvironment);
  await updateDataEnvironmentStatus(dataEnvironment.Name.S, ERROR);
}

/**
 * Handle any unknown error, update the data environment status to prevent from remaining in progress status forever
 */
export async function handleUnknownError(e, options) {
  logger.info(`Unknown error, options ${JSON.stringify(options)}`);
  logger.error(ERROR_CODES.UNKNOWN_ERROR.code, ERROR_CODES.UNKNOWN_ERROR.message, e);
  if (options.dataEnv) {
    await updateDataEnvironmentToErrorStatus(options.dataEnv);
  }
  if (options.appEnv) {
    await updateAppEnvironmentToErrorStatus(options.appEnv);
  }
  if (options.deploymentId) {
    await updateDeploymentToErrorStatus(options.deploymentId);
  }
}

/**
 * Handle any unknown error, update the data/app environment status to prevent from remaining in progress status forever
 */
export async function handleInvalidOptionsError(options) {
  logger.error(ERROR_CODES.INVALID_OPTIONS.code, ERROR_CODES.INVALID_OPTIONS.message, options);
  if (options.dataEnv) {
    await updateDataEnvironmentToErrorStatus(options.dataEnv);
  }
  if (options.appEnv) {
    await updateAppEnvironmentToErrorStatus(options.appEnv);
  }
}

export async function updateAppEnvironmentToErrorStatus(env) {
  const queryResults = await getAppEnvironmentByName(env);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_APP_ENV_FOUND.code, ERROR_CODES.NO_APP_ENV_FOUND.message, env);
    return;
  }
  const appEnvironment = queryResults[0];
  await updateAppEnvironmentStatus(appEnvironment.Name.S, ERROR);
}

export async function updateDeploymentToErrorStatus(deploymentId) {
  const deployment = await getDeploymentByDeploymentId(deploymentId);
  if (deployment === null) {
    logger.error(ERROR_CODES.NO_DEPLOYMENT_FOUND.code, ERROR_CODES.NO_DEPLOYMENT_FOUND.message, deploymentId);
    return;
  }
  await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
}
