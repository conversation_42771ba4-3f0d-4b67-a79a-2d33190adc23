import { ERROR, STABLE } from 'common/enums/environmentStatus.js';
import { getDataStoresByEnv } from 'common/dal/datastores.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import { ERROR_CODES } from 'common/util/const.js';
import { DataEnvServiceError } from './errors.js';

export default function waitQvnzRestoreReady(dataEnv) {
  return new Promise((resolve, reject) => {
    const interval = setInterval(async () => {
      const datastores = (await getDataStoresByEnv(dataEnv)).filter((ds) => ds.Type.S === dataStoreTypes.QIVS);
      logger.info('datastores', { datastores });
      const allStable = datastores.every((ds) => ds.Status.S === STABLE);
      const anyError = datastores.some((ds) => ds.Status.S === ERROR);
      logger.info(`QVNZ restore status: ${allStable}`);
      logger.info(`QVNZ restore error: ${anyError}`);
      if (allStable === true) {
        clearInterval(interval);
        resolve();
      }
      if (anyError === true) {
        clearInterval(interval);
        const error = new DataEnvServiceError(dataEnv, 'property_service', ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.message, ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.code);
        reject(error);
      }
    }, 60 * 1000);
  });
}
