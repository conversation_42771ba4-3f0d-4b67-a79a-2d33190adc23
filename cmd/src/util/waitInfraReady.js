import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { ERROR, PROVISIONING } from 'common/enums/environmentStatus.js';

export default function waitInfraReady(env) {
  return new Promise((resolve, reject) => {
    const interval = setInterval(async () => {
      const queryResults = await getDataEnvironmentByName(env);
      const status = queryResults[0].Status.S;
      logger.info(`Infrastructure status: ${status}`);
      if (status === PROVISIONING) {
        clearInterval(interval);
        resolve();
      }
      if (status === ERROR) {
        clearInterval(interval);
        reject(new Error('Infrastructure error occurred.'));
      }
    }, 30 * 1000);
  });
}
