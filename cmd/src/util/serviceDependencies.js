export function getDependencies(serviceDependencies) {
  const services = serviceDependencies;
  const order = [];
  const independentServices = [];
  const dependencyCount = {};
  const serviceGroups = {};

  // Initialize dependency count and dependency set for each service
  Object.keys(services).forEach((service) => {
    const { dependsOn } = services[service];
    dependencyCount[service] = dependsOn.length;
    serviceGroups[service] = new Set(dependsOn);

    if (dependsOn.length === 0 && service !== 'monarch-web') {
      independentServices.push(service);
    }
  });

  // Process independent services
  while (independentServices.length > 0) {
    const currentService = independentServices.shift();
    order.push([currentService]);

    // Update dependency count for services depending on the current one
    Object.keys(services).forEach((service) => {
      if (serviceGroups[service].has(currentService)) {
        serviceGroups[service].delete(currentService);
        if (serviceGroups[service].size === 0 && service !== 'monarch-web') {
          independentServices.push(service);
        }
      }
    });
  }

  // Check for circular dependencies
  const circularDependencies = Object.keys(serviceGroups).filter(
    (service) => serviceGroups[service].size > 0 && service !== 'monarch-web',
  );

  // Push circular dependencies to the order as a single group
  if (circularDependencies.length > 0) {
    order.push(circularDependencies);
  }

  // Always deploy 'monarch-web' as the last service
  if (dependencyCount['monarch-web'] >= 0) {
    order.push(['monarch-web']);
  }

  return order;
}
