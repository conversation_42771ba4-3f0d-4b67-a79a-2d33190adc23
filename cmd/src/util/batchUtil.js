import { DescribeJobsCommand } from '@aws-sdk/client-batch';
import { retryWithDelay } from 'common/util/retryUtil.js';
import { devBatchClient } from './awsConfig.js';

const endStatuses = ['SUCCEEDED', 'FAILED'];

function jobsEnded(jobs) {
  return jobs.every((job) => endStatuses.includes(job.status));
}

export async function fetchJobs(jobs) {
  const jobIds = jobs.map((job) => job.jobId);
  return checkJobStatus(jobIds);
}

export async function waitReindexingJobsCompleted(jobs, delaySeconds = 600) {
  logger.info('Waiting for reindexing jobs to complete', { jobs });
  const timeoutSeconds = 24 * 60 * 60;
  const { timeoutReached } = await retryWithDelay(() => fetchJobs(jobs), jobsEnded, timeoutSeconds, delaySeconds);
  return timeoutReached;
}

export async function checkJobStatus(jobIds) {
  try {
    const command = new DescribeJobsCommand({ jobs: jobIds });
    const response = await devBatchClient.send(command);
    return response.jobs || [];
  } catch (error) {
    logger.error('CMD-ERR-065', 'Error fetching job status:', error);
    return [];
  }
}
