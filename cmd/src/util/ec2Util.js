import { runCmdSync } from 'common/util/cmdUtil.js';
import { normalizeName } from 'common/util/appNameUtli.js';
import { CreateTagsCommand, DeleteTagsCommand, DescribeInstancesCommand } from '@aws-sdk/client-ec2';
import { QIVS_EX, QIVS_IN } from 'common/util/const.js';
import { DEV_AWS_CREDENTIALS, devEc2Client } from './awsConfig.js';

const SLEEP_TAG_KEY = 'skeddly:sleep';
const SLEEP_TAG_VALUE = 'app';
const SLEEP_TAG = {
  Key: SLEEP_TAG_KEY,
  Value: SLEEP_TAG_VALUE,
};

export function getK8sIp(env) {
  const instanceName = `Monarch-${normalizeName(env)} Kubernetes`;
  const cmd = getEc2PrivateIpCmd(instanceName);
  const k8sIp = runCmdSync(cmd).trim();
  if (k8sIp.length === 0) {
    throw new Error('k8s ip is empty');
  }
  return k8sIp;
}

export function getKafkaIp(env) {
  const instanceName = `Monarch-${normalizeName(env)} Kafka`;
  const cmd = getEc2PrivateIpCmd(instanceName);
  const kafkaIp = runCmdSync(cmd).trim();
  if (kafkaIp.length === 0) {
    throw new Error('kafka ip is empty');
  }
  logger.info(`kafka ip: ${kafkaIp}`);
  return kafkaIp;
}

export function getEc2Id(ec2Name) {
  const cmd = `${DEV_AWS_CREDENTIALS} aws ec2 describe-instances --filters "Name=tag:Name,Values=${ec2Name}" "Name=instance-state-name,Values=running" --query "Reservations[*].Instances[*].InstanceId" --output text`;
  return runCmdSync(cmd).replace(/\n/g, '');
}

export function getQivsInternalEc2Id(appEnv) {
  return getEc2Id(`${normalizeName(appEnv).toUpperCase()}${QIVS_IN}`);
}

export function getQivsExternalEc2Id(appEnv) {
  return getEc2Id(`${normalizeName(appEnv).toUpperCase()}${QIVS_EX}`);
}
export function getQivsInternalIp(env) {
  const instanceName = `${normalizeName(env).toUpperCase()}${QIVS_IN}`;
  const cmd = getEc2PrivateIpCmd(instanceName);
  const qivsIp = runCmdSync(cmd).trim();
  if (qivsIp.length === 0) {
    return '';
  }
  logger.info(`qivs internal ip: ${qivsIp}`);
  return qivsIp;
}

export function getQivsExternalIp(env) {
  const instanceName = `${normalizeName(env).toUpperCase()}${QIVS_EX}`;
  const cmd = getEc2PrivateIpCmd(instanceName);
  const qivsIp = runCmdSync(cmd).trim();
  if (qivsIp.length === 0) {
    return '';
  }
  logger.info(`qivs external ip: ${qivsIp}`);
  return qivsIp;
}

export function getEc2IpByName(instanceName) {
  const cmd = getEc2PrivateIpCmd(instanceName);
  const qivsIp = runCmdSync(cmd).trim();
  if (qivsIp.length === 0) {
    return '';
  }
  logger.info(`qivs external ip: ${qivsIp}`);
  return qivsIp;
}
export function getEc2PrivateIpCmd(instanceName) {
  return `${DEV_AWS_CREDENTIALS} aws ec2 describe-instances --filters "Name=tag:Name,Values=${normalizeName(instanceName)}" "Name=instance-state-name,Values=running" --query "Reservations[].Instances[].PrivateIpAddress" --output text`;
}

export async function getInstanceIdByTag(tagKey, tagValue) {
  const describeInstancesCommand = new DescribeInstancesCommand({
    Filters: [
      {
        Name: `tag:${tagKey}`,
        Values: [tagValue],
      },
    ],
  });
  const response = await devEc2Client.send(describeInstancesCommand);

  const instances = response.Reservations.flatMap((reservation) => reservation.Instances);

  if (instances.length === 0) {
    logger.info(`No instances found with tag ${tagKey}: ${tagValue}`);
    return null;
  }
  return instances[0].InstanceId;
}

export async function addSleepTag(instanceId) {
  logger.info('Adding sleep tag to instance', { instanceId });

  try {
    const describeInstancesCommand = new DescribeInstancesCommand({
      InstanceIds: [instanceId],
    });
    const response = await devEc2Client.send(describeInstancesCommand);
    const tags = response.Reservations.flatMap((reservation) => reservation.Instances.flatMap((instance) => instance.Tags || []));
    tags.push(SLEEP_TAG);
    const command = new CreateTagsCommand({
      Resources: [instanceId],
      Tags: tags,
    });

    await devEc2Client.send(command);
  } catch (error) {
    logger.error('ERR-CICD-CMD-044', 'Error adding sleep tag:', error);
  }
}

export async function removeSleepTag(instanceId) {
  try {
    const command = new DeleteTagsCommand({
      Resources: [instanceId],
      Tags: [{ Key: SLEEP_TAG_KEY }],
    });

    await devEc2Client.send(command);
  } catch (error) {
    logger.error('ERR-CICD-CMD-045', 'Error removing sleep tag:', error);
  }
}

function getMonarchEc2Names(envName) {
  const k8sInstanceName = `Monarch-${normalizeName(envName)} Kubernetes`;
  const kafkaInstanceName = `Monarch-${normalizeName(envName)} Kafka`;
  return [k8sInstanceName, kafkaInstanceName];
}

export async function addSleepTagsToEc2(envName) {
  const instances = getMonarchEc2Names(envName);
  for (const instance of instances) {
    const instanceId = await getInstanceIdByTag('Name', instance);
    if (instanceId) {
      await addSleepTag(instanceId);
    } else {
      logger.info(`instance not found for ${instance}`);
    }
  }
}

export async function removeSleepTagsFromEc2(envName) {
  const instances = getMonarchEc2Names(envName);
  for (const instance of instances) {
    const instanceId = await getInstanceIdByTag('Name', instance);
    if (instanceId) {
      await removeSleepTag(instanceId);
    } else {
      logger.info(`instance not found for ${instance}`);
    }
  }
}
