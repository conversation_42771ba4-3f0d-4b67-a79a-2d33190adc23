import { PROD_EMAILS, QVCONZ_DAE_HOST } from 'common/util/const.js';
import runQuery from '../dal/pgManager.js';
import { getStringColumns, getQvconzHostname } from '../dal/website.js';

export async function replaceUrls(appEnv, dbName) {
  const {
    contentUrl, websiteUrl, qvaWebsiteUrl, costBuilderUrl, darrochUrl,
  } = getDeaWebsiteUrls(appEnv);
  let replacements = [
    { from: 'content.dev.quotablevalue.co.nz', to: `${contentUrl}` },
    { from: 'https://qv.co.nz', to: `https://${websiteUrl}` },
    { from: 'https://www.qv.co.nz', to: `https://${websiteUrl}` },
    { from: 'https://dev.quotablevalue.co.nz', to: `https://${websiteUrl}` },
    { from: 'dev.quotablevalue.co.nz', to: `${websiteUrl}` },
    { from: 'www.qv.co.nz', to: `${websiteUrl}` },
    { from: 'https://qva.dev2.quotablevalue.co.nz', to: `https://${qvaWebsiteUrl}` },
    { from: 'qva.dev2.quotablevalue.co.nz', to: `${qvaWebsiteUrl}` },
    { from: 'https://costbuilder.dev.quotablevalue.co.nz', to: `https://${costBuilderUrl}` },
    { from: 'costbuilder.dev.quotablevalue.co.nz', to: `${costBuilderUrl}` },
    { from: 'https://darroch.dev.quotablevalue.co.nz', to: `https://${darrochUrl}` },
    { from: 'darroch.dev.quotablevalue.co.nz', to: `${darrochUrl}` },
  ];

  const hostname = await getQvconzHostname(dbName);
  logger.info(`Hostname: ${hostname}`);
  if (hostname.includes(QVCONZ_DAE_HOST)) {
    const oleEnv = hostname.split('.')[0];
    const {
      contentUrl: oldContentUrl,
      websiteUrl: oldWebsiteUrl,
      qvaWebsiteUrl: oldQvaWebsiteUrl,
      costBuilderUrl: oldcostBuilderUrl,
      darrochUrl: oldDarrochUrl,
    } = getDeaWebsiteUrls(oleEnv);
    replacements = [
      { from: oldWebsiteUrl, to: websiteUrl },
      { from: oldQvaWebsiteUrl, to: qvaWebsiteUrl },
      { from: oldcostBuilderUrl, to: costBuilderUrl },
      { from: oldDarrochUrl, to: darrochUrl },
      { from: oldContentUrl, to: contentUrl },
    ];
  }

  const replaceWebsiteUrls = [
    `update website.wagtailcore_site set hostname = '${websiteUrl}' where is_default_site = true`,
    `update website.wagtailcore_site set hostname = '${darrochUrl}' where site_name = 'Darroch'`,
    `update website.wagtailcore_site set hostname = '${costBuilderUrl}' where site_name = 'CostBuilder'`,
    `update website.wagtailcore_site set hostname = '${qvaWebsiteUrl}' where site_name = 'QV AU'`,
  ];

  for (const replaceWebsiteUrl of replaceWebsiteUrls) {
    await runQuery(replaceWebsiteUrl, dbName);
  }

  for (const { from, to } of replacements) {
    logger.info(`Replacing ${from} with ${to}`);
    const rows = await getStringColumns(dbName, 'website');
    const updateQueries = rows.map((row) => {
      const { table_name: tableName, column_name: columnName } = row;
      return `
          UPDATE website.${tableName}
          SET ${columnName} = REPLACE(${columnName}, '${from}', '${to}')
          WHERE ${columnName} LIKE '%${from}%';
      `;
    });

    for (const updateQuery of updateQueries) {
      await runQuery(updateQuery, dbName);
    }
  }
}

export async function replaceEmails(email, dbName) {
  for (const prodEmail of PROD_EMAILS) {
    logger.info(`Replacing ${prodEmail} with ${email}`);
    const rows = await getStringColumns(dbName, 'website');

    const updateQueries = rows.map((row) => {
      const { table_name: tableName, column_name: columnName } = row;
      return `
          UPDATE website.${tableName}
          SET ${columnName} = REPLACE(${columnName}, '${prodEmail}', '${email}')
          WHERE ${columnName} LIKE '%${prodEmail}%';
      `;
    });

    for (const updateQuery of updateQueries) {
      await runQuery(updateQuery, dbName);
    }
  }
}

function getDeaWebsiteUrls(appEnv) {
  const websiteUrl = `${appEnv}.${QVCONZ_DAE_HOST}`;
  const qvaWebsiteUrl = `qva.${websiteUrl}`;
  const costBuilderUrl = `costbuilder.${websiteUrl}`;
  const darrochUrl = `darroch.${websiteUrl}`;
  const contentUrl = `content.${websiteUrl}`;

  return {
    contentUrl, websiteUrl, qvaWebsiteUrl, costBuilderUrl, darrochUrl,
  };
}
