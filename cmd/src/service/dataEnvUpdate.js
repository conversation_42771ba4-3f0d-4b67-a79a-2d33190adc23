import { ERROR_CODES } from 'common/util/const.js';
import { getDdeApplicationsByEnv } from 'common/dal/applications.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { STABLE } from 'common/enums/environmentStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { createConfigsForApps } from './deployApplications.js';
import { AppEnvServiceError } from '../util/errors.js';

export default async function dataEnvUpdate(options) {
  const { dataEnv } = options;
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new AppEnvServiceError(dataEnv, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dataEnvironment = queryResults[0];
  const allApplications = await getDdeApplicationsByEnv(dataEnv);
  await createConfigsForApps(allApplications, dataEnvironment, ApplicationType.DDE);

  const pendingApplications = allApplications.filter((item) => item.Status.S === PipelineStatus.PENDING);
  logger.info(`pending applications: ${pendingApplications.length}}`);
  if (pendingApplications.length === 0) {
    logger.info('no deployment is required,update data env task finished');
    await updateDataEnvironmentStatus(dataEnv, STABLE);
    return;
  }
  const deploymentId = await initDeployment(dataEnv, ApplicationType.DDE, pendingApplications.map((app) => app.Name.S).join(','), 'date-env-update');
  logger.info(`deploymentId ${deploymentId} initiated`);
  await updateDataEnvironmentStatus(dataEnv, STABLE);
  logger.info('update data env task finished');
}
