import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { ERROR_CODES } from 'common/util/const.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import { updateDatastoreToDelete } from 'common/dal/datastores.js';
import { getDatabaseNames } from 'common/dal/datastoreStacks.js';
import { destroyFinishUpdateEnvironment } from './dataEnvironment.js';
import {
  destroyAgentJob, destroyDb, destroySchedule, dropUsersOnStaticDbs,
} from '../dal/destroyMssqlObjects.js';
import { DataEnvServiceError } from '../util/errors.js';

export default async function qvnzDestroy(options) {
  const { dataEnv } = options;
  logger.info('start destroying mssql databases.', dataEnv);
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, dataStoreTypes.QIVS, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  for (const db of (await getDatabaseNames(dataEnv, dataStoreTypes.QIVS))) {
    const dbName = `${dataEnv}_${db}`;
    logger.info(`delete db ${dbName} started`);
    await destroyDb(dbName);
    logger.info(`delete db ${dbName} finished`);
    const dsUniqueKey = `${dataEnv}-${dataStoreTypes.QIVS}-${db}`;
    await updateDatastoreToDelete(dsUniqueKey);
  }

  await dropUsersOnStaticDbs(dataEnv);

  const result = await destroyAgentJob(`${dataEnv}`);
  logger.info(`delete agent job ${dataEnv} finished, result ${result}`);

  await destroySchedule(`${dataEnv}`);
  logger.info('delete schedule finished');

  await destroyFinishUpdateEnvironment(dataEnv);
  logger.info(`delete env ${dataEnv} successfully`);
}
