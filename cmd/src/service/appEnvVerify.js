import {
  getDataEnvironmentByName,
  getDataEnvironmentsByStatus,
  startTimeCompareFn,
} from 'common/dal/dataEnvironments.js';
import { STATUS_INVALID, STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import { getBuilds } from 'common/service/getBuilds.js';
import { getAllPodsConfiguration } from 'common/dal/podConfiguration.js';
import { HEALTHY, STABLE, UNHEALTHY } from 'common/enums/environmentStatus.js';
import Logger from 'common/util/Logger.js';
import { ApplicationStack } from 'common/enums/applicationStack.js';
import { getAppEnvironmentByName } from 'common/dal/appEnvironments.js';
import { getDefaultDeployAppStacks } from 'common/dal/appStacks.js';
import getConfig from 'common/config/getConfig.js';
import deleteAppEnvironment from 'common/service/deleteAppEnvironment.js';
import waitDeploymentCompleted from 'common/service/waitDeploymentCompleted.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import createAppEnvironment from 'common/service/createAppEnvironment.js';
import { getAppEnvConfig } from 'common/service/getAppEnvConfig.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import appEnvCreate from './appEnvCreate.js';
import appEnvDestroy from './appEnvDestroy.js';
import { sendSlack } from '../util/slackUtil.js';

export default async function appEnvVerify(options) {
  global.logger = new Logger();
  const { payload } = options;
  const { branch, dde } = JSON.parse(payload);
  logger.info(`branch: ${branch} , dde: ${dde}`);

  const stage = await getConfig('STAGE', 'cicd');
  let stableDataEnv = await getDataEnvironmentsByStatus([STABLE, HEALTHY, UNHEALTHY]);
  stableDataEnv = stableDataEnv.filter((env) => env.Stage.S === stage);
  if (stableDataEnv.length === 0) {
    return { status: STATUS_INVALID, message: `No stable data environment found in stage (${stage})` };
  }
  if (dde !== '') {
    const dataEnvironments = await getDataEnvironmentByName(dde);
    if (dataEnvironments.length === 0) {
      return { status: STATUS_INVALID, message: `data environment ${dde} not found` };
    }
  }

  let dataEnv = dde;
  if (dde === '') {
    stableDataEnv.sort(startTimeCompareFn());
    dataEnv = stableDataEnv[0].Name.S;
  }

  logger.info(`create app env using data environment: ${dataEnv}`);
  const appEnv = await getAppEnvName();
  const message = `verify app env[${appEnv}] using data environment: ${dataEnv}, branch: ${branch}`;
  await sendSlack(CmdAppTasks.APP_ENV_VERIFY, message, ':white_check_mark:');
  const awsAccount = 'dev';
  const includeQivsExternal = false;
  const appStack = await getDefaultDeployAppStacks();
  const applications = (await getApplications(branch)).filter((app) => appStack.find((stack) => stack.Pipeline.S === app.name));
  const appNames = applications.map((app) => app.name);
  logger.info('applications ', { appNames });

  const includeQivs = false;
  const costCentre = 'CICD';
  const stacks = [ApplicationStack.MONARCH];
  const contextConfig = JSON.stringify(await getAppEnvConfig(appEnv, dataEnv, awsAccount, includeQivs, false), null, 2);
  const appPayload = {
    dataEnv,
    defaultBranch: branch,
    contextConfig,
    awsAccount,
    locked: false,
    applications,
    includeQivs,
    includeQivsExternal,
    costCentre,
    stacks,
    sleepTags: false,
  };
  const createAppResult = await createAppEnvironment(appEnv, appPayload, false);
  logger.info('create app env result', { createAppResult });
  if (createAppResult.status !== STATUS_SUCCESS) {
    return { status: STATUS_INVALID, message: 'failed to create app environment record.' };
  }
  try {
    await appEnvCreate({ appEnv, reindex: false, sleepTags: false });
    await waitDeploymentCompleted(appEnv, ApplicationType.DAE);
  } catch (e) {
    logger.error('ERR-SCHEDULE-001', `failed to create scheduled dae env[${appEnv}] using data environment: ${dataEnv}, branch: ${branch}`, e);
    return { status: STATUS_INVALID, message: 'failed to create scheduled.' };
  } finally {
    logger.info('start deleting the app environment');
    await deleteAppEnvironment(appEnv);
    await appEnvDestroy({ appEnv });
    logger.info('app environment deleted');
  }

  return { status: STATUS_SUCCESS, message: 'app env verify completed.' };
}

async function getAppEnvName() {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentDay = currentDate.getDate();
  const baseName = `v${currentMonth}${currentDay}`;
  let envName = baseName;
  let existingEnvironments = await getAppEnvironmentByName(envName);
  logger.info(`envName ${envName}, existingEnvironments: ${existingEnvironments.length}`);
  if (existingEnvironments.length === 0) {
    return envName;
  }
  // If the base name exists, generate a new name with a numeric suffix
  let suffix = 1;
  envName = `${baseName}${suffix}`;
  existingEnvironments = await getAppEnvironmentByName(envName);
  logger.info(`envName ${envName}, existingEnvironments: ${existingEnvironments.length}`);

  while (existingEnvironments.length > 0) {
    suffix++;
    envName = `${baseName}${suffix}`;
    existingEnvironments = await getAppEnvironmentByName(envName);
    logger.info(`envName ${envName}, existingEnvironments: ${existingEnvironments.length}`);
  }
  return envName;
}

async function getApplications(branch) {
  const parameter = {
    stacks: `${ApplicationStack.MONARCH}`,
  };
  const builds = await getBuilds(parameter);
  const podsConfiguration = await getAllPodsConfiguration();
  return builds.map((build) => convertToApplication(build, branch, podsConfiguration));
}

function convertToApplication(build, branch, podsConfiguration) {
  const application = { ...build };
  application.gitBranch = branch;
  application.name = application.application;
  delete application.application;
  const podConfig = podsConfiguration.find((pod) => pod.AppName.S === application.name);
  if (podConfig) {
    application.pods = parseInt(podConfig.Default.S, 10);
  } else {
    application.pods = 0;
  }
  return application;
}
