import { ApplicationStack } from 'common/enums/applicationStack.js';
import { REINDEX_TYPES } from 'common/enums/reindexTypes.js';
import { getAppEnvironmentByName, getStacks } from 'common/dal/appEnvironments.js';
import { getApiKeyByName } from 'common/service/apiGateway.js';
import { getApplicationsByEnvAndType } from 'common/dal/applications.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { API_STREAM_APP } from 'common/util/const.js';
import { normalizeName } from 'common/util/appNameUtli.js';

export async function reindexAllOpensearch(appEnv) {
  logger.info(`reindex OpenSearch ${appEnv}`);
  const results = await getAppEnvironmentByName(appEnv);
  if (results.length === 0) {
    throw new Error(`Could not find appEnvironment ${appEnv}`);
  }
  const stacks = getStacks(results[0]);
  const reindexType = getReindexType(stacks);
  if (!reindexType) {
    throw new Error(`Could not determine a reindexType for stacks ${stacks.join(', ')}`);
  }
  logger.info(`reindexOpensearch ${appEnv} for stacks ${stacks.join(', ')}`);
  const apiKeyName = `${appEnv}-dae-lambda-api-key`;
  const lambdaApiKey = await getApiKeyByName(apiKeyName);

  const url = `https://${appEnv}.dae.qvapi.co.nz/stream/reindex-all`;
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': lambdaApiKey,
    },
    body: JSON.stringify({
      reindexType,
    }),
  });
  if (!response.ok) {
    throw new Error(`reindex failed with statusCode ${response.status}`);
  }
  const responseData = await response.json();
  const jobs = responseData.map((job) => ({
    jobName: job.jobName,
    jobArn: job.jobArn,
    jobId: job.jobId,
  }));

  logger.info(`reindex succeeded with statusCode ${response.status}`, {
    jobs,
  });
  return jobs;
}

export async function reindexOpensearch(appEnv, payload) {
  logger.info(`refreshIndex ${appEnv}`, { payload });
  const results = await getAppEnvironmentByName(appEnv);
  if (results.length === 0) {
    throw new Error(`Could not find appEnvironment ${appEnv}`);
  }
  const { indexName, createNewIndex } = JSON.parse(payload);
  const indexType = indexName.split('-').slice(1, -1).join('-');
  const newIndexName = `${indexName.split('-').slice(0, -1).join('-')}-${new Date().getTime()}`;
  const apiKeyName = `${appEnv}-dae-lambda-api-key`;
  const lambdaApiKey = await getApiKeyByName(apiKeyName);
  const url = `https://${appEnv}.dae.qvapi.co.nz/stream/reindex`;
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': lambdaApiKey,
    },
    body: JSON.stringify({
      indexName: createNewIndex ? newIndexName : indexName,
      indexType,
    }),
  });
  if (!response.ok) {
    throw new Error(`reindex failed with statusCode ${response.status}`);
  }
  const responseData = await response.json();
  const job = {
    jobName: responseData.jobName,
    jobArn: responseData.jobArn,
    jobId: responseData.jobId,
  };
  logger.info(`reindex succeeded with statusCode ${response.status}`, {
    job,
  });
  return job;
}

function getReindexType(stacks) {
  if (!stacks || !stacks.length) {
    return null;
  }
  const includesMonarch = stacks.includes(ApplicationStack.MONARCH);
  const includesPublicWebsite = stacks.includes(ApplicationStack.PUBLIC_WEBSITE);
  if (includesMonarch && includesPublicWebsite) {
    return REINDEX_TYPES.ALL;
  }
  if (includesMonarch) {
    return REINDEX_TYPES.INTERNAL;
  }
  if (includesPublicWebsite) {
    return REINDEX_TYPES.EXTERNAL;
  }
  return null;
}

export async function deleteOpensearchIndexes(appEnv) {
  const applications = await getApplicationsByEnvAndType(appEnv, ApplicationType.DAE);
  if (!applications.some((app) => app.Name.S === API_STREAM_APP)) {
    logger.info(`No QIVS stream app found in ${appEnv}`);
    return;
  }
  logger.info(`delete OpensearchIndexes for ${appEnv}`);
  const daeApiKeyName = `${normalizeName(appEnv)}-dae-lambda-api-key`;
  const daeApiKey = await getApiKeyByName(daeApiKeyName);
  if (!daeApiKey) {
    return;
  }

  logger.info(`deleteOpensearchIndexes for ${appEnv}`);
  const url = `https://${appEnv}.dae.qvapi.co.nz/stream/delete-index`;
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': daeApiKey,
      },
      body: JSON.stringify({
        envName: appEnv,
      }),
    });
    if (!response.ok) {
      logger.error(`delete index failed with statusCode ${response.status}`);
      logger.error(await response.json());
    }
  } catch (error) {
    logger.error('ERR-DYE-f06dae', 'Something went wrong while trying to delete opensearch indexes', error);
  }
}
