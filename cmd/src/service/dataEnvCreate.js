import { getDataEnvironmentByName, updateDataEnvironmentJsonConfig } from 'common/dal/dataEnvironments.js';
import {
  AWS_ACCOUNTS,
  CICD_TF_BUCKET_PREFIX,
  CICDDB01_EC2_ID,
  DEV_TF_BUCKET_PREFIX,
  DYNAMIC_DEVICE_PREFIX,
  ERROR_CODES,
  MSSQL_PATHS,
  TF_DATA_TEMPLATE_FOLDER,
} from 'common/util/const.js';
import { createTfScript } from 'common/dal/tfScripts.js';
import { getDataStoresByEnv, updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import { PROVISIONING } from 'common/enums/environmentStatus.js';
import { runCmdSync, runRemoteLinuxCmdSync, runRemotePowerShellCmd } from 'common/util/cmdUtil.js';
import {
  getPgDataFolder, listFiles, readFileToString, substringAfter,
} from 'common/util/fileUtil.js';
import { getDdeApplicationsByEnv } from 'common/dal/applications.js';
import { normalizeName } from 'common/util/appNameUtli.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { DataEnvServiceError } from '../util/errors.js';
import { tfApply, writeTfScriptsToDisk } from '../util/tfUtil.js';
import { DEV_AWS_ACCESS_KEY_ID, DEV_AWS_CREDENTIALS, DEV_AWS_SECRET_ACCESS_KEY } from '../util/awsConfig.js';

export default async function dataEnvCreate(options) {
  const { dataEnv } = options;
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, '', ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dataEnvironment = queryResults[0];
  const drive = dataEnvironment.Drive?.S;
  const size = dataEnvironment.Size?.S;
  const costCentre = dataEnvironment.CostCentre.S;
  const applications = await getDdeApplicationsByEnv(dataEnv);
  const lambdaApplications = applications.map((app) => app.Name.S);
  logger.info(`lambdaApplications: ${JSON.stringify(lambdaApplications)}`);
  const datastores = (await getDataStoresByEnv(dataEnv)).map((ds) => ds.Name.S);
  const includeApiLoad = lambdaApplications.includes('api-load');

  const result = await listFiles(
    TF_DATA_TEMPLATE_FOLDER,
    (dir, filename) => {
      if (!includeApiLoad && filename.endsWith('api_load.tf')) {
        return false;
      }
      return ['.tf', '.sh', '.sample', '.yml'].some((extension) => filename.endsWith(extension));
    },
  );

  logger.info(`result: ${JSON.stringify(result)}`);
  for (const file of result) {
    let path = `tf/data_${dataEnv}${substringAfter(file, `${TF_DATA_TEMPLATE_FOLDER}`)}`;
    let text = readFileToString(file);
    if (path.endsWith('locals.tf.sample')) {
      text = text
        .replace('ENV', dataEnv)
        .replace('COST_CENTRE', costCentre)
        .replace('DEVICE_NAME', `${DYNAMIC_DEVICE_PREFIX}${drive.toLowerCase()}`)
        .replace('DRIVE_NAME', `CICDDB01 dynamic drive ${drive}:`)
        .replace('LAMBDA_APPLICATIONS', JSON.stringify(lambdaApplications))
        .replace('DATASTORES', JSON.stringify(datastores))
        .replace('DEV_ACCESS_KEY_ID', DEV_AWS_ACCESS_KEY_ID)
        .replace('DEV_SECRET_ACCESS_KEY', DEV_AWS_SECRET_ACCESS_KEY)
        .replace('SIZE', size)
        .replace('INCLUDE_API_LOAD', includeApiLoad);
      path = path.replace('.sample', '');
    }
    if (path.endsWith('base.tf')) {
      text = text.replace(`${DEV_TF_BUCKET_PREFIX}/data_template`, `${DEV_TF_BUCKET_PREFIX}/data_${dataEnv}`);
      text = text.replace(`${CICD_TF_BUCKET_PREFIX}/data_template`, `${CICD_TF_BUCKET_PREFIX}/data_${dataEnv}`);
    }
    await createTfScript(`data_${dataEnv}`, path, text);
  }

  await writeTfScriptsToDisk(`data_${dataEnv}`);
  for (const account of AWS_ACCOUNTS) {
    await tfApply(`data_${dataEnv}`, account);
    logger.info(`tf apply in ${account} account finished`);
  }
  await createDynamicDisk(drive, dataEnv);
  await createMssqlFolders(drive);
  await createPgFolders(drive);

  await updateDataEnvironmentStatus(dataEnv, PROVISIONING);

  logger.info('job finished');
}

async function createDynamicDisk(drive, env) {
  const device = `${DYNAMIC_DEVICE_PREFIX}${drive.toLowerCase()}`;
  const getVolumeIdCmd = `${DEV_AWS_CREDENTIALS} aws ec2 describe-volumes --filters "Name=attachment.instance-id,Values=${CICDDB01_EC2_ID}" "Name=attachment.device,Values=${device}" --query "Volumes[0].Attachments[0].VolumeId" --output text`;
  const volumeId = runCmdSync(getVolumeIdCmd).trim().replace(/-/g, '');
  logger.info(`volumeId: ${volumeId}`);

  const getDiskNumberCmd = `Get-Disk | Where-Object { $_.SerialNumber -like \\"${volumeId}*\\" } | Select-Object -ExpandProperty Number`;
  const diskNumber = (await runRemotePowerShellCmd(getDiskNumberCmd)).replace(/\s/g, '');
  logger.info(`diskNumber: ${diskNumber}`);

  const createDiskCmd = `Initialize-Disk -Number ${diskNumber} -PartitionStyle GPT -PassThru`;
  const result = await runRemotePowerShellCmd(createDiskCmd);
  logger.info(result);

  const createPartitionCmd = `New-Partition -UseMaximumSize -DiskNumber ${diskNumber}`;
  const result2 = await runRemotePowerShellCmd(createPartitionCmd);
  logger.info(result2);

  const partitionNumber = 2; // partition 1 is reserved for system
  const formatVolumeCmd = `Get-Partition -DiskNumber ${diskNumber} -PartitionNumber ${partitionNumber} | Format-Volume -FileSystem NTFS -NewFileSystemLabel "${env}" -AllocationUnitSize 4096 -Confirm:$false`;
  const result3 = await runRemotePowerShellCmd(formatVolumeCmd);
  logger.info(result3);

  const assignDriveLetterCmd = `Get-Partition -DiskNumber ${diskNumber}  -PartitionNumber  ${partitionNumber} | Set-Partition -NewDriveLetter ${drive} -Confirm:$false`;
  const result4 = await runRemotePowerShellCmd(assignDriveLetterCmd);
  logger.info(result4);
}

async function createMssqlFolders(drive) {
  for (const item of MSSQL_PATHS) {
    await createPath(`${drive}:${item}`);
  }
}

async function createPgFolders(drive) {
  await createPath(getPgDataFolder(drive));
}

async function createPath(path) {
  const result = await runRemoteLinuxCmdSync(`mkdir ${path}`);
  logger.info(result);
  const grantPermissionCmd = `icacls "${path}"  --% /grant "everyone:(OI)(CI)F" /T`;
  await runRemotePowerShellCmd(grantPermissionCmd);
  logger.info(result);
}

async function ddeContext(dataEnv) {
  const getApiKeyCmd = `${DEV_AWS_CREDENTIALS} aws apigateway get-api-keys --name-query "${normalizeName(dataEnv)}-dde-lambda-api-key" --include-values`;
  const keys = JSON.parse(runCmdSync(getApiKeyCmd));
  if (keys.items.length === 0) {
    throw new Error(`No api key found for ${dataEnv}`);
  }
  const value = {
    DATA_ENV: dataEnv,
  };
  return value;
}
