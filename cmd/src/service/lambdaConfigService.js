import AdmZip from 'adm-zip';
import fs, { readdirSync } from 'fs';
import path from 'path';

import {
  ARTIFACT_FILE_NAME,
  CONFIG_SAMPLE_NAME,
  DEPLOY_BUCKET_NAME,
  INFRA_SAMPLE_PATH,
  TMP_FOLDER,
} from 'common/util/const.js';
import {
  checkParameterExistence,
  createAdvancedSecureParameter,
  createParameter,
  deleteParameter,
  testConfigParameterName,
} from 'common/util/ssmutil.js';
import { deleteFolder, readFileToString } from 'common/util/fileUtil.js';
import { downloadFile } from 'common/util/s3util.js';
import { normalizeName } from 'common/util/appNameUtli.js';
import { cicdS3Client, cicdSsmClient, devSsmClient } from '../util/awsConfig.js';

export async function createEcsConfig(appEnv, application, context, costCentre) {
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(`/${normalizeName(appEnv)}/${appName}`);
  const bucketKey = application?.BucketKey?.S;
  const folder = `${TMP_FOLDER}/${appEnv}/${appName}`;
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdir(`${TMP_FOLDER}/${appEnv}/${appName}`, { recursive: true }, (err) => {
    if (err) {
      return console.error(err);
    }
    logger.info('Directory created successfully.');
    return null;
  });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info(`unzip ${fileName}`);
  const zip = new AdmZip(fileName, {});
  try {
    zip.extractEntryTo(CONFIG_SAMPLE_NAME, `${folder}/`, false, true);
  } catch (e) {
    logger.error(`unzip ${fileName} failed`);
    console.log(e);
  }

  const filePath = `${TMP_FOLDER}/${appEnv}/${appName}/${CONFIG_SAMPLE_NAME}`;
  let configJson = readFileToString(filePath);
  logger.info(`create ecs config ${filePath}`);
  Object.entries(context).forEach(([key, v]) => {
    configJson = configJson.replaceAll(`{${key}}`, v);
  });
  logger.info('config json', { configJson });
  if (await checkParameterExistence(devSsmClient, appConfigName)) {
    await deleteParameter(devSsmClient, appConfigName);
  }
  if (appName === 'report-generator') {
    await createAdvancedSecureParameter(devSsmClient, appConfigName, configJson, costCentre);
  } else {
    await createParameter(devSsmClient, appConfigName, configJson, costCentre);
  }
  deleteFolder(folder);
}

export async function createLambdaConfig(appEnv, application, context, costCentre) {
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(`/${normalizeName(appEnv)}/${appName}`);

  const bucketKey = application?.BucketKey?.S;
  const folder = `${TMP_FOLDER}/${appEnv}/${appName}`;
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdir(`${TMP_FOLDER}/${appEnv}/${appName}`, { recursive: true }, (err) => {
    if (err) {
      return console.error(err);
    }
    logger.info('Directory created successfully.');
    return null;
  });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info('unzip deploy.zip');
  const zip = new AdmZip(fileName, {});
  zip.extractEntryTo(CONFIG_SAMPLE_NAME, `${folder}/`, false, true);

  let srcConfigJson = readFileToString(`${TMP_FOLDER}/${appEnv}/${appName}/${CONFIG_SAMPLE_NAME}`);
  logger.info('create lambda config');
  Object.entries(context).forEach(([key, v]) => {
    srcConfigJson = srcConfigJson.replaceAll(`{${key}}`, v);
  });
  logger.info('config json', { configJson: srcConfigJson });
  if (await checkParameterExistence(devSsmClient, appConfigName)) {
    await deleteParameter(devSsmClient, appConfigName);
  }
  await createParameter(devSsmClient, appConfigName, srcConfigJson, costCentre);
  deleteFolder(folder);
}

export async function createLambdaTestConfig(env, application, context, costCentre) {
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(testConfigParameterName(env, appName));

  const bucketKey = application?.BucketKey?.S;
  const folder = `${TMP_FOLDER}/${env}/${appName}`;
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdir(`${TMP_FOLDER}/${env}/${appName}`, { recursive: true }, (err) => {
    if (err) {
      return console.error(err);
    }
    logger.info('Directory created successfully.');
    return null;
  });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info('unzip deploy.zip');
  const zip = new AdmZip(fileName, {});
  zip.extractEntryTo(`test/${CONFIG_SAMPLE_NAME}`, `${folder}/test/`, false, true);

  let srcConfigJson = readFileToString(`${TMP_FOLDER}/${env}/${appName}/test/${CONFIG_SAMPLE_NAME}`);
  logger.info('create lambda test config');
  Object.entries(context).forEach(([key, v]) => {
    srcConfigJson = srcConfigJson.replaceAll(`{${key}}`, v);
  });
  logger.info('config json', { appConfigName, configJson: srcConfigJson });
  if (await checkParameterExistence(cicdSsmClient, appConfigName)) {
    await deleteParameter(cicdSsmClient, appConfigName);
  }
  await createParameter(cicdSsmClient, appConfigName, srcConfigJson, costCentre);
  deleteFolder(folder);
}

export async function createK8sTestConfig(appEnv, application, context, costCentre) {
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(testConfigParameterName(appEnv, appName));
  const bucketKey = application?.BucketKey?.S;
  logger.info('bucketKey', { bucketKey });
  const folder = `${TMP_FOLDER}/${appEnv}/${appName}`;
  logger.info('folder', { folder });
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdir(`${TMP_FOLDER}/${appEnv}/${appName}`, { recursive: true }, (err) => {
    if (err) {
      return console.error(err);
    }
    logger.info('Directory created successfully.');
    return null;
  });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info('unzip deploy.zip');
  const zip = new AdmZip(fileName, {});
  zip.extractAllTo(`${folder}/`, true);

  const zipFiles = listZipFiles(`${folder}/`);
  if (zipFiles.length > 1) {
    throw new Error('More than one zip file found');
  }
  const zipFile = zipFiles[0];
  logger.info('zipFile', { zipFile });
  const configZipName = `${folder}/${zipFile}`;

  logger.info('configZipName', { configZipName });
  const configZip = new AdmZip(configZipName, {});

  try {
    configZip.extractEntryTo(`test/${CONFIG_SAMPLE_NAME}`, `${folder}/test/`, false, true);
  } catch (e) {
    if (e.message.includes('Entry doesn\'t exist')) {
      logger.info(`no test sample for ${appName}`);
      return;
    }
    logger.error('ERR-CICD-CMD-048', `unzip ${configZipName} failed`, e);
    throw e;
  }

  let srcConfigJson = readFileToString(`${TMP_FOLDER}/${appEnv}/${appName}/test/${CONFIG_SAMPLE_NAME}`);
  logger.info('create k8s test config');
  Object.entries(context).forEach(([key, v]) => {
    srcConfigJson = srcConfigJson.replaceAll(`{${key}}`, v);
  });
  logger.info('config json', { appConfigName, configJson: srcConfigJson });
  if (await checkParameterExistence(cicdSsmClient, appConfigName)) {
    await deleteParameter(cicdSsmClient, appConfigName);
  }
  await createParameter(cicdSsmClient, appConfigName, srcConfigJson, costCentre);
  deleteFolder(folder);
}

export function listZipFiles(folder) {
  const files = readdirSync(folder);
  logger.info('files', { files });
  // Filter files that end with .zip and exclude deploy.zip
  const zipFiles = files.filter((file) => file.endsWith('.zip') && file !== 'deploy.zip');
  logger.info('zipFiles', { zipFiles });
  return zipFiles; // if you want to return the list
}

function removeDaeSuffix(str) {
  if (str.endsWith('-dae')) {
    return str.slice(0, -4);
  }
  return str;
}

export async function createConfigIfSampleExist(filePath, configName, appEnv, application, context, costCentre) {
  logger.info('createConfigIfSampleExist', {
    filePath, configName, appEnv, costCentre,
  });
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(`/${normalizeName(appEnv)}/${appName}/${configName}`);

  const bucketKey = application?.BucketKey?.S;
  const folder = `${TMP_FOLDER}/${appEnv}/${appName}`;
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdirSync(folder, { recursive: true });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info('unzip deploy.zip');
  const zip = new AdmZip(fileName, {});
  try {
    zip.extractEntryTo(filePath, `${folder}/${path.dirname(INFRA_SAMPLE_PATH)}`, false, true);
  } catch (e) {
    if (e.message.includes('Entry doesn\'t exist')) {
      logger.info(`no tf sample for ${appName}`);
      return;
    }
    throw e;
  }
  const tempfilePath = `${folder}/${filePath}`;
  let srcConfigJson = readFileToString(tempfilePath);
  logger.info(`create config ${appConfigName}`);
  Object.entries(context).forEach(([key, v]) => {
    srcConfigJson = srcConfigJson.replaceAll(`{${key}}`, v);
  });
  logger.info('config json', { configJson: srcConfigJson });
  if (await checkParameterExistence(cicdSsmClient, appConfigName)) {
    await deleteParameter(cicdSsmClient, appConfigName);
  }
  await createParameter(cicdSsmClient, appConfigName, srcConfigJson, costCentre);
  deleteFolder(folder);
}
