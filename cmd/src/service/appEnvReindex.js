import { updateAppEnvironmentStatus, updateSleepTagsForAppEnvironment } from 'common/dal/appEnvironments.js';
import { HEALTHY, INDEXING_FAILED } from 'common/enums/environmentStatus.js';
import { addSleepTagsToEc2, removeSleepTagsFromEc2 } from '../util/ec2Util.js';
import { checkJobStatus, waitReindexingJobsCompleted } from '../util/batchUtil.js';
import { reindexOpensearch, reindexAllOpensearch } from './opensearchIndex.js';

export default async function appEnvReindex(options) {
  const { appEnv, payload } = options;
  try {
    await removeSleepTagsFromEc2(appEnv);
    await updateSleepTagsForAppEnvironment(appEnv, false);
    let jobs;
    if (payload) {
      jobs = [await reindexOpensearch(appEnv, payload)];
    } else {
      jobs = await reindexAllOpensearch(appEnv);
    }
    const timeoutReached = await waitReindexingJobsCompleted(jobs, 60);
    if (timeoutReached) {
      logger.error('ERR-CICD-CMD-046', 'failed to wait for reindexing jobs completed');
      await updateAppEnvironmentStatus(appEnv, INDEXING_FAILED);
      return;
    }
    const jobIds = jobs.map((job) => job.jobId);
    const jobStatus = await checkJobStatus(jobIds);
    const anyUnsuccessful = jobStatus.some((job) => job.status !== 'SUCCEEDED');
    const url = 'https://ap-southeast-2.console.aws.amazon.com/batch/home?region=ap-southeast-2#jobs/fargate/detail';
    jobStatus.forEach((job) => {
      logger.info('reindexing job status', { url: `${url}/${job.jobId}`, status: job.status });
    });
    logger.info('reindexing jobs completed');
    await updateAppEnvironmentStatus(appEnv, anyUnsuccessful ? INDEXING_FAILED : HEALTHY);
  } catch (e) {
    logger.error('ERR-CICD-CMD-045', 'failed to reindex opensearch', e);
    await updateAppEnvironmentStatus(appEnv, INDEXING_FAILED);
  } finally {
    await addSleepTagsToEc2(appEnv);
    await updateSleepTagsForAppEnvironment(appEnv, true);
  }
}
