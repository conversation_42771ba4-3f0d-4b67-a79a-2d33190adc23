import { QVNZ_PUBLIC, QVNZ_REFRESH_JOB_NAME } from 'common/util/const.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import { updateDatastoreStartTime, updateDatastoreToStable } from 'common/dal/datastores.js';
import { createDb, recreateSynonym, setRecoverySimple } from '../dal/createMssqlObjects.js';
import runAgentJob from '../dal/agentJob.js';
import { setUpUserRole } from '../dal/assignPermission.js';
import { executeSqlScripts } from '../util/sqlUtil.js';
import { dropSchemas, dropUsers } from '../dal/destroyMssqlObjects.js';

export default async function qvnzPublicRestore(dataEnv, drive) {
  logger.info(`restore ${QVNZ_PUBLIC} started`);
  const dsUniqueKey = `${dataEnv}-${dataStoreTypes.QIVS}-${QVNZ_PUBLIC}`;
  await updateDatastoreStartTime(dsUniqueKey);
  await restoreQvnzPublic(dataEnv, drive);
  await setRecoverySimple(dataEnv, QVNZ_PUBLIC);
  await recreateSynonym(dataEnv, QVNZ_PUBLIC);
  const jobName = `'${dataEnv} ${QVNZ_REFRESH_JOB_NAME}'`;
  await runAgentJob(jobName);
  await updateDatastoreToStable(dsUniqueKey, 'Schema created, synonym created, agent job started.');
}
async function restoreQvnzPublic(dataEnv, drive) {
  const dbName = `${dataEnv}_${QVNZ_PUBLIC}`;
  await createDb(dbName, drive);
  await dropSchemas(dataEnv, QVNZ_PUBLIC);
  await dropUsers(dataEnv, QVNZ_PUBLIC);
  await executeSqlScripts(dataEnv, QVNZ_PUBLIC);
  await setUpUserRole(dataEnv, dbName);
}
