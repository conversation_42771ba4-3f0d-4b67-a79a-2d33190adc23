import { DDE_HOST_ZONE_ID, ERROR_CODES } from 'common/util/const.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import {
  deleteDataEnvironment,
  getDataEnvironmentByName,
  updateDataEnvironmentStatus,
} from 'common/dal/dataEnvironments.js';
import { getDataStoresByEnv } from 'common/dal/datastores.js';
import { INFRA_DELETED } from 'common/enums/environmentStatus.js';
import { normalizeName } from 'common/util/appNameUtli.js';
import { getApplicationsByEnvAndType } from 'common/dal/applications.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { deleteDynamodb } from 'common/util/dynamodb.js';
import { DataEnvServiceError } from '../util/errors.js';
import dataInfraDestroy from './dataInfraDestroy.js';
import qvnzDestroy from './qvnzDestroy.js';
import pgDestroy from './pgDestroy.js';
import { deleteMssqlLoginIfExist } from '../dal/destroyMssqlObjects.js';
import { dropPgUserIfExists } from '../dal/createPgObjects.js';
import {
  deleteApplicationInfra,
  deleteLambdaApplications,
  deleteRoute53Records,
  deleteTestConfig,
} from './applicationsDestroy.js';
import { deleteCloudWatchLogGroups } from '../util/cloudwatch.js';
import { cloudWatchLogsClient, devCloudWatchLogsClient } from '../util/awsConfig.js';

export default async function dataEnvDestroy(options) {
  const { dataEnv } = options;
  logger.info(`destroy ${dataEnv} started`);
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, '', ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dataEnvironment = queryResults[0];
  const dataStores = await getDataStoresByEnv(dataEnvironment.Name.S);
  const uniqueDatastoreTypes = [...new Set(dataStores.map((dataStore) => dataStore.Type.S))];
  logger.info(`Found ${uniqueDatastoreTypes.length} datastores to delete: ${uniqueDatastoreTypes}`);

  for (const dataStoreType of uniqueDatastoreTypes) {
    if (dataStoreType === dataStoreTypes.PGMonarch) {
      await pgDestroy(options);
    }
    if (dataStoreType === dataStoreTypes.QIVS) {
      await qvnzDestroy(options);
    }
  }
  await deleteApplicationInfra(dataEnv, ApplicationType.DDE);
  const applications = await getApplicationsByEnvAndType(dataEnv, ApplicationType.DDE);
  await deleteLambdaApplications(applications, dataEnv, ApplicationType.DDE);
  for (const application of applications) {
    await deleteTestConfig(application, dataEnv);
  }
  deleteRoute53Records(normalizeName(dataEnv), DDE_HOST_ZONE_ID, ApplicationType.DDE);

  await dataInfraDestroy(options);
  await deleteMssqlLoginIfExist(dataEnv);
  await dropPgUserIfExists(dataEnv);
  await deleteCloudWatchLogGroups(cloudWatchLogsClient, [
    dataEnv,
  ]);

  await deleteCloudWatchLogGroups(devCloudWatchLogsClient, [
    `/${dataEnv}`,
    `-${dataEnv}`,
  ]);
  await deleteDynamodb(dataEnv, ApplicationType.DDE);

  await deleteDataEnvironment(dataEnv);

  logger.info(`destroy ${dataEnv} finished`);
}
