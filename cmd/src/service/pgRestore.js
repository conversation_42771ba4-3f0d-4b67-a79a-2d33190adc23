import {
  CICDDB01_IP, DB_PASSWORD_LENGTH, ERROR_CODES, PG_BACKUP_PATH, PG_USER,
} from 'common/util/const.js';
import { getDataEnvironmentByName, updatePgConfigByName } from 'common/dal/dataEnvironments.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import {
  updateDatastoreLastUpdatedTime,
  updateDatastoreStartTime,
  updateDatastoreToStable,
} from 'common/dal/datastores.js';
import { getPgDataFolder } from 'common/util/fileUtil.js';
import { runRemoteLinuxCmdSync } from 'common/util/cmdUtil.js';
import getConfig from 'common/config/getConfig.js';
import { getDatabaseNames } from 'common/dal/datastoreStacks.js';
import { DataEnvServiceError } from '../util/errors.js';
import {
  createPgDb,
  createPgExtensions,
  createPgUser,
  createTb,
  getTablespaceName,
  grantPrivileges,
} from '../dal/createPgObjects.js';
import { restoreFinishUpdateEnvironment } from './dataEnvironment.js';
import waitInfraReady from '../util/waitInfraReady.js';
import { generatePassword } from '../util/stringUtil.js';
import waitQvnzRestoreReady from '../util/waitQvnzRestoreReady.js';
import { sanitiseDb } from '../dal/sanitise.js';
import { destroyPostgresDb, terminateSessions } from '../dal/destroyPgObjects.js';
import { getAppUsers } from '../util/qvUserUtil.js';

export default async function pgRestore(options) {
  const { dataEnv, datastore } = options;

  logger.info(`restore postgres  started: ${dataEnv} ${datastore}`);

  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, dataStoreTypes.PGMonarch, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  await waitInfraReady(dataEnv);
  await waitQvnzRestoreReady(dataEnv);
  const dataEnvironment = queryResults[0];

  const drive = dataEnvironment.Drive.S;
  const location = getPgDataFolder(drive);
  const tablespaceName = getTablespaceName(dataEnv);
  let names = (await getDatabaseNames(dataEnv, dataStoreTypes.PGMonarch));

  const username = `${dataEnv}_${PG_USER}`;
  const migrateUsername = `${dataEnv}_migration_${PG_USER}`;
  if (!datastore) { // skip when restoring single db
    const password = generatePassword(DB_PASSWORD_LENGTH);
    const pgConfig = {
      pgIp: CICDDB01_IP,
      pgDbName: `${dataEnv}_property_service`,
      qvconzPgDbName: `${dataEnv}_qvconz`,
      pgUsername: username,
      pgPassword: password,
      pgMigrateUsername: migrateUsername,
      pgMigratePassword: password,
    };
    await updatePgConfigByName(dataEnv, pgConfig);
    await createTb(tablespaceName, location);
    await createPgUser(username, password);
    await createPgUser(migrateUsername, password);
  }

  if (datastore) {
    const dbName = `${dataEnv}_${datastore}`;
    await terminateSessions(dbName);
    await destroyPostgresDb(dbName);
    names = [datastore];
  }
  const users = await getAppUsers();
  for (const name of names) {
    try {
      const dbName = `${dataEnv}_${name}`;
      const dsUniqueKey = `${dataEnv}-${dataStoreTypes.PGMonarch}-${name}`;
      await updateDatastoreStartTime(dsUniqueKey);

      await createPgDb(dbName, tablespaceName);
      await createPgExtensions(dbName);
      await updateDatastoreLastUpdatedTime(dsUniqueKey);
      try {
        await restoreDumpFiles(dataEnv, name);
      } catch (e) {
        logger.error('ERR-CICD-CMD-098', 'Error restoring postgres dump files', e);
      }
      await grantPrivileges(dbName, migrateUsername, 'common_role');
      await grantPrivileges(dbName, username, 'admin');
      await sanitiseDb(dataEnv, name, users);
      await updateDatastoreToStable(dsUniqueKey, '');
    } catch (e) {
      logger.error('ERR-CICD-CMD-099', 'Error restoring postgres', e);
      throw new DataEnvServiceError(dataEnv, name, e.message, 'ERR-CICD-CMD-099');
    }

    await restoreFinishUpdateEnvironment(dataEnv);
  }
}

export async function restoreDumpFiles(dataEnv, name) {
  const dbName = `${dataEnv}_${name}`;
  const password = await getConfig('PG_PASSWORD');
  const server = await getConfig('PG_SERVER');
  const dbuser = await getConfig('PG_USERNAME');
  const restoreCmd = `cat ${PG_BACKUP_PATH}/${name}/*.gz | gzip -kdf | pg_restore --no-owner --no-privileges --dbname=postgresql://${dbuser}:${password}@${server}/${dbName}`;

  const result = await runRemoteLinuxCmdSync(restoreCmd);
  logger.info('restoreDumpFiles', { result });
  return result;
}
