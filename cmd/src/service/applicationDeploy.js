import { updateDeploymentStatus } from 'common/dal/deployments.js';
import { DeploymentStatus } from 'common/enums/deploymentStatus.js';
import {
  getAppEnvironmentByName,
  updateAppEnvironmentStatus,
  updateSleepTagsForAppEnvironment,
} from 'common/dal/appEnvironments.js';
import { INDEXING, INDEXING_FAILED, UNHEALTHY } from 'common/enums/environmentStatus.js';
import {
  formatApplications,
  getApplicationsByEnvAndType,
  updateApplicationDeployStatus,
} from 'common/dal/applications.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { API_STREAM_APP, REINDEXER_QUEUE_NAME } from 'common/util/const.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import { addExistingApiToAppEnv } from 'common/service/apiGateway.js';
import { deployApplications } from './deployApplications.js';
import appEnvTests from './appEnvTests.js';
import { addSleepTagsToEc2 } from '../util/ec2Util.js';
import { reindexAllOpensearch } from './opensearchIndex.js';
import { waitReindexingJobsCompleted } from '../util/batchUtil.js';

export default async function applicationDeploy(options) {
  logger.info('app environment deployment', { options });
  const {
    deploymentId, reindex, payload, sleepTags,
  } = options;
  const { type, envName, applicationName } = JSON.parse(payload);
  logger.info(`deployApplications  ${type} ${envName} ${applicationName}`);
  try {
    const { message, status } = await deployApplications(deploymentId, envName, type, applicationName);
    logger.info('deployApplications result', { message, status });
    if (type === ApplicationType.DAE && !reindex && applicationName === '') {
      logger.info('when deploy all applications, check if api stream exists in the environment');
      const queryResults = await getAppEnvironmentByName(envName);
      const appEnvironment = queryResults[0];
      const applications = await getApplicationsByEnvAndType(envName, type);
      const hasApiStream = applications.some((application) => application.Name.S === API_STREAM_APP);
      if (!hasApiStream) {
        logger.info('API Stream application not found in the environment, adding it to the environment from the existing data environment');
        await addExistingApiToAppEnv(appEnvironment.DataEnv.S, envName, API_STREAM_APP);
      }
    }
    if (status === UNHEALTHY) {
      logger.info(`${type} ${envName} deployment ${deploymentId} unhealthy`);
      if (sleepTags) {
        await addSleepTagsToEc2(envName);
        await updateSleepTagsForAppEnvironment(envName, true);
      }
      return;
    }
  } catch (e) {
    logger.error('ERR-CICD-CMD-041', 'failed to deploy applications, unknown errors, please investigate.', e);
    const allApplications = formatApplications(await getApplicationsByEnvAndType(envName, type));
    const pendingApplications = allApplications.filter((item) => item.status === PipelineStatus.PENDING || item.status === PipelineStatus.DEPLOYING);
    for (const pendingApplication of pendingApplications) {
      await updateApplicationDeployStatus(pendingApplication.key, PipelineStatus.FAILED);
    }

    await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
    if (type === ApplicationType.DAE) {
      await updateAppEnvironmentStatus(envName, UNHEALTHY);
    }
    if (type === ApplicationType.DDE) {
      await updateDataEnvironmentStatus(envName, UNHEALTHY);
    }
    if (sleepTags) {
      await addSleepTagsToEc2(envName);
      await updateSleepTagsForAppEnvironment(envName, true);
    }
    return;
  }
  if (reindex) {
    try {
      await updateAppEnvironmentStatus(envName, INDEXING);
      const jobs = await reindexAllOpensearch(envName);
      const timeoutReached = await waitReindexingJobsCompleted(jobs, 600);
      if (timeoutReached) {
        logger.error('ERR-CICD-CMD-044', 'failed to wait for reindexing jobs completed');
        await updateAppEnvironmentStatus(envName, INDEXING_FAILED);
        await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
        if (sleepTags) {
          await addSleepTagsToEc2(envName);
          await updateSleepTagsForAppEnvironment(envName, true);
        }
        return;
      }
    } catch (e) {
      logger.error('ERR-CICD-CMD-043', 'failed to reindex opensearch', e);
      await updateAppEnvironmentStatus(envName, INDEXING_FAILED);
      await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
      if (sleepTags) {
        await addSleepTagsToEc2(envName);
        await updateSleepTagsForAppEnvironment(envName, true);
      }
      return;
    }
  }
  try {
    await appEnvTests(deploymentId, envName, type);
  } catch (e) {
    logger.error('ERR-CICD-CMD-042', 'failed to run tests', e);
    if (sleepTags) {
      await addSleepTagsToEc2(envName);
      await updateSleepTagsForAppEnvironment(envName, true);
    }
  }
  if (sleepTags) {
    await addSleepTagsToEc2(envName);
    await updateSleepTagsForAppEnvironment(envName, true);
  }
}
