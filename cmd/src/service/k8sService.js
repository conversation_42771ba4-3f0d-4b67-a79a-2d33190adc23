import { runK8sCmdSync } from 'common/util/cmdUtil.js';
import { sleep } from 'common/util/sleepUtil.js';
import { restartK8sEc2Instance } from './appEnvCreate.js';
import { getK8sIp } from '../util/ec2Util.js';

function isK8sApiServerAccessible(k8sIp) {
  try {
    const result = runK8sCmdSync(k8sIp, 'kubectl get nodes');
    logger.info('API server is accessible:', { result });
    return true;
  } catch (error) {
    logger.error('ERR-CICD-CMD-045', 'API server is not accessible:', error);
    return false;
  }
}

async function waitForK8sApiServer(k8sIp) {
  const intervalMinutes = 1;
  const timeoutMinutes = 30;
  const checkCount = timeoutMinutes / intervalMinutes;
  for (let i = 0; i < checkCount; i++) {
    const isAccessible = isK8sApiServerAccessible(k8sIp);
    if (isAccessible) {
      return true;
    }
    logger.info(`API server not accessible. Retrying in ${intervalMinutes} minute(s)...`);
    await sleep(intervalMinutes * 60 * 1000);
  }
  return false;
}

export async function waitK8sApiServerWithTimeout(envName) {
  const retryLimit = 3;
  const retryIntervalMinutes = 30;
  const k8sIp = getK8sIp(envName);
  let attempt = 1;
  while (attempt <= retryLimit) {
    logger.info(`Attempt ${attempt} of ${retryLimit}`);
    const isAccessible = isK8sApiServerAccessible(k8sIp);
    if (isAccessible) {
      return true;
    }
    await restartK8sEc2Instance(envName);
    const serverAvailable = await waitForK8sApiServer(k8sIp);
    if (serverAvailable) {
      logger.info('K8s API server is back online after restart.');
      return true;
    }
    if (attempt === retryLimit) {
      return false;
    }
    attempt++;
    await sleep(retryIntervalMinutes * 60 * 1000);
  }
  return false;
}
