import { listFiles, readFileToString, substringAfter } from 'common/util/fileUtil.js';
import {
  CICD_TF_BUCKET_PREFIX,
  DEV_TF_BUCKET_PREFIX,
  TF_ALLOWED_EXTENSIONS,
  TF_APP_TEMPLATE_FOLDER,
} from 'common/util/const.js';
import { createTfScript } from 'common/dal/tfScripts.js';
import getConfig from 'common/config/getConfig.js';
import { randomBytes } from 'crypto';
import { normalizeName } from 'common/util/appNameUtli.js';
import { ApplicationStack } from 'common/enums/applicationStack.js';
import { getApiKeyByName } from 'common/service/apiGateway.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import { tfApply, writeTfScriptsToDisk } from '../util/tfUtil.js';
import { DEV_AWS_ACCESS_KEY_ID, DEV_AWS_SECRET_ACCESS_KEY } from '../util/awsConfig.js';

export async function getTfScripts(appEnvironment, ecsApplications) {
  const includeQivs = appEnvironment.IncludeQivs?.BOOL;
  const includeQivsExternal = appEnvironment.IncludeQivsExternal?.BOOL;
  const includeMonarch = appEnvironment.Stacks.L.map((it) => it.S).includes(ApplicationStack.MONARCH);
  const includePublicWebsite = appEnvironment.Stacks.L.map((it) => it.S).includes(ApplicationStack.PUBLIC_WEBSITE);
  const includeReportGenerator = ecsApplications.some((it) => it === 'report-generator');

  logger.info(`includeQivs: ${includeQivs}, includeMonarch: ${includeMonarch}, includePublicWebsite: ${includePublicWebsite}, includeReportGenerator: ${includeReportGenerator}`);

  return listFiles(
    TF_APP_TEMPLATE_FOLDER,
    (dir, filename) => {
      if (filename === 'locals.tf') {
        return false;
      }
      // TODO remove this once we've implemented apply terraform for application
      if (!includeReportGenerator && filename.startsWith('report_generator_')) {
        return false;
      }
      if (!includeQivs && filename.startsWith('qivs_internal_')) {
        return false;
      }
      if (!includeQivsExternal && filename.startsWith('qivs_external_')) {
        return false;
      }
      if (!includePublicWebsite && filename.startsWith('qvconz_')) {
        return false;
      }
      if (!includeMonarch && filename.startsWith('monarch_')) {
        return false;
      }
      return TF_ALLOWED_EXTENSIONS.some((extension) => filename.endsWith(extension));
    },
  );
}

export async function applyTfScripts(appEnvironment, lambdaApplications, k8sApplications, ecsApplications, qvCustomDomains) {
  const env = appEnvironment.Name.S;
  const costCentre = appEnvironment.CostCentre.S;
  const files = await getTfScripts(appEnvironment, ecsApplications);
  logger.info(`result: ${JSON.stringify(files)}`);
  const appEnv = `app_${env}`;
  const taskName = getTaskName(env);
  logger.info(`task name: ${taskName}`);

  const groupedFiles = files.reduce((acc, file) => {
    if (file.includes('/cicd/')) {
      acc.cicd.push(file);
    } else if (file.includes('/dev/')) {
      acc.dev.push(file);
    } else if (file.includes('/modules/')) {
      acc.modules.push(file);
    }
    return acc;
  }, { cicd: [], dev: [], modules: [] });

  for (const file of groupedFiles.modules) {
    logger.info(`processing ${file}`);
    const path = `tf/${appEnv}${substringAfter(file, `${TF_APP_TEMPLATE_FOLDER}`)}`;
    const text = readFileToString(file);
    await createTfScript(appEnv, path, text);
  }
  await writeTfScriptsToDisk(appEnv);

  const replacements = {
    LAMBDA_APPLICATIONS: JSON.stringify(lambdaApplications),
    K8S_APPLICATIONS: JSON.stringify(k8sApplications),
    QV_CUSTOM_DOMAINS: JSON.stringify(qvCustomDomains),
    ENV: normalizeName(env),
    COST_CENTRE: costCentre,
    QIVS_AMI_ID: await getConfig('QIVS_AMI_ID'),
    DEV_ACCESS_KEY_ID: DEV_AWS_ACCESS_KEY_ID,
    DEV_SECRET_ACCESS_KEY: DEV_AWS_SECRET_ACCESS_KEY,
    TASK_DEFINITION_NAME: taskName,
    GITHUB_TOKEN: await getConfig('GITHUB_TOKEN'),
    QVCONZ_ENABLED: ecsApplications.some((it) => it === 'public-website'),
  };
  await writeAndApplyTfScripts(groupedFiles.dev, replacements, env, 'dev');

  const daeApiKeyName = `${normalizeName(env)}-dae-lambda-api-key`;
  const lambdaApiKey = await getApiKeyByName(daeApiKeyName);
  logger.info(`lambdaApiKey key: ${lambdaApiKey}`);
  replacements.DAE_API_KE = lambdaApiKey;
  await writeAndApplyTfScripts(groupedFiles.cicd, replacements, env, 'cicd');
}

async function writeAndApplyTfScripts(files, replacements, env, account) {
  runCmdSync('mkdir -p ~/.ssh');
  runCmdSync("aws ssm get-parameter --name /cicd/git-ssh-key --query 'Parameter.Value' --output text > ~/.ssh/id_rsa");
  runCmdSync('chmod 600 ~/.ssh/id_rsa');
  runCmdSync('ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts');
  const appEnv = `app_${env}`;
  for (const file of files) {
    logger.info(`processing ${file}`);
    let path = `tf/${appEnv}${substringAfter(file, `${TF_APP_TEMPLATE_FOLDER}`)}`;
    let text = readFileToString(file);
    if (path.endsWith('locals.tf.sample')) {
      Object.keys(replacements).forEach((key) => {
        text = text.replace(key, replacements[key]);
      });
      path = path.replace('.sample', '');
    }
    if (path.endsWith('base.tf')) {
      text = text.replace(`${DEV_TF_BUCKET_PREFIX}/app_template`, `${DEV_TF_BUCKET_PREFIX}/app_${env}`);
      text = text.replace(`${CICD_TF_BUCKET_PREFIX}/app_template`, `${CICD_TF_BUCKET_PREFIX}/app_${env}`);
    }
    await createTfScript(appEnv, path, text);
  }
  await writeTfScriptsToDisk(appEnv);
  await tfApply(appEnv, account);
}

function getTaskName(env) {
  const randomStr = randomBytes(4).toString('hex');
  return `report-generator-${normalizeName(env)}-${randomStr}`;
}
