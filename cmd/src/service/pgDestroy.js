import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { ERROR_CODES } from 'common/util/const.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import { updateDatastoreToDelete } from 'common/dal/datastores.js';
import { getDatabaseNames } from 'common/dal/datastoreStacks.js';
import { DataEnvServiceError } from '../util/errors.js';
import { destroyPostgresDb, destroyPostgresTb, terminateSessions } from '../dal/destroyPgObjects.js';
import { destroyFinishUpdateEnvironment } from './dataEnvironment.js';

export default async function pgDestroy(options) {
  const { dataEnv } = options;
  logger.info('start destroying mssql databases.', dataEnv);
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, dataStoreTypes.PGMonarch, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dbs = await getDatabaseNames(dataEnv, dataStoreTypes.PGMonarch);
  const tablespaceName = `${dataEnv}_tb`;

  for (const db of dbs) {
    const dbName = `${dataEnv}_${db}`;
    logger.info(`delete sessions for db ${dbName} started`);
    await terminateSessions(dbName);
    logger.info(`delete sessions for db ${dbName} finished`);
    logger.info(`delete db ${dbName} started`);
    await destroyPostgresDb(dbName);
    logger.info(`delete db ${dbName} finished`);

    const dsUniqueKey = `${dataEnv}-${dataStoreTypes.PGMonarch}-${db}`;
    await updateDatastoreToDelete(dsUniqueKey);
  }
  logger.info(`delete tb ${tablespaceName} started`);
  await destroyPostgresTb(tablespaceName);
  logger.info(`delete tb ${tablespaceName} finished`);

  await destroyFinishUpdateEnvironment(dataEnv);
  logger.info(`delete env ${dataEnv} successfully`);
}
