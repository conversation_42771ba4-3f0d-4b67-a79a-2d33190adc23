import { getApplicationsByEnvAndType, updateTestResultsForApplication } from 'common/dal/applications.js';
import { updateDeploymentStatus, updateTestResultsForDeployment } from 'common/dal/deployments.js';
import { updateAppEnvironmentStatus, updateTestResultsForAppEnvironment } from 'common/dal/appEnvironments.js';
import { accumulateTestResults, getDaeTestResult } from 'common/service/testReports.js';
import { DeploymentStatus } from 'common/enums/deploymentStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { filterEcsBuilds, removeEcsApplications, removeEcsBuilds } from 'common/dal/appStacks.js';
import { getBuildsByDeployment, updateBuildIdByKey, updateStatusByKey } from 'common/dal/deploymentBuilds.js';
import { getBuildStatus, startBuild } from 'common/service/codebuild.js';
import { sleep } from 'common/util/sleepUtil.js';
import { NO_TEST } from 'common/util/const.js';
import { testConfigParameterName } from 'common/util/ssmutil.js';
import { removeDaeSuffix } from 'common/util/appNameUtli.js';
import { HEALTHY, TESTING } from 'common/enums/environmentStatus.js';
import { updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import { updateTestResultsForDataEnvironment } from 'common/dal/dataEnvironments.js';
import { cicdS3Client } from '../util/awsConfig.js';

const CODEBUILD_CHECK_INTERVAL = 30 * 1000;
const CODEBUILD_CHECK_MAX_ATTEMPTS = 60;
const INIT_TEST_RESULT = {
  lintProblemsCount: 0,
  lintErrorsCount: 0,
  lintWarningsCount: 0,

  coverageLinesTotal: 0,
  coverageLinesCovered: 0,

  coverageStatementsTotal: 0,
  coverageStatementsCovered: 0,

  coverageFunctionsTotal: 0,
  coverageFunctionsCovered: 0,

  coverageBranchesTotal: 0,
  coverageBranchesCovered: 0,

  testPasses: 0,
  testPending: 0,
  testFailures: 0,
  testTests: 0,
};

export default async function appEnvTests(deploymentId, envName, type) {
  const deploymentBuilds = await getBuildsByDeployment(deploymentId);
  const codeBuildTestIds = await getCodeBuildTestIds(deploymentBuilds, deploymentId);
  await updateDeploymentStatus(deploymentId, DeploymentStatus.TESTS_RUNNING);
  if (type === ApplicationType.DAE) {
    await updateAppEnvironmentStatus(envName, TESTING);
  }
  if (type === ApplicationType.DDE) {
    await updateDataEnvironmentStatus(envName, TESTING);
  }
  const buildIds = await Promise.all(codeBuildTestIds);

  let attempts = 0;
  while (attempts < CODEBUILD_CHECK_MAX_ATTEMPTS) {
    attempts += 1;
    const updateDeploymentBuilds = (await getBuildsByDeployment(deploymentId)).filter((item) => item.Status.S === 'IN_PROGRESS');
    logger.info(`builds in progress ${updateDeploymentBuilds.length}`);
    if (updateDeploymentBuilds.length === 0) {
      break;
    }
    const buildsResults = await getBuildStatus(buildIds);
    for (const buildResult of buildsResults) {
      const { buildStatus, id } = buildResult;
      const build = updateDeploymentBuilds.find((item) => item.BuildId.S === id);
      logger.info('build', { build });
      if (build && buildStatus !== 'IN_PROGRESS') {
        await updateStatusByKey(build.Key.S, buildStatus);
      }
    }
    await sleep(CODEBUILD_CHECK_INTERVAL);
  }

  if (attempts === CODEBUILD_CHECK_MAX_ATTEMPTS) {
    logger.info('Some builds might still be in progress after maximum wait time.');
  }
  logger.info(`deploymentBuilds length ${deploymentBuilds.length}`);
  let deploymentAccumulatedTestResult = { ...INIT_TEST_RESULT };
  for (const deploymentBuild of deploymentBuilds) {
    const application = deploymentBuild.Application.S;
    const testResults = await getDaeTestResult(cicdS3Client, application, envName, deploymentBuild.DeploymentId.S);
    await updateTestResultsForApplication(`${type}-${envName}-${application}`, testResults);
    deploymentAccumulatedTestResult = accumulateTestResults(deploymentAccumulatedTestResult, testResults);
  }
  await updateTestResultsForDeployment(deploymentId, deploymentAccumulatedTestResult);

  let accumulatedTestResult = { ...INIT_TEST_RESULT };
  let applications = await getApplicationsByEnvAndType(envName, type);
  applications = await removeEcsApplications(applications);
  const testResults = applications.map((application) => JSON.parse(application.TestResults?.S || '{}'));

  logger.info('testResults', { testResults });
  for (const testResult of testResults) {
    accumulatedTestResult = accumulateTestResults(accumulatedTestResult, testResult);
  }

  if (type === ApplicationType.DAE) {
    await updateTestResultsForAppEnvironment(envName, accumulatedTestResult);
  }
  if (type === ApplicationType.DDE) {
    await updateTestResultsForDataEnvironment(envName, accumulatedTestResult);
  }

  await updateDeploymentStatus(deploymentId, DeploymentStatus.TESTS_COMPLETED);

  if (type === ApplicationType.DAE) {
    await updateAppEnvironmentStatus(envName, HEALTHY);
  }
  if (type === ApplicationType.DDE) {
    await updateDataEnvironmentStatus(envName, HEALTHY);
  }
  logger.info(`${type} environment tests complete`);
}

async function getCodeBuildTestIds(deploymentBuilds, deployment) {
  const deploymentWithoutTests = await filterEcsBuilds(deploymentBuilds);
  const deploymentWithTests = await removeEcsBuilds(deploymentBuilds);

  for (const build of deploymentWithoutTests) {
    const application = build.Application.S;
    await updateBuildIdByKey(`${deployment}-${application}`, NO_TEST, 'SUCCEEDED');
  }

  return deploymentWithTests
    .map(async (build) => {
      const application = build.Application.S;
      const deployBranch = build.DeployBranch.S;
      const envName = build.Env.S;
      const envVars = [
        { name: 'ENV', value: `${envName}`, type: 'PLAINTEXT' },
        { name: 'TEST_CONFIG', value: testConfigParameterName(envName, application), type: 'PLAINTEXT' },
        { name: 'DEPLOYMENT_ID', value: `${deployment}`, type: 'PLAINTEXT' },
        { name: 'TZ', value: 'Pacific/Auckland', type: 'PLAINTEXT' },
      ];
      const buildId = await startBuild(removeDaeSuffix(application), deployBranch, envVars);
      await updateBuildIdByKey(`${deployment}-${application}`, buildId, 'IN_PROGRESS');
      logger.info('buildId', { buildId });
      return buildId;
    });
}
