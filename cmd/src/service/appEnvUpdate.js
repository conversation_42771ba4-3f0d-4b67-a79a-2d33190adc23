import { ERROR_CODES } from 'common/util/const.js';
import { getAppEnvironmentByName, updateAppEnvironmentStatus } from 'common/dal/appEnvironments.js';
import { getDaeApplicationsByEnv } from 'common/dal/applications.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { STABLE } from 'common/enums/environmentStatus.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { createConfigsForApps } from './deployApplications.js';
import { AppEnvServiceError } from '../util/errors.js';
import { cicdS3Client, cicdSsmClient, devSsmClient } from '../util/awsConfig.js';
import { addSleepTagsToEc2, removeSleepTagsFromEc2 } from '../util/ec2Util.js';

export default async function appEnvUpdate(options) {
  const { appEnv } = options;
  const queryResults = await getAppEnvironmentByName(appEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, appEnv);
    throw new AppEnvServiceError(appEnv, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const appEnvironment = queryResults[0];
  const allApplications = await getDaeApplicationsByEnv(appEnv);
  await createConfigsForApps(allApplications, appEnvironment, ApplicationType.DAE, cicdSsmClient, cicdS3Client, devSsmClient);

  const pendingApplications = allApplications.filter((item) => item.Status.S === PipelineStatus.PENDING);
  logger.info(`pending applications: ${pendingApplications.length}}`);

  const sleepTags = appEnvironment.SleepTags?.BOOL;
  if (sleepTags) {
    await addSleepTagsToEc2(appEnv);
  } else {
    await removeSleepTagsFromEc2(appEnv);
  }
  if (pendingApplications.length === 0) {
    logger.info('no deployment is required,update app env task finished');
    await updateAppEnvironmentStatus(appEnv, STABLE);
    return;
  }

  const deploymentId = await initDeployment(appEnv, ApplicationType.DAE, pendingApplications.map((app) => app.Name.S).join(','), 'app-env-update');
  logger.info(`deploymentId ${deploymentId} initiated`);
  await updateAppEnvironmentStatus(appEnv, STABLE);

  logger.info('update app env task finished');
}
