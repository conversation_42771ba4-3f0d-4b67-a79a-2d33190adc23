import { deleteTfScriptsByEnv } from 'common/dal/tfScripts.js';
import { AWS_ACCOUNTS, DEV_TF_BUCKET_PREFIX } from 'common/util/const.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import { tfDestroy, writeTfScriptsToDisk } from '../util/tfUtil.js';
import { DEV_AWS_CREDENTIALS } from '../util/awsConfig.js';

export default async function dataInfraDestroy(options) {
  const { dataEnv } = options;
  AWS_ACCOUNTS.forEach((account) => {
    runCmdSync(`mkdir -p tf/data_${dataEnv}/${account}`);
  });
  await writeTfScriptsToDisk(`data_${dataEnv}`);
  for (let i = 0; i < AWS_ACCOUNTS.length; i++) {
    const account = AWS_ACCOUNTS[i];
    await tfDestroy(`data_${dataEnv}`, account);
  }
  await deleteTfScriptsByEnv(`data_${dataEnv}`);

  const deleteTfS3Bucket = `${DEV_AWS_CREDENTIALS} aws s3 rm s3://qv-dynamic-environments-terraform/${DEV_TF_BUCKET_PREFIX}/data_${dataEnv}`;
  const s3Result = runCmdSync(deleteTfS3Bucket);
  logger.info(`deleteTfS3Bucket result: ${s3Result}`);
  logger.info(`delete env ${dataEnv} disk successfully`);
}
