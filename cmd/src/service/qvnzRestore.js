import {
  DB_SCRIPTS, DB_SCRIPTS_GIT_REPO, ERROR_CODES, QVNZ_PUBLIC,
} from 'common/util/const.js';
import { getDataEnvironmentByName, updateSqlConfigByName } from 'common/dal/dataEnvironments.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import getConfig from 'common/config/getConfig.js';
import { getDatabaseNames, getDynamicDbFile } from 'common/dal/datastoreStacks.js';
import { createMssqlLogins } from '../dal/createMssqlObjects.js';
import { restoreFinishUpdateEnvironment } from './dataEnvironment.js';
import dynamicDbRestore from './mssqlDynamicDbRestore.js';
import qvnzPublicRestore from './qvnzPublicRestore.js';
import staticDbRestore from './mssqlStaticDbRestore.js';
import { destroyDb } from '../dal/destroyMssqlObjects.js';
import { DataEnvServiceError } from '../util/errors.js';
import waitInfraReady from '../util/waitInfraReady.js';

export default async function qvnzRestore(options) {
  const { dataEnv } = options;
  const { datastore } = options;
  logger.info(`restore QVNZ  started: ${dataEnv} ${datastore}`);
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, datastore, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dataEnvironment = queryResults[0];
  await cloneDbScriptsRepo(dataEnvironment);
  logger.info(dataEnvironment);
  await waitInfraReady(dataEnv);
  logger.info('infra ready');

  const drive = dataEnvironment.Drive.S;
  if (!datastore) { // skip when restoring single db
    const sqlConfig = await createMssqlLogins(dataEnv);
    await updateSqlConfigByName(dataEnv, sqlConfig);
    await staticDbRestore(dataEnv);
  }
  let dbNames = await getDatabaseNames(dataEnv, dataStoreTypes.QIVS);
  if (datastore) {
    const dbName = `${dataEnv}_${datastore}`;
    logger.info(`destroy db  ${dbName} before restore`);
    await destroyDb(dbName);
    dbNames = [datastore];
  }
  logger.info('restore db', { dbNames });
  for (const dbName of dbNames) {
    try {
      if (dbName === QVNZ_PUBLIC) {
        await qvnzPublicRestore(dataEnv, drive);
      } else {
        const bakFiles = await getDynamicDbFile(dataEnv, dbName);
        logger.info('bakFiles', { bakFiles });
        if (bakFiles.length === 1) {
          const [key, value] = bakFiles[0];
          await dynamicDbRestore(value, drive, dataEnv, key);
        }
      }
    } catch (e) {
      console.error(e);
      throw new DataEnvServiceError(dataEnv, dbName, ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.message, ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.code);
    }
  }
  await restoreFinishUpdateEnvironment(dataEnv);
  logger.info('job finished');
}

export async function cloneDbScriptsRepo(dataEnvironment) {
  const branch = dataEnvironment.Branch.S;
  runCmdSync(`rm -rf ${DB_SCRIPTS}`);
  const gitCmd = `git clone -b ${branch} ${DB_SCRIPTS_GIT_REPO.replace('https://', `https://${await getConfig('GITHUB_TOKEN')}@`)}`;
  runCmdSync(gitCmd);
}
