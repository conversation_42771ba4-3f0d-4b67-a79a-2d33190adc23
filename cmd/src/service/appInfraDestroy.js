import { AWS_ACCOUNTS } from 'common/util/const.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import { tfDestroy, writeTfScriptsToDisk } from '../util/tfUtil.js';

export default async function appInfraDestroy(appEnv) {
  AWS_ACCOUNTS.forEach((account) => {
    runCmdSync(`mkdir -p tf/app_${appEnv}/${account}`);
  });
  await writeTfScriptsToDisk(`app_${appEnv}`);
  for (let i = 0; i < AWS_ACCOUNTS.length; i++) {
    const account = AWS_ACCOUNTS[i];
    await tfDestroy(`app_${appEnv}`, account);
  }

  logger.info(`delete env ${appEnv} disk successfully`);
}
