import { ERROR_CODES, QIVS_USER, REFRESH_INTERVAL_IN_MILLISECONDS } from 'common/util/const.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import {
  updateDatastoreLastUpdatedTimeAndPercentAndMessage,
  updateDatastoreStartTime,
  updateDatastoreToStable,
} from 'common/dal/datastores.js';
import { sleep } from 'common/util/sleepUtil.js';
import { getDatabaseNames } from 'common/dal/datastoreStacks.js';
import { recreateSynonym, setRecoverySimple } from '../dal/createMssqlObjects.js';
import { assignRole, setUpUserRole } from '../dal/assignPermission.js';
import {
  getBakFileInfo, getRestoreHistory, getRestoreStatus, restoreFromBakFile,
} from '../dal/restoreStatus.js';
import { DataEnvServiceError } from '../util/errors.js';
import { formatDuration } from '../util/dateutil.js';
import { restoreFinishUpdateEnvironment } from './dataEnvironment.js';
import { executeSqlScripts } from '../util/sqlUtil.js';
import { dropSchemas, dropUsers } from '../dal/destroyMssqlObjects.js';
import { sanitiseDb } from '../dal/sanitise.js';
import { getAppUsers } from '../util/qvUserUtil.js';

export default async function dynamicDbRestore(value, drive, dataEnv, key) {
  const users = await getAppUsers();
  const bakFileInfo = await getBakFileInfo(value);
  if (bakFileInfo.length === 0) {
    logger.error(`bak file not exist ${value}`);
    throw new DataEnvServiceError(ERROR_CODES.BAK_FILE_NOT_EXIST.code, ERROR_CODES.BAK_FILE_NOT_EXIST.message);
  }
  const restoreStart = Date.now();
  restoreFromBakFile(bakFileInfo, drive, dataEnv, key, value)
    .then((r) => {
      logger.info('restore finished');
      logger.info(r);
    })
    .catch(async (error) => {
      logger.info('restore fail');
      logger.error(ERROR_CODES.MSSQL_DB_ERROR.code, ERROR_CODES.MSSQL_DB_ERROR.message, error);
      throw new DataEnvServiceError(dataEnv, key, ERROR_CODES.MSSQL_DB_ERROR.code, ERROR_CODES.MSSQL_DB_ERROR.message);
    });
  const dsUniqueKey = `${dataEnv}-${dataStoreTypes.QIVS}-${key}`;
  await updateDatastoreStartTime(dsUniqueKey);

  while (true) {
    let restoreStatusResults;
    try {
      restoreStatusResults = await getRestoreStatus();
    } catch (e) {
      console.error(e);
      if (e.code === 'ETIMEOUT') {
        logger.info('restore status timeout');
        restoreStatusResults = { recordset: [] };
      }
    }
    if (restoreStatusResults.recordset.length > 0) {
      const restoreStatus = restoreStatusResults.recordset[0];
      const estimatedCompletionTime = formatDuration(parseInt(restoreStatus.estimated_completion_time, 10));
      const percentComplete = restoreStatus.percent_complete.toFixed(2);
      const message = `Restoring ${dataEnv}_${key},${percentComplete}% completed, ${estimatedCompletionTime} to completed).`;
      await updateDatastoreLastUpdatedTimeAndPercentAndMessage(dsUniqueKey, message, percentComplete);
    } else {
      const history = await getRestoreHistory();
      logger.info('restore history', { result: history });
      const historyResult = history.recordset[0];
      const destinationDatabaseName = historyResult.destination_database_name;
      const dbName = `${dataEnv}_${key}`;
      logger.info(`destinationDatabaseName ${destinationDatabaseName} , dbName ${dbName}`);

      if (destinationDatabaseName === dbName) {
        const restoreCompletedAt = Date.now();
        let message = `Restoring ${dbName} completed in ${formatDuration(restoreCompletedAt - restoreStart)}.`;
        await updateDatastoreLastUpdatedTimeAndPercentAndMessage(dsUniqueKey, message, '100');
        logger.info(`restore history match, restore completed. current db ${dbName}`);
        await setRecoverySimple(dataEnv, key);
        logger.info(`restore ${value} finished`);
        await recreateSynonym(dataEnv, key);
        logger.info('recreate synonyms finished');
        await dropSchemas(dataEnv, key);
        await dropUsers(dataEnv, key);
        await setUpUserRole(dataEnv, `${dataEnv}_${key}`);
        await createQivsUserOnDynamicDb(dataEnv, key);
        await executeSqlScripts(dataEnv, key);
        logger.info('assign permissions finished');
        message += ` db-scripts completed in ${formatDuration(Date.now() - restoreCompletedAt)}.`;
        await sanitiseDb(dataEnv, key, users);
        await updateDatastoreToStable(dsUniqueKey, message);
        await restoreFinishUpdateEnvironment(dataEnv);
        break;
      } else {
        logger.info(`restore history not match, restore terminated abnormally. current db ${dbName}`, { result: historyResult });
        throw new DataEnvServiceError(dataEnv, key, ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.code, ERROR_CODES.RESTORE_TERMINATED_ABNORMALLY.message);
      }
    }
    await sleep(REFRESH_INTERVAL_IN_MILLISECONDS);
  }
}

export async function createQivsUserOnDynamicDb(dataEnv, datastore) {
  const databaseNames = await getDatabaseNames(dataEnv, dataStoreTypes.QIVS);
  const qivsUserDbs = databaseNames.filter((dbName) => ['qvnz', 'qvnz_history'].includes(dbName));
  if (qivsUserDbs.includes(datastore)) {
    await assignRole(`${dataEnv}_${datastore}`, ['db_owner'], [`${dataEnv}_${QIVS_USER}`]);
  }
}
