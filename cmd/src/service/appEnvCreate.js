import {
  ERROR_CODES,
  K8S_CONFIG,
  M<PERSON><PERSON><PERSON>_K8S_BUCKET,
  QIVS_EX,
  QIVS_IN,
  QV_CONTENT_BUCKET_DAE,
  QV_CONTENT_BUCKET_PROD,
  QV_CONTENT_DIRECTORIES,
  TMP_FOLDER,
} from 'common/util/const.js';
import {
  joinDomainToDev,
  reNameComputer,
  runCmdSync,
  runRemoteLinuxCmdOnQivs,
  runRemotePowerShellCmdOnQivs,
  runRemotePowerShellSingleQuoteCmdOnQivs,
  scpFromQivs,
  scpToQivs,
} from 'common/util/cmdUtil.js';
import {
  getAppEnvironmentByName,
  getStacks,
  updateAppEnvironmentStatus,
  updateAppEnvironmentTfDeterminedConfig,
} from 'common/dal/appEnvironments.js';
import { STABLE } from 'common/enums/environmentStatus.js';
import { getTfDeterminedConfig } from 'common/service/getAppEnvConfig.js';
import { sleep } from 'common/util/sleepUtil.js';
import { getDaeApplicationsByEnv } from 'common/dal/applications.js';
import { cloneBucket, uploadText } from 'common/util/s3util.js';
import { normalizeName } from 'common/util/appNameUtli.js';
import path from 'path';
import { readFile, writeFile } from 'fs/promises';
import getConfig from 'common/config/getConfig.js';
import fs from 'fs';
import { writeToFile } from 'common/util/fileUtil.js';
import { groupApplicationByPlatform } from 'common/dal/appStacks.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { initDeployment } from 'common/service/initDeployment.js';
import { ApplicationStack } from 'common/enums/applicationStack.js';
import { getDaeContext } from 'common/service/getContext.js';
import { DEV_AWS_CREDENTIALS, devS3Client } from '../util/awsConfig.js';
import { AppEnvServiceError } from '../util/errors.js';
import { applyTfScripts } from './applyTfScripts.js';
import { createAuth0Config, getAuth0Token, getPropertyServiceClientId } from '../util/auth0Util.js';
import {
  getEc2IpByName,
  getK8sIp,
  getKafkaIp,
  getQivsExternalEc2Id,
  getQivsExternalIp,
  getQivsInternalEc2Id,
  getQivsInternalIp,
} from '../util/ec2Util.js';
import { getNextAvailableIp } from '../util/ipUtil.js';
import { getQivsSecondNetwork, networkName } from '../util/qivsUtil.js';
import { replaceEmails, replaceUrls } from '../util/wagtailUtil.js';
import { getQvCustomAccess, updateQvCustomAccess } from '../dal/qvCustomAccess.js';
import { updateQvUser } from '../dal/qvUser.js';

const EC2_STOP_STATUS_MAX_ATTEMPTS = 60;
const EC2_STOP_STATUS_CHECK_INTERVAL = 10 * 1000;

const EC2_READY_STATUS_MAX_ATTEMPTS = 60;
const EC2_READY_STATUS_CHECK_INTERVAL = 20 * 1000;

async function clonePublicWebsiteS3(appEnvironment) {
  const stacks = getStacks(appEnvironment);
  logger.info(`stacks ${stacks}`);
  const includePublicWebsite = stacks.includes(ApplicationStack.PUBLIC_WEBSITE);
  if (!includePublicWebsite) {
    logger.info('public website is not included');
    return;
  }
  logger.info('clone public website S3');
  const env = appEnvironment.Name.S;
  await cloneBucket(devS3Client, QV_CONTENT_BUCKET_PROD, `${env}-${QV_CONTENT_BUCKET_DAE}`, QV_CONTENT_DIRECTORIES);

  const alternativeDomainName = `content.${env}.qvconz.internal.quotablevalue.co.nz`;
  const getCloudfrontCmd = `${DEV_AWS_CREDENTIALS} aws cloudfront list-distributions --query 'DistributionList.Items[?Aliases.Items[?@ == \`${alternativeDomainName}\`]].Id' --output text`;
  const distributionId = (await runCmdSync(getCloudfrontCmd)).replace(/\n/g, '');
  const invalidationCmd = `${DEV_AWS_CREDENTIALS} aws cloudfront create-invalidation --distribution-id ${distributionId} --paths "/*"`;
  await runCmdSync(invalidationCmd);
}

export async function configPublicWebsite(appEnvironment, newContextConfig) {
  const stacks = getStacks(appEnvironment);
  logger.info(`stacks ${stacks}`);
  const includePublicWebsite = stacks.includes(ApplicationStack.PUBLIC_WEBSITE);
  if (!includePublicWebsite) {
    logger.info('public website is not included');
    return;
  }
  logger.info('configuring public website');
  const dataEnv = appEnvironment.DataEnv.S;
  const appEnv = appEnvironment.Name.S;
  const dbName = `${dataEnv}_qvconz`;
  await replaceUrls(appEnv, dbName);
  const email = newContextConfig.CB_FROM_EMAIL;
  await replaceEmails(email, dbName);
}

function getApiKey(daeApiKeyName, appEnv) {
  const getApiKeyCmd = `${DEV_AWS_CREDENTIALS} aws apigateway get-api-keys --name-query "${daeApiKeyName}" --include-values`;
  const keys = JSON.parse(runCmdSync(getApiKeyCmd));
  if (keys.items.length === 0) {
    throw new Error(`No api key found for ${appEnv}`);
  }
  const apiKey = keys.items[0].value;
  logger.info(`api key: ${apiKey}`);
  return apiKey;
}

export default async function appEnvCreate(options) {
  const { appEnv, reindex, sleepTags } = options;
  const queryResults = await getAppEnvironmentByName(appEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, appEnv);
    throw new AppEnvServiceError(appEnv, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const appEnvironment = queryResults[0];

  const existingApplications = await getDaeApplicationsByEnv(appEnv);

  const {
    lambdaApplications,
    ecsApplications,
    k8sApplications,
  } = await groupApplicationByPlatform(existingApplications);

  let customAccessEntries = [];
  let customDomains = [];
  const dataEnvName = appEnvironment.DataEnv.S;
  if (appEnvironment.IncludeQivsExternal?.BOOL) {
    customAccessEntries = await getQvCustomAccess(`${dataEnvName}_qvnz`, appEnv);
    customDomains = customAccessEntries.map(((entry) => entry.newUrl));
    customDomains.push(`${normalizeName(appEnv)}.qivs.external.quotablevalue.co.nz`);
    logger.info('customDomains', { customDomains });
    await updateQvCustomAccess(`${dataEnvName}_qvnz`, customAccessEntries);
    await updateQvUser(`${dataEnvName}_qvnz`);
  }

  await applyTfScripts(appEnvironment, lambdaApplications.map((app) => app.Name.S), k8sApplications.map((app) => app.Name.S), ecsApplications.map((app) => app.Name.S), customDomains);
  // wait for k8s to be ready
  await sleep(60 * 1000);
  await uploadK8sAdminConfigToS3(appEnv);

  await restartK8sEc2Instance(appEnv);

  const daeApiKeyName = `${normalizeName(appEnv)}-dae-lambda-api-key`;
  const daeApiKey = getApiKey(daeApiKeyName, appEnv);
  logger.info(`daeApiKey key: ${daeApiKey}`);

  const kafkaIp = getKafkaIp(appEnv);
  const privateAlbDnsName = getPrivateAlbDnsName(appEnv);
  const tfDeterminedConfig = await getTfDeterminedConfig(daeApiKey, kafkaIp, privateAlbDnsName);
  await updateAppEnvironmentTfDeterminedConfig(appEnv, tfDeterminedConfig);
  const contextConfig = await getDaeContext(appEnv);

  await configQivsInternal(appEnvironment, contextConfig);
  await configQivsExternal(appEnvironment, contextConfig, customDomains);
  await clonePublicWebsiteS3(appEnvironment, contextConfig);
  await configPublicWebsite(appEnvironment, contextConfig);
  const daeAuthAccount = 'test';
  const appClientId = await getPropertyServiceClientId(daeAuthAccount);
  const tokenData = await getAuth0Token(daeAuthAccount);
  await createAuth0Config(tokenData.access_token, appClientId, appEnv, daeAuthAccount);
  await updateAppEnvironmentStatus(appEnv, STABLE);
  const deploymentId = await initDeployment(appEnv, ApplicationType.DAE, '', 'app-env-create', reindex, sleepTags);
  logger.info(`deploymentId ${deploymentId} initiated`);

  logger.info('job finished');
}

function getPrivateAlbDnsName(appEnv) {
  const cmd = `${DEV_AWS_CREDENTIALS} aws elbv2 describe-load-balancers --names "Monarch-${normalizeName(appEnv)}-PrivateALB" --query 'LoadBalancers[0].DNSName' --output text`;
  return runCmdSync(cmd).trim();
}

async function uploadK8sAdminConfigToS3(env) {
  const k8sIp = getK8sIp(env);
  logger.info(`k8s ip: ${k8sIp}`);
  try {
    await checkHomeFiles(k8sIp);
  } catch (e) {
    throw new Error('cannot access files in home directory');
  }

  const sshCmd = `ssh -i cicd-managed.pem -o StrictHostKeyChecking=no  ubuntu@${k8sIp} 'cat ${K8S_CONFIG}'`;
  logger.info(`sshCmd: ${sshCmd}`);
  const k8sAdminConf = runCmdSync(sshCmd).trim();
  logger.info(`adminConf: ${k8sAdminConf}`);
  const result = await uploadText(devS3Client, MONARCH_K8S_BUCKET, `${normalizeName(env)}/${K8S_CONFIG}`, k8sAdminConf);
  logger.info(`uploadText result: ${JSON.stringify(result)}`);
}

// check if files are ready in home directory
async function checkHomeFiles(ip) {
  const sshCmdLs = `ssh -i cicd-managed.pem -o StrictHostKeyChecking=no ubuntu@${ip} 'ls'`;
  for (let i = 0; i < 10; i++) {
    const homeFiles = runCmdSync(sshCmdLs);
    if (homeFiles && homeFiles.trim().length > 0) {
      logger.info(`home files: ${homeFiles}`);
      return homeFiles;
    }
    logger.info(' wait for delay and retry');
    await sleep(60 * 1000);
  }
  throw new Error('No files found after 10 retries');
}

export function getEc2Status(ec2InstanceId) {
  const statusCmd = `${DEV_AWS_CREDENTIALS} aws ec2 describe-instances --instance-ids ${ec2InstanceId} --query 'Reservations[].Instances[].State.Name' --output text`;
  return runCmdSync(statusCmd).replace(/\n/g, '');
}

function getK8sEc2Id(appEnv) {
  const cmd = `${DEV_AWS_CREDENTIALS} aws ec2 describe-instances --filters "Name=tag:Name,Values=Monarch-${normalizeName(appEnv)} Kubernetes" "Name=instance-state-name,Values=running" --query "Reservations[*].Instances[*].InstanceId" --output text`;
  return runCmdSync(cmd).replace(/\n/g, '');
}

function stopEc2(ec2InstanceId) {
  const stopCmd = `${DEV_AWS_CREDENTIALS} aws ec2 stop-instances --instance-ids ${ec2InstanceId}`;
  runCmdSync(stopCmd);
}

function startEc2(ec2InstanceId) {
  const startCmd = `${DEV_AWS_CREDENTIALS} aws ec2 start-instances --instance-ids ${ec2InstanceId}`;
  runCmdSync(startCmd);
}

async function restartEc2Instance(ec2InstanceId) {
  logger.info(`restarting ec2 ${ec2InstanceId}`);
  stopEc2(ec2InstanceId);
  logger.info(`ec2 ${ec2InstanceId} stopped`);
  let attempts = 0;
  while (attempts < EC2_STOP_STATUS_MAX_ATTEMPTS) {
    attempts += 1;
    const status = getEc2Status(ec2InstanceId);
    logger.info(`status ${status}`);

    if (status === 'stopped') {
      break;
    }
    await sleep(EC2_STOP_STATUS_CHECK_INTERVAL);
  }
  logger.info(`ec2 ${ec2InstanceId} starting`);
  startEc2(ec2InstanceId);

  attempts = 0;
  while (attempts < EC2_READY_STATUS_MAX_ATTEMPTS) {
    attempts += 1;
    const status = getEc2Status(ec2InstanceId);
    logger.info(`status ${status}`);
    if (status === 'running') {
      break;
    }
    await sleep(EC2_READY_STATUS_CHECK_INTERVAL);
  }
  logger.info(`ec2 ${ec2InstanceId} restarted`);
  // sleep for minutes to wait for ssh to be ready
  await sleep(2 * 60 * 1000);
}

export async function restartK8sEc2Instance(appEnv) {
  const ec2InstanceId = getK8sEc2Id(appEnv);
  await restartEc2Instance(ec2InstanceId);
}

export async function restartQivsInternalEc2Instance(appEnv) {
  const ec2InstanceId = getQivsInternalEc2Id(appEnv);
  await restartEc2Instance(ec2InstanceId);
}

export async function restartQivsExternalEc2Instance(appEnv) {
  const ec2InstanceId = getQivsExternalEc2Id(appEnv);
  await restartEc2Instance(ec2InstanceId);
}

export async function configQivsInternal(appEnvironment, contextConfig) {
  if (!appEnvironment.IncludeQivs.BOOL) {
    logger.info('qivs internal is not included');
    return;
  }
  const envName = appEnvironment.Name.S;
  const qivsIp = getQivsInternalIp(envName);
  const baseUrl = `${normalizeName(envName).toLowerCase()}.qivs.internal.quotablevalue.co.nz`;
  const serverName = QIVS_IN.toLowerCase();

  await addSecondNetworkInterface(appEnvironment, qivsIp);

  const renamed = await reNameComputer(qivsIp, `${normalizeName(envName).toUpperCase()}${QIVS_IN}`);
  if (renamed) {
    await restartQivsInternalEc2Instance(envName);
  }

  const joined = await joinDomainToDev(qivsIp);
  if (joined) {
    await restartQivsInternalEc2Instance(envName);
  }

  await configQvnzWeb(appEnvironment, contextConfig, baseUrl, serverName);
  await configRegistry(appEnvironment, contextConfig, baseUrl, serverName);
  await configIIS(appEnvironment, baseUrl, serverName);
  await restartQivsInternalEc2Instance(envName);
}

export async function configQivsExternal(appEnvironment, contextConfig, customDomains) {
  if (!appEnvironment.IncludeQivsExternal?.BOOL) {
    logger.info('qivs external is not included');
    return;
  }
  const envName = appEnvironment.Name.S;
  const qivsIp = getQivsExternalIp(envName);
  const baseUrl = `${normalizeName(envName).toLowerCase()}.qivs.external.quotablevalue.co.nz`;
  const serverName = QIVS_EX.toLowerCase();
  const renamed = await reNameComputer(qivsIp, `${normalizeName(envName).toUpperCase()}${QIVS_EX}`);
  if (renamed) {
    await restartQivsExternalEc2Instance(envName);
  }

  await configQvnzWeb(appEnvironment, contextConfig, baseUrl, serverName);
  await configRegistry(appEnvironment, contextConfig, baseUrl, serverName);
  await configIIS(appEnvironment, baseUrl, serverName, customDomains);
  await restartQivsExternalEc2Instance(envName);
  await configPermissions(qivsIp);
}

export async function configQvnzWeb(appEnvironment, mergedConfig, baseUrl, serveName) {
  const envName = appEnvironment.Name.S;
  const instanceName = `${(envName + serveName).toUpperCase()}`;
  const qivsIp = getEc2IpByName(instanceName);
  const qivsFilePath = 'C:/qvnz/web/QIVS/Web.config';
  let qivsWebConfigText = await runRemoteLinuxCmdOnQivs(qivsIp, `cat ${qivsFilePath}`);
  qivsWebConfigText = await updateQivsWebConfig(envName, qivsIp, qivsWebConfigText, mergedConfig, baseUrl, serveName);
  const qivsTempFilePath = path.join(TMP_FOLDER, 'Web.config');
  writeToFile(qivsWebConfigText, qivsTempFilePath);
  await scpToQivs(qivsIp, qivsTempFilePath, qivsFilePath);

  const soapFilePath = 'C:/QIVSNewWorld/QIVSWrapperSoap/Web.config';
  let soapWebConfigText = await runRemoteLinuxCmdOnQivs(qivsIp, `cat ${soapFilePath}`);
  soapWebConfigText = await updateSoapWebConfig(envName, qivsIp, soapWebConfigText, mergedConfig);
  const soapTmpFilePath = path.join(TMP_FOLDER, 'Web.config');
  writeToFile(soapWebConfigText, soapTmpFilePath);
  await scpToQivs(qivsIp, soapTmpFilePath, soapFilePath);
}

export function replaceConfig(text, configMap) {
  let replacedText = text;
  configMap.forEach((value, key) => {
    const regex = new RegExp(`<add\\s+key="${key}"\\s+value="[^"]*"`, 'i');
    replacedText = replacedText.replace(regex, `<add key="${key}" value="${value}"`);
  });
  return replacedText;
}

const REGISTRY_KEYS = [
  'APSServerName',
  'QivsBaseUrl',
  'QVVReportDemographicsURL',
  'QVVReportFilesURL',
  'QVVReportPhotosURL',
  'SoapWebserviceURL',
  'UserManagerSoapURL',
  'QVNZ',
];
const REGISTRY_PATHS = ['HKLM:\\\\SOFTWARE\\\\Wow6432Node\\\\Datacom\\\\QVNZ', 'HKLM:\\\\SOFTWARE\\\\Datacom\\\\QVNZ'];

export async function configRegistry(appEnvironment, mergedConfig, baseUrl, serveName) {
  const envName = appEnvironment.Name.S;
  const instanceName = `${(envName + serveName).toUpperCase()}`;
  const qivsIp = getEc2IpByName(instanceName);
  const {
    MSSQL_DB_HOST: mssqlDbHost,
    QIVS_MAIN_DB: qivsMainDb,
    QIVS_USER: qivsUser,
    QIVS_PASSWORD: qivsPassword,
  } = mergedConfig;
  const qvnzUrl = `Provider=SQLOLEDB;server=${mssqlDbHost};UID=${qivsUser};Pwd=${qivsPassword};Database=${qivsMainDb};APP=QVNZ_${normalizeName(envName)}`;
  const configMap = new Map([
    ['QVNZ', qvnzUrl],
  ]);
  for (const registryPath of REGISTRY_PATHS) {
    for (const registryKey of REGISTRY_KEYS) {
      const getValueCmd = `(Get-ItemProperty -Path '${registryPath}').${registryKey}`;
      let value = (await runRemotePowerShellCmdOnQivs(qivsIp, getValueCmd)).replace(/\s/g, '');
      if (configMap.has(registryKey)) {
        value = configMap.get(registryKey);
      } else {
        value = value.replace('testawsinweb01-qivs', baseUrl);
        if (registryKey !== 'SoapWebserviceURL' && registryKey !== 'UserManagerSoapURL') {
          logger.info(`https url: ${registryKey}`);
          value = value.replace('http:', 'https:');
        } else {
          logger.info(`http url: ${registryKey}, value ${value}`);
        }
      }
      const setValueCmd = `Set-ItemProperty -Path '${registryPath}' -Name '${registryKey}' -Value '${value}'`;
      await runRemotePowerShellCmdOnQivs(qivsIp, setValueCmd);
    }
  }
}

async function configIIS(appEnvironment, baseUrl, serveName, customDomains = []) {
  const envName = appEnvironment.Name.S;
  const instanceName = `${(normalizeName(envName) + serveName).toUpperCase()}`;
  const qivsIp = getEc2IpByName(instanceName);
  let secondQivsIp;
  // Update IIS bindings
  const existingQivsBinding = '***********:80:testawsinweb01-qivs';
  const setQivsBinding = `Set-WebBinding -Name "QIVS" -BindingInformation "${existingQivsBinding}" -PropertyName BindingInformation -Value "${qivsIp}:80:${baseUrl}"`;
  await runRemotePowerShellCmdOnQivs(qivsIp, setQivsBinding);

  for (const domain of customDomains) {
    const addBindingCmd = `New-WebBinding -Name "QIVS" -IPAddress "${qivsIp}" -Port 80 -HostHeader "${domain}"`;
    try {
      await runRemotePowerShellCmdOnQivs(qivsIp, addBindingCmd);
    } catch (e) {
      logger.error('ERR-CICD-CMD-030', `Error adding binding for ${domain}`, e);
    }
  }
  if (serveName === QIVS_IN.toLowerCase()) {
    secondQivsIp = getQivsSecondNetwork(envName).PrivateIpAddress;
    const existingSoapBinding = '***********:64206:soapwrappertest';
    const setSoapBindingCmd = `Set-WebBinding -Name "QIVSWrapperSoap" -BindingInformation "${existingSoapBinding}" -PropertyName BindingInformation -Value "${secondQivsIp}:64206:soapwrapper${envName}"`;
    await runRemotePowerShellCmdOnQivs(qivsIp, setSoapBindingCmd);
  }

  // Add gns.net to the 'Log on as a batch job' permission under Local Security Policy
  const exportConfigCmd = 'secedit /export /cfg C:\\secpol-bak.cfg';
  await runRemotePowerShellCmdOnQivs(qivsIp, exportConfigCmd);
  const tmp = '/tmp/secpol.cfg';
  await scpFromQivs(qivsIp, 'C://secpol-bak.cfg', tmp);
  await replaceTextInFile(tmp, /^SeBatchLogonRight.*/, 'gns.net');
  await scpToQivs(qivsIp, tmp, 'C:/secpol.cfg');
  const importConfigCmd = 'secedit /configure /db secedit.sdb /cfg C:\\secpol.cfg /areas USER_RIGHTS';
  await runRemotePowerShellCmdOnQivs(qivsIp, importConfigCmd);

  // Edit /etc/hosts
  const hostsTmp = '/tmp/hosts';
  const hosts = 'C:/Windows/System32/drivers/etc/hosts';
  await scpFromQivs(qivsIp, hosts, hostsTmp);
  let hostEntries = '';
  hostEntries = `
     \r\n${qivsIp}  ${envName}${serveName}
     \r\n${qivsIp}  ${envName}${serveName}.qv.dev
     \r\n${qivsIp}  ${baseUrl}
 `;
  customDomains.forEach((domain) => {
    hostEntries += `\r\n${qivsIp}  ${domain}`;
  });
  if (secondQivsIp) {
    hostEntries += `\r\n${secondQivsIp}  soapwrapper${envName}`;
  }

  fs.appendFileSync(hostsTmp, hostEntries, (err) => {
    if (err) {
      console.error('Error appending to file:', err);
    } else {
      logger.info('Successfully appended to the file.');
    }
  });
  await scpToQivs(qivsIp, hostsTmp, hosts);

  // re-apply Identity of the Application Pool
  const gnsNetPassword = await getConfig('GNS_NET_PASSWORD');
  await runRemotePowerShellCmdOnQivs(qivsIp, 'Import-Module WebAdministration; Set-ItemProperty IIS:\\AppPools\\QIVS -Name processModel.identityType -Value 0');
  await runRemotePowerShellCmdOnQivs(qivsIp, `Import-Module WebAdministration; Set-ItemProperty IIS:\\AppPools\\QIVS -Name processModel.userName -Value 'gns.net'; Set-ItemProperty IIS:\\AppPools\\QIVS -Name processModel.password -Value '${gnsNetPassword}'; Set-ItemProperty IIS:\\AppPools\\QIVS -Name processModel.identityType -Value 3`);

  await runRemotePowerShellCmdOnQivs(qivsIp, 'iisreset');

  const setUsernameCmd = 'Set-WebConfigurationProperty -Filter \\"/system.applicationHost/sites/site[@name=\'\\\'\'QIVS\'\\\'\']/application[@path=\'\\\'\'/\'\\\'\']/virtualDirectory[@path=\'\\\'\'/PhotoImages\'\\\'\']\\" -Name \\"userName\\" -Value \\"gns.net\\"';
  await runRemotePowerShellSingleQuoteCmdOnQivs(qivsIp, setUsernameCmd);

  const directories = [
    'C:\\qvnz\\web\\QIVS',
    'C:\\qvnz\\web\\Images',
    'C:\\qvnz\\web\\Stylesheets',
  ];

  for (const dir of directories) {
    const grantPermissionCmd = `icacls "${dir}" --% /grant "gns.net:(OI)(CI)F" /T`;
    await runRemotePowerShellSingleQuoteCmdOnQivs(qivsIp, grantPermissionCmd);
  }

  try {
    await runRemotePowerShellCmdOnQivs(qivsIp, 'Stop-WebAppPool -Name "QIVS"');
  } catch (e) {
    logger.info('pool already stopped');
  }
  await runRemotePowerShellCmdOnQivs(qivsIp, 'Start-WebAppPool -Name "QIVS"');
}

async function replaceTextInFile(filePath, searchRegex, appendText) {
  const data = await readFile(filePath, 'utf16le');
  const result = data.replace(new RegExp(searchRegex, 'gm'), (match) => `${match},${appendText}`);
  await writeFile(filePath, result, 'utf16le');
}

export async function addSecondNetworkInterface(appEnvironment) {
  const envName = appEnvironment.Name.S;
  if (getQivsSecondNetwork(envName) != null) {
    logger.info('second network interface already added');
    return;
  }
  const qivsIp = getQivsInternalIp(envName);
  const nextIp = await getNextAvailableIp(qivsIp);
  const subnetId = 'subnet-5df45139';
  const securityGroups = ['sg-cc7ac1ab', 'sg-0ca7ed62c9df85784', 'sg-eb77768c', 'sg-0999116e', 'sg-4f880028', 'sg-095d3412548277e29', 'sg-3d55db5a', 'sg-0f56c5c6f77d97bee'];
  const createNetworkCmd = `${DEV_AWS_CREDENTIALS} aws ec2 create-network-interface --subnet-id ${subnetId} --private-ip-address ${nextIp} --groups ${securityGroups.join(' ')} --description "${networkName(envName)}"`;
  runCmdSync(createNetworkCmd);
  const networkInterfaceId = getQivsSecondNetwork(envName).NetworkInterfaceId;

  const ec2InstanceId = getQivsInternalEc2Id(envName);
  logger.info(`ec2InstanceId: ${ec2InstanceId}`);

  const attachCmd = `${DEV_AWS_CREDENTIALS} aws ec2 attach-network-interface --network-interface-id ${networkInterfaceId} --instance-id ${ec2InstanceId} --device-index 2`;
  runCmdSync(attachCmd);
}

export async function updateQivsWebConfig(envName, qivsIp, webConfigText, mergedConfig, baseUrl, serveName) {
  const qivsBaseUrl = `https://${baseUrl}`;
  let newWebConfigText = webConfigText;
  const qivsServerName = `${normalizeName(envName).toLowerCase()}${serveName}`;

  newWebConfigText = newWebConfigText.replaceAll('testawsinweb01', qivsServerName);
  const {
    MSSQL_DB_HOST: mssqlDbHost,
    QIVS_MAIN_DB: qivsMainDb,
    QIVS_USER: qivsUser,
    QIVS_PASSWORD: qivsPassword,
    MONARCH_WEB_URL: monarchWebUrl,
    AUTH0_CLIENT_ID: auth0ClientId,
    AUTH0_DOMAIN: auth0Domain,
  } = mergedConfig;
  newWebConfigText = newWebConfigText.replaceAll('https://test.qvmonarch.co.nz', monarchWebUrl);
  const qvnzUrl = `server=${mssqlDbHost};uid=${qivsUser};Pwd=${qivsPassword};database=${qivsMainDb}`;
  const qvnzResourcesUrl = `server=${mssqlDbHost};uid=${qivsUser};Pwd=${qivsPassword};database=qvnz_resources`;
  const esamUrl = `server=${mssqlDbHost};uid=${qivsUser};Pwd=${qivsPassword};database=esam`;
  const configMap = new Map([
    ['dbConnQIVS', qvnzUrl],
    ['dbConnMain', qvnzUrl],
    ['dbConnLoggingDB', qvnzUrl],
    ['dbConnResources', qvnzResourcesUrl],
    ['dbConnEsam', esamUrl],
    ['Auth0Domain', auth0Domain],
    ['Auth0ClientId', auth0ClientId],
    ['WebServerIPAddress', qivsIp],
    ['BaseURL', qivsBaseUrl],
    ['LocalHostURL', qivsBaseUrl],
    ['BaseQIVSInternalURL', qivsBaseUrl],
  ]);

  newWebConfigText = replaceConfig(newWebConfigText, configMap);
  return newWebConfigText;
}

export async function updateSoapWebConfig(envName, qivsIp, webConfigText, mergedConfig) {
  let text = webConfigText;
  const {
    MONARCH_SERVICES_URL: monarchServicesUrl,
  } = mergedConfig;
  const configMap2 = new Map([
    ['propertyUriStringValue', monarchServicesUrl],
    ['photoMetaDataUriStringValue', monarchServicesUrl],
    ['updateMetaDataUriStringValue', monarchServicesUrl],
  ]);
  text = replaceConfig(text, configMap2);
  return text;
}

export async function configPermissions(qivsIp) {
  const setAnonymousAuthenticationCmd = 'Set-WebConfigurationProperty -filter "/system.webServer/security/authentication/anonymousAuthentication"  -PSPath "IIS:/" -location "QIVS"  -Name "enabled" -Value "true"';
  try {
    await runRemotePowerShellSingleQuoteCmdOnQivs(
      qivsIp,
      setAnonymousAuthenticationCmd,
    );
  } catch (e) {
    logger.error('ERR-CICD-CMD-029', 'Error setting anonymous authentication', e);
  }
}
