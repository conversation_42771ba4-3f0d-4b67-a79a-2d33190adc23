import { QIVS_USER, STATIC_DB_BAK_FILE_LOCATION } from 'common/util/const.js';
import { assignRole, setUpUserRole } from '../dal/assignPermission.js';
import getConnection from '../dal/mssqlManager.js';

export default async function staticDbRestore(dataEnv) {
  for (const dbName of Array.from(STATIC_DB_BAK_FILE_LOCATION.keys())) {
    await setUpUserRole(dataEnv, dbName);
  }
  await createQivsUserOnStaticDbs(dataEnv);
  await msdb(dataEnv);
}

export async function createQivsUserOnStaticDbs(dataEnv) {
  const qivsUserDbs = ['ESAM', 'qvnz_resources'];
  for (const dbName of qivsUserDbs) {
    await assignRole(dbName, ['db_owner'], [`${dataEnv}_${QIVS_USER}`]);
  }
}

async function msdb(dataEnv) {
  await assignRole('msdb', [], [`${dataEnv}_monarch_services`]);
  const conn = await getConnection();
  const sql = `GRANT EXECUTE ON OBJECT::dbo.sp_send_dbmail TO ${dataEnv}_monarch_services`;
  logger.info(sql);
  await conn.query(sql);
}
