import {
  ARTIFACT_FILE_NAME, DB_SCRIPTS, DEPLOY_BUCKET_NAME, ERROR_CODES, SHA,
} from 'common/util/const.js';
import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { dataStoreTypes } from 'common/enums/dataStoreTypes.js';
import fs from 'fs';
import AdmZip from 'adm-zip';
import { deleteObjectIfManaged, downloadFile, uploadText } from 'common/util/s3util.js';

import { getDataStoresByEnv, updateDatastoreSha } from 'common/dal/datastores.js';
import { readFileToString } from 'common/util/fileUtil.js';
import { cicdS3Client } from '../util/awsConfig.js';
import { executeSqlScripts } from '../util/sqlUtil.js';
import { DataEnvServiceError } from '../util/errors.js';
import { generateSqlReport } from '../util/sqlReportUtil.js';

export default async function deployDbScripts(options) {
  const { dataEnv, datastore } = options;
  logger.info(`restore QVNZ started: ${options.dataEnv} , ${options.datastore}`);
  const queryResults = await getDataEnvironmentByName(dataEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, dataEnv);
    throw new DataEnvServiceError(dataEnv, dataStoreTypes.QIVS, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const dataEnvironment = queryResults[0];
  logger.info(dataEnvironment);
  const datastores = await getDataStoresByEnv(dataEnv);
  const ds = datastores.find((item) => item.Name.S === datastore);
  if (!ds) {
    logger.error(ERROR_CODES.NO_DATASTORE_FOUND.code, ERROR_CODES.NO_DATASTORE_FOUND.message, null, datastore);
    return;
  }
  const bucketFolder = `${DB_SCRIPTS}/deploy/${dataEnv}/${datastore}`;
  const bucketKey = `${bucketFolder}/${ARTIFACT_FILE_NAME}`;
  logger.info('bucketKey', { bucketKey });
  const folder = `./${DB_SCRIPTS}`;
  logger.info('folder', { folder });
  const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
  fs.mkdirSync(folder, { recursive: true });
  await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);

  logger.info('unzip deploy.zip');
  const zip = new AdmZip(fileName, {});
  zip.extractAllTo(`${folder}/`, true);
  const sha = readFileToString(`${folder}/${SHA}`).trim();

  const sqlResults = await executeSqlScripts(dataEnv, datastore);
  logger.info('db scripts imported', sqlResults);
  const sqlReport = generateSqlReport(sqlResults);
  const reportFile = `${bucketFolder}/${sha}.txt`;
  await deleteObjectIfManaged(cicdS3Client, DEPLOY_BUCKET_NAME, reportFile);
  await uploadText(cicdS3Client, DEPLOY_BUCKET_NAME, reportFile, sqlReport);

  logger.info(`sha ${sha}`);
  await updateDatastoreSha(ds.Key.S, sha);
}
