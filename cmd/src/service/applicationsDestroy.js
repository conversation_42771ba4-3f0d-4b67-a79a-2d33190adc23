import { filterLambdaApplications, getPlatform } from 'common/dal/appStacks.js';
import { getPipelineName, normalizeName, removeDaeSuffix } from 'common/util/appNameUtli.js';
import {
  checkParameterExistence,
  deleteParameter,
  testConfigParameterName,
  tfConfigParameterName,
} from 'common/util/ssmutil.js';
import { runCmdSync, runCmdSyncWithRetry } from 'common/util/cmdUtil.js';
import { getApplicationsByEnvAndType } from 'common/dal/applications.js';
import { ARTIFACT_FILE_NAME, DEPLOY_BUCKET_NAME } from 'common/util/const.js';
import fs from 'fs';
import { downloadFile } from 'common/util/s3util.js';
import AdmZip from 'adm-zip';
import { dirname, join } from 'path';
import { Platform } from 'common/enums/platform.js';
import {
  cicdS3Client, cicdSsmClient, DEV_AWS_CREDENTIALS, devSsmClient,
} from '../util/awsConfig.js';
import { deleteStack } from './appEnvDestroy.js';

export async function deleteLambdaApplications(applications, appEnv, type) {
  if (await checkApiDomain(normalizeName(appEnv), type) === true) {
    await deleteApiMappings(normalizeName(appEnv), type);
  }
  const lambdaApplications = await filterLambdaApplications(applications);
  for (const lambdaApplication of lambdaApplications) {
    await deleteStack(lambdaApplication, appEnv);
    if (await checkApiDomain(appEnv, type) === true) {
      await deleteApiDomain(appEnv, type);
    }
  }
}

export async function deleteAppConfig(application, appEnv) {
  let appName = application.Name.S;
  appName = normalizeName(removeDaeSuffix(appName));
  if (await checkParameterExistence(devSsmClient, `/${appEnv}/${appName}`)) {
    await deleteParameter(devSsmClient, `/${appEnv}/${appName}`);
  }
}

export async function deleteTestConfig(application, appEnv) {
  const appName = application.Name.S;
  const appConfigName = removeDaeSuffix(testConfigParameterName(appEnv, appName));
  if (await checkParameterExistence(cicdSsmClient, appConfigName)) {
    await deleteParameter(cicdSsmClient, appConfigName);
  }
}

export async function deleteTfConfig(application, appEnv) {
  const appName = application.Name.S;
  const appConfigName = tfConfigParameterName(appEnv, appName);
  if (await checkParameterExistence(cicdSsmClient, appConfigName)) {
    await deleteParameter(cicdSsmClient, appConfigName);
  }
}

async function checkApiDomain(appEnv, type) {
  const apiDomainName = `${appEnv}.${type}.qvapi.co.nz`;
  const checkDomainNameCmd = `${DEV_AWS_CREDENTIALS} aws apigateway get-domain-names --query "items[?domainName=='${apiDomainName}']"`;
  const result = runCmdSync(checkDomainNameCmd).trim().replace(/\n/g, '');

  logger.info(`${result}`);
  if (result === '[]') {
    logger.info(`domain name ${apiDomainName} does not exist`);
    return false;
  }
  return true;
}
export function deleteRoute53Records(appEnv, hostedZoneId, type) {
  const cmd = `${DEV_AWS_CREDENTIALS} aws route53 list-resource-record-sets \
    --hosted-zone-id  ${hostedZoneId} \
    --query "ResourceRecordSets[?Name=='${appEnv}.${type}.qvapi.co.nz.']"`;
  const result = runCmdSync(cmd).replace(/\n/g, '');
  if (result === '[]') {
    logger.info(`No records found for ${appEnv}.${type}.qvapi.co.nz.`);
    return;
  }
  const records = JSON.parse(result);
  const deletePayload = {
    Comment: 'Deleting Alias record',
    Changes: records.map((record) => ({
      Action: 'DELETE',
      ResourceRecordSet: record,
    })),
  };

  const deleteCmd = `${DEV_AWS_CREDENTIALS}  aws route53 change-resource-record-sets --hosted-zone-id ${hostedZoneId} --change-batch '${JSON.stringify(deletePayload)}'`;
  runCmdSync(deleteCmd);
}
async function deleteApiDomain(appEnv, type) {
  const apiDomainName = `${appEnv}.${type}.qvapi.co.nz`;
  const deleteApiDomainCmd = `${DEV_AWS_CREDENTIALS} aws apigateway delete-domain-name --domain-name ${apiDomainName}`;
  await runCmdSyncWithRetry(deleteApiDomainCmd, 'TooManyRequests');
}

async function deleteApiMappings(appEnv, type) {
  const domainName = `${appEnv}.${type}.qvapi.co.nz`;
  const cmd = `${DEV_AWS_CREDENTIALS} aws apigatewayv2 get-api-mappings --domain-name ${domainName} --query 'Items[].ApiMappingId' --output text`;
  const idsText = runCmdSync(cmd).replace(/\n/g, '');
  logger.info(`api mapping ids: ${idsText}`);
  if (idsText === '') {
    return;
  }
  const ids = idsText.split(/\s+/);

  for (const id of ids) {
    const deleteApiMappingCmd = `${DEV_AWS_CREDENTIALS} aws apigatewayv2 delete-api-mapping --domain-name ${domainName} --api-mapping-id ${id}`;
    runCmdSync(deleteApiMappingCmd);
  }
}

export async function deleteS3Delivery(appEnv, applications) {
  for (const application of applications) {
    const appName = await getPipelineName(application);
    const deliveryS3Bucket = `aws s3 rm s3://qv-deployment/${appName}/deploy/${appEnv}/ --recursive`;
    const s3Result = runCmdSync(deliveryS3Bucket);
    logger.info(`deliveryS3Bucket result: ${s3Result}`);
  }
}

export async function deleteApplicationInfra(appEnv, type) {
  logger.info(`destroy application infra ${appEnv} started`);
  runCmdSync('mkdir -p ~/.ssh');
  runCmdSync("aws ssm get-parameter --name /cicd/git-ssh-key --query 'Parameter.Value' --output text > ~/.ssh/id_rsa");
  runCmdSync('chmod 600 ~/.ssh/id_rsa');
  runCmdSync('ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts');
  const applications = await getApplicationsByEnvAndType(appEnv, type);
  for (const application of applications) {
    const appName = application.Name.S;
    const platform = await getPlatform(appName);
    const normalizedAppName = platform === Platform.K8S
      ? (`monarch-${appName}`).replace(/^monarch-monarch-/, 'monarch-')
      : appName;

    const bucketKey = `${normalizedAppName}/deploy/${normalizeName(appEnv)}/${ARTIFACT_FILE_NAME}`;

    const folder = `tf/applications/${appName}`;
    const fileName = `${folder}/${ARTIFACT_FILE_NAME}`;
    fs.mkdirSync(folder, { recursive: true });
    try {
      await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);
      logger.info(`downloaded ${bucketKey} to ${fileName}`);
    } catch (e) {
      if (e.Code === 'NoSuchKey') {
        logger.info(`artifact ${bucketKey} doesn't exist, no deployment has been done, so no need to destroy infra`);
        continue;
      } else {
        throw e;
      }
    }
    logger.info('extracting infra from zip');
    await extractFolderFromZip(fileName, `${folder}/infra`, 'infra');

    if (fs.existsSync(`${folder}/infra`)) {
      logger.info(`getting tfvars for ${appName}`);
      const tfVarsPath = `/${appEnv}/${appName}/tfvars`;
      const getTfVarsCmd = `aws ssm get-parameter --name ${tfVarsPath} --query 'Parameter.Value' --output text > ${folder}/infra/config.tfvars`;
      try {
        runCmdSync(getTfVarsCmd);
      } catch (e) {
        logger.error('ERR-CMD-123', `Failed to get tfvars for ${appName}`, e);
        return;
      }

      const tfStatePath = `dev/${appEnv}/${appName}`;
      const destroyCmd = `cd ${folder}/infra && terraform init -backend-config="key=${tfStatePath}" --reconfigure && terraform destroy -var-file="config.tfvars" -auto-approve`;
      runCmdSync(destroyCmd);

      logger.info(`destroy infra for ${appName} finished`);
    }
  }
}

export async function extractFolderFromZip(zipFilePath, targetFolder, subFolder) {
  const zip = new AdmZip(zipFilePath, {});
  const entries = zip.getEntries();
  for (const entry of entries) {
    if (entry.entryName.startsWith(subFolder)) {
      const fullPath = join(targetFolder, entry.entryName.substr(subFolder.length));
      if (entry.isDirectory) {
        fs.mkdirSync(fullPath, { recursive: true });
      } else {
        fs.mkdirSync(dirname(fullPath), { recursive: true });
        const content = entry.getData();
        fs.writeFileSync(fullPath, content, {});
      }
    }
  }
}
