import { UpdateItemCommand } from '@aws-sdk/client-dynamodb';
import {
  APPLICATIONS,
  ARTIFACT_FILE_NAME,
  INFRA_SAMPLE_PATH,
  MONITOR_PIPELINE_STATUS_TIMEOUT,
} from 'common/util/const.js';
import { copyFile } from 'common/dal/appDeployArtifacts.js';
import {
  formatApplication,
  formatApplications,
  getApplicationByKey,
  getApplicationsByEnvAndType,
  updateApplicationDeployStatus, updateApplicationDeployStatusAndUserName,
} from 'common/dal/applications.js';
import { findBuildByKey } from 'common/dal/builds.js';
import { getTableName } from 'common/dal/getResourceName.js';
import { dynamoDbClient } from 'common/util/dynamodb.js';
import { PipelineStatus } from 'common/enums/pipelineStatus.js';
import { getAppEnvironmentByName, updateAppEnvironmentStatus } from 'common/dal/appEnvironments.js';
import { sleep } from 'common/util/sleepUtil.js';
import { getDataEnvironmentByName } from 'common/dal/dataEnvironments.js';
import { getApplicationsDependencies, getPlatform, removeEcsApplications } from 'common/dal/appStacks.js';
import { Platform } from 'common/enums/platform.js';
import { getDeployPipelineName, normalizeName } from 'common/util/appNameUtli.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { getDataStoresByEnv, updateDataEnvironmentStatus } from 'common/dal/datastores.js';
import {
  DEPLOYING, HEALTHY, STABLE, UNHEALTHY,
} from 'common/enums/environmentStatus.js';
import { monitorPipelineStatusWithTimeout } from 'common/service/codepipeline.js';
import { healthCheckResult, waitApplicationToBeHealthyWithTimeout } from 'common/service/healthCheck.js';
import { DeploymentStatus } from 'common/enums/deploymentStatus.js';
import { getApplicationsToDeploy } from 'common/service/initDeployment.js';
import { getDeploymentByDeploymentId, updateDeploymentStatus } from 'common/dal/deployments.js';
import { getApiKeyByName } from 'common/service/apiGateway.js';
import { saveDeploymentBuilds } from 'common/dal/deploymentBuilds.js';
import { getContext } from 'common/service/getContext.js';
import {
  createConfigIfSampleExist,
  createEcsConfig,
  createK8sTestConfig,
  createLambdaConfig,
  createLambdaTestConfig,
} from './lambdaConfigService.js';
import { waitK8sApiServerWithTimeout } from './k8sService.js';
import { deleteCloudFormationStack, getStackStatus, stackExist } from '../util/cloudformationUtil.js';
import { getDependencies } from '../util/serviceDependencies.js';

async function deleteStackIfRolledBack(application, envName) {
  const stackName = normalizeName(`${application.name}-${envName}`);
  const result = await stackExist(stackName);
  if (result) {
    const status = await getStackStatus(stackName);
    if (status === 'ROLLBACK_COMPLETE') {
      logger.info(`stack ${stackName} status is ${status}, delete the stack and redeploy`);
      await deleteCloudFormationStack(stackName);
    }
  }
}

async function waitForPipelineTriggered() {
  await sleep(5 * 1000);
}

async function waitForDeploymentPipelineCompleted() {
  await sleep(60 * 1000);
}

export async function deployApplications(deploymentId, envName, type, applicationName) {
  if (type === ApplicationType.DAE) {
    await updateAppEnvironmentStatus(envName, DEPLOYING);
  }
  // if type is DDE, wait  datastores to be ready, as /dde/context is not ready until datastores are ready
  if (type === ApplicationType.DDE) {
    await waitUntilDatastoresReady(envName);
    await updateDataEnvironmentStatus(envName, DEPLOYING);
  }
  const costCenter = await getCostCenterByEnv(envName, type);
  logger.info(`costCenter ${costCenter}`);
  const applicationsToDeploy = await getApplicationsToDeploy(applicationName, type, envName);

  const lambdaApps = [];
  const ecsApps = [];
  const k8sApps = [];
  const failedApps = [];
  await Promise.all(applicationsToDeploy.map(async (app) => {
    const platform = await getPlatform(app.Name.S);
    if (platform === Platform.LAMBDA) {
      lambdaApps.push(formatApplication(app));
    } else if (platform === Platform.ECS) {
      ecsApps.push(formatApplication(app));
    } else if (platform === Platform.K8S) {
      k8sApps.push(formatApplication(app));
    }
  }));

  lambdaApps.sort((a, b) => a.name.localeCompare(b.name));
  const apiKeyName = `${envName}-${type.toLowerCase()}-lambda-api-key`;
  const lambdaApiKey = await getApiKeyByName(apiKeyName);
  for (const application of lambdaApps) {
    let retryCount = 0;
    const maxRetries = 3;
    let pipelineResult = false;
    let appHealth = false;
    logger.info(`deploying lambda ${application.name}`);
    while (retryCount < maxRetries) {
      await deleteStackIfRolledBack(application, envName);
      await deployLatestArtifactCreateConfigAndTests(
        envName,
        type,
        application,
        deploymentId,
        costCenter,
      );
      await waitForPipelineTriggered();
      pipelineResult = await getDeploymentWaitingPromise(deploymentId, {
        application: application.name,
        envName,
        type,
      });
      if (pipelineResult) {
        appHealth = await waitApplicationToBeHealthyWithTimeout(envName, application, type, lambdaApiKey, 5 * 60 * 1000);
        logger.info('appHealth', { appHealth });

        if (appHealth) {
          logger.info(`${application.name} is healthy.`);
          break; // Exit the retry loop if the application is healthy
        } else {
          retryCount++;
          logger.error(`Failed to get ${application.name} health in the time limit. Retrying ${retryCount}/${maxRetries}...`);
        }
      } else {
        logger.error(`Pipeline ${application.name} failed to deploy. Retrying ${retryCount}/${maxRetries}...`);
        retryCount++;
      }

      if (retryCount >= maxRetries) {
        logger.error(`Max retries reached for ${application.name}. Please investigate.`);
        failedApps.push(application);
      }
    }
  }

  if (k8sApps.length > 0) {
    const k8sServer = await waitK8sApiServerWithTimeout(envName);
    if (!k8sServer) {
      await updateAppEnvironmentStatus(envName, UNHEALTHY);
      return {
        status: UNHEALTHY,
        message: 'K8s API server is unavailable.',
      };
    }
  }
  const serviceDependencies = await getApplicationsDependencies();
  const dependencies = getDependencies(serviceDependencies);
  logger.info('dependencies', { dependencies });
  for (const dependency of dependencies) {
    const applications = k8sApps.filter((app) => dependency.includes(app.name));
    logger.info(`k8s applications in current dependency ${applications.map((app) => app.name)}`);
    if (applications.length > 0) {
      for (const application of applications) {
        logger.info(`deploying k8s ${application.name}`);
        await
        deployLatestArtifactCreateConfigAndTests(
          envName,
          type,
          application,
          deploymentId,
          costCenter,
        );
      }
      await waitForPipelineTriggered();
      const deploymentBuilds = applications.map((application) => ({
        application: application.name,
        envName,
        type,
      }));

      await getDeploymentWaitingPromises(deploymentId, deploymentBuilds);
      await waitForDeploymentPipelineCompleted();
      let failedK8sApplications = [...applications];
      let retries = 0;
      const maxRetries = 3;
      while (failedK8sApplications.length > 0 && retries < maxRetries) {
        const retryFailedApplications = [];
        logger.info(`Retry attempt ${retries + 1} for failed applications: ${failedK8sApplications.map((app) => app.name).join(', ')}`);
        for (const application of failedK8sApplications) {
          const timeout = 3 * 60 * 1000;
          const appHealth = await waitApplicationToBeHealthyWithTimeout(envName, application, type, lambdaApiKey, timeout);
          if (!appHealth) {
            logger.error(`Failed to get ${application.name} health in the time limit, retrying...`);
            retryFailedApplications.push(application);
          } else {
            logger.info(`${application.name} is now healthy.`);
          }
        }

        retries++;
        failedK8sApplications = retryFailedApplications;

        if (failedK8sApplications.length > 0 && retries < maxRetries) {
          for (const application of failedK8sApplications) {
            logger.info(`Retrying k8s deployment for ${application.name}`);
            await deployLatestArtifactCreateConfigAndTests(
              envName,
              type,
              application,
              deploymentId,
              costCenter,
            );
          }
          await waitForPipelineTriggered();
          const failedAppNames = failedK8sApplications.map((app) => app.name);
          const failedDeploymentBuilds = deploymentBuilds.filter((build) => failedAppNames.includes(build.application));
          await getDeploymentWaitingPromises(deploymentId, failedDeploymentBuilds);
          await waitForDeploymentPipelineCompleted();
        }
      }
      if (failedK8sApplications.length > 0) {
        failedApps.push(...failedK8sApplications);
        logger.error(`The following applications failed after ${maxRetries} retries: ${failedK8sApplications.map((app) => app.name).join(', ')}`);
      } else {
        logger.info('All applications deployed and passed health checks.');
      }
    }
  }

  for (const application of ecsApps) {
    logger.info(`deploying ecs ${application.name}`);
    await deployLatestArtifactCreateConfigAndTests(envName, type, application, deploymentId, costCenter);
    await waitForPipelineTriggered();
    const pipelineResult = await getDeploymentWaitingPromise(deploymentId, {
      application: application.name,
      envName,
      type,
    });
    if (!pipelineResult) { // no health check for ECS, just check the pipeline status
      failedApps.push(application);
    }
  }
  if (failedApps.length > 0) {
    await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
    if (type === ApplicationType.DAE) {
      await updateAppEnvironmentStatus(envName, UNHEALTHY);
    }
    if (type === ApplicationType.DDE) {
      await updateDataEnvironmentStatus(envName, UNHEALTHY);
    }
    return {
      status: UNHEALTHY,
      message: `Some applications failed to deploy. ${failedApps.map((app) => app.name).join(', ')}`,
    };
  }
  // current deployment is successful, but we need to check the health of all the other applications
  let status = HEALTHY;
  const applicationsByEnv = await getApplicationsByEnvAndType(envName, type);
  for (const application of formatApplications(await removeEcsApplications(applicationsByEnv))) {
    const appHealth = await healthCheckResult(envName, application, type, lambdaApiKey);
    if (!appHealth) {
      logger.info(`Application ${application.name} is unhealthy.`);
      status = UNHEALTHY;
      await updateDeploymentStatus(deploymentId, DeploymentStatus.ERROR);
      break; // exit the loop if any application is unhealthy
    }
  }
  if (type === ApplicationType.DAE) {
    await updateAppEnvironmentStatus(envName, status);
  }
  if (type === ApplicationType.DDE) {
    await updateDataEnvironmentStatus(envName, status);
  }
  const message = (status === HEALTHY)
    ? 'All applications deployed successfully.'
    : 'Current deployment is successful, but other applications are unhealthy.';
  return {
    status,
    message,
  };
}

export async function createConfigsForApp(application, env, type, costCentre) {
  const context = await getContext(env, type);
  logger.info(`deploying ${application.Name.S}`);
  await createConfigIfSampleExist(INFRA_SAMPLE_PATH, 'tfvars', env, application, context, costCentre);
  if ((await getPlatform(application.Name.S)) === Platform.K8S) {
    await createK8sTestConfig(env, application, context, costCentre);
  }
  if ((await getPlatform(application.Name.S)) === Platform.LAMBDA) {
    await createLambdaConfig(env, application, context, costCentre);
    await createLambdaTestConfig(env, application, context, costCentre);
  }
  if ((await getPlatform(application.Name.S)) === Platform.ECS) {
    await createEcsConfig(env, application, context, costCentre);
  }
}

export async function createConfigsForApps(applications, environment, type) {
  const envName = environment.Name.S;
  const costCentre = environment.CostCentre.S;
  for (const application of applications) {
    await createConfigsForApp(application, envName, type, costCentre);
  }
}

export async function deployApplicationArtifact(appEnv, application, userName) {
  logger.info(`deploy ${application.Name.S}`);
  const from = application?.BucketKey?.S;
  if (from === '') {
    logger.info(`no deploy artifact found for ${application.Name.S}`);
  } else {
    let name = `${application.Name.S}`;
    if ((await getPlatform(name)) === Platform.K8S) {
      name = `monarch-${application.Name.S}`;
    }
    const to = `${name}/deploy/${normalizeName(appEnv)}/${ARTIFACT_FILE_NAME}`;
    logger.info(`copying ${from} to ${to}`);
    await copyFile(from, to);
    await sleep(30 * 1000);
  }
  const status = PipelineStatus.DEPLOYING;
  await updateApplicationDeployStatusAndUserName(application.Key.S, status, userName);
}

export async function fetchLatestBuildForApplication(type, appEnv, application) {
  const key = `${type}-${appEnv}-${application.name}`;

  const { deployBranch } = application;
  logger.info(`deployBranch ${deployBranch}`);

  const build = await findBuildByKey(`${application.name}-${deployBranch}`);
  logger.info(build);
  const params = {
    TableName: getTableName(APPLICATIONS),
    Key: {
      Key: { S: key },
    },
    UpdateExpression: 'SET #Sha = :Sha, #BucketKey = :BucketKey, #BuildVersion = :BuildVersion',
    ExpressionAttributeNames: {
      '#Sha': 'Sha',
      '#BucketKey': 'BucketKey',
      '#BuildVersion': 'BuildVersion',
    },
    ExpressionAttributeValues: {
      ':Sha': { S: build?.Sha?.S ?? '' },
      ':BucketKey': { S: build?.BucketKey?.S ?? '' },
      ':BuildVersion': { S: build?.BuildVersion?.S ?? '' },
    },
  };
  await dynamoDbClient.send(new UpdateItemCommand(params));
}

// UpdateUsagePlan API will be executed during the deployment process,
// the rate limit is 1 request every 20 seconds per account and can't be increased.
// See https://docs.aws.amazon.com/apigateway/latest/developerguide/limits.html#api-gateway-limits
// each call of this function needs to wait for some time to avoid throttling
export async function deployLatestArtifactCreateConfigAndTests(envName, type, application, deploymentId, costCentre) {
  const deployment = await getDeploymentByDeploymentId(deploymentId);
  const userName = deployment.UserName?.S;
  const key = `${type}-${envName}-${application.name}`;
  await fetchLatestBuildForApplication(type, envName, application);
  const updatedApplication = (await getApplicationByKey(key))[0];
  await createConfigsForApp(updatedApplication, envName, type, costCentre);
  await deployApplicationArtifact(envName, updatedApplication, userName);
  const deployBranch = updatedApplication.DeployBranch.S;
  logger.info(`deployBranch ${deployBranch}`);
  await saveDeploymentBuilds(envName, type, deploymentId, updatedApplication, deployBranch);
}

export async function getCostCenterByEnv(envName, type) {
  logger.info(`getCostCenterByEnv ${envName} ${type}`);
  let environment;
  if (type === ApplicationType.DAE) {
    const environments = await getAppEnvironmentByName(envName);
    if (environments.length === 0) {
      logger.info(`getCostCenterByEnv ${envName} ${type} not found`);
      return '';
    }
    [environment] = environments;
  }
  if (type === ApplicationType.DDE) {
    const environments = await getDataEnvironmentByName(envName);
    if (environments.length === 0) {
      logger.info(`getCostCenterByEnv ${envName} ${type} not found`);
      return '';
    }
    [environment] = environments;
  }
  return environment.CostCentre.S;
}

export async function waitUntilDatastoresReady(envName) {
  let stable = false;
  let count = 0;
  // DDE creation could take more than half day (16 ~ 18h), wait for 24 hours
  while (!stable && count < 60 * 24) {
    const datastores = await getDataStoresByEnv(envName);
    stable = true;
    for (const datastore of datastores) {
      logger.info(`datastore ${datastore.Name.S} status ${datastore.Status.S}`);
      if (datastore.Status.S !== STABLE) {
        stable = false;
      }
    }
    if (!stable) {
      await sleep(60 * 1000);
    }
    count += 1;
  }
}

export async function getDeploymentWaitingPromise(deploymentId, deploymentBuild) {
  const { application, envName, type } = deploymentBuild;
  const applicationKey = `${type}-${envName}-${application}`;
  const pipelineName = await getDeployPipelineName(application, envName);
  logger.info(`wait the deployment pipeline to be finished ${pipelineName}`);

  return monitorPipelineStatusWithTimeout(deploymentId, applicationKey, pipelineName, MONITOR_PIPELINE_STATUS_TIMEOUT);
}

export async function getDeploymentWaitingPromises(deploymentId, deploymentBuilds) {
  const promises = deploymentBuilds.map((build) => getDeploymentWaitingPromise(deploymentId, build));
  return Promise.all(promises);
}
