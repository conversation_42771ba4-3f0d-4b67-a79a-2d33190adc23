import { STATIC_DB_BAK_FILE_LOCATION } from 'common/util/const.js';
import { getBakFileInfo, restoreFromBakFile } from '../dal/restoreStatus.js';

export default async function qvnzStatic(options) {
  const { drive } = options;
  logger.info('restore QVNZ static databases');
  for (const [key, value] of STATIC_DB_BAK_FILE_LOCATION) {
    const bakFileInfo = await getBakFileInfo(value);
    await restoreFromBakFile(bakFileInfo, drive, '', key, value);
  }
  logger.info('job finished');
}
