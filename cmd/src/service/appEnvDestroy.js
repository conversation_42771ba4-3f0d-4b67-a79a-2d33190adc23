import { deleteAppEnvironment, getAppEnvironmentByName } from 'common/dal/appEnvironments.js';
import { deleteApplicationsByAppEnv, getApplicationsByEnvAndType } from 'common/dal/applications.js';
import { runCmdSync, unJoinDomainFromDev } from 'common/util/cmdUtil.js';
import { sleep } from 'common/util/sleepUtil.js';
import {
  AWS_ACCOUNTS,
  DAE_HOST_ZONE_ID,
  ERROR_CODES,
  MONARCH_K8S_BUCKET,
  QV_CONTENT_BUCKET_DAE,
} from 'common/util/const.js';
import { deleteTfScriptsByEnv } from 'common/dal/tfScripts.js';
import { normalizeName, removeDaeSuffix } from 'common/util/appNameUtli.js';
import { getPlatform } from 'common/dal/appStacks.js';
import { Platform } from 'common/enums/platform.js';
import { ApplicationType } from 'common/enums/applicationType.js';
import { deleteDynamodb } from 'common/util/dynamodb.js';
import appInfraDestroy from './appInfraDestroy.js';
import {
  ACCOUNT_CREDENTIALS,
  cloudWatchLogsClient,
  DEV_AWS_CREDENTIALS,
  devCloudWatchLogsClient,
  TF_S3_BUCKETS,
} from '../util/awsConfig.js';
import { deleteAuth0Config, getAuth0Token, getPropertyServiceClientId } from '../util/auth0Util.js';
import { AppEnvServiceError } from '../util/errors.js';
import { getQivsInternalEc2Id, getQivsInternalIp } from '../util/ec2Util.js';
import { restartQivsInternalEc2Instance } from './appEnvCreate.js';
import { getQivsSecondNetwork } from '../util/qivsUtil.js';
import {
  deleteAppConfig,
  deleteApplicationInfra,
  deleteLambdaApplications,
  deleteRoute53Records,
  deleteS3Delivery,
  deleteTestConfig,
  deleteTfConfig,
} from './applicationsDestroy.js';
import { deleteOpensearchIndexes } from './opensearchIndex.js';
import { deleteCloudWatchLogGroups } from '../util/cloudwatch.js';

export default async function appEnvDestroy(options) {
  const { appEnv } = options;
  logger.info(`destroy ${appEnv} started`);
  const queryResults = await getAppEnvironmentByName(appEnv);
  if (queryResults.length === 0) {
    logger.error(ERROR_CODES.NO_ENV_FOUND.code, ERROR_CODES.NO_ENV_FOUND.message, appEnv);
    throw new AppEnvServiceError(appEnv, ERROR_CODES.NO_ENV_FOUND.message, ERROR_CODES.NO_ENV_FOUND.code);
  }
  const appEnvironment = queryResults[0];
  await deleteOpensearchIndexes(appEnv);
  await undoScriptsOnQivs(appEnvironment);
  await deleteApplicationInfra(appEnv, ApplicationType.DAE);
  const applications = await getApplicationsByEnvAndType(appEnv, ApplicationType.DAE);
  await deleteLambdaApplications(applications, appEnv, ApplicationType.DAE);
  for (const application of applications) {
    await deleteAppConfig(application, appEnv);
    await deleteTestConfig(application, appEnv);
    await deleteTfConfig(application, appEnv);
  }
  await deleteAuth0(appEnvironment, appEnv);
  const normalisedEnvName = normalizeName(appEnv);
  await forceDeleteECR(normalisedEnvName);
  await emptyBuckets(appEnv);
  await appInfraDestroy(appEnv);
  await deleteTfScriptsByEnv(`app_${appEnv}`);
  deleteTfS3Buckets(appEnv);
  deleteDeploymentS3Buckets(normalisedEnvName);
  deleteDeployLogs(normalisedEnvName, applications);
  await deleteS3Delivery(normalisedEnvName, applications);
  await deleteReports(normalisedEnvName, applications);
  await deleteEcsDeploymentFolders(normalisedEnvName);
  await deleteDynamodb(normalisedEnvName, ApplicationType.DAE);

  await deleteCloudWatchLogGroups(cloudWatchLogsClient, [
    normalisedEnvName,
  ]);

  await deleteCloudWatchLogGroups(devCloudWatchLogsClient, [
    `/${normalisedEnvName}`,
    `-${normalisedEnvName}`,
    `-${normalisedEnvName}/performance`,
    `-${normalisedEnvName}/application`,
    `-${normalisedEnvName}/dataplane`,
  ]);

  deleteRoute53Records(normalisedEnvName, DAE_HOST_ZONE_ID, ApplicationType.DAE);

  await deleteNetwork(appEnv);
  await deleteApplicationsByAppEnv(appEnv);
  await deleteAppEnvironment(appEnv);

  logger.info(`destroy ${appEnv} finished`);
}

export async function deleteStack(lambdaApplication, appEnv) {
  const startTime = Date.now();
  logger.info(`delete lambda application resources ${lambdaApplication.Name.S}`);
  let appName = lambdaApplication.Name.S;
  appName = removeDaeSuffix(appName);
  const stackName = normalizeName(`${appName}-${appEnv}`);
  const deleteStackCmd = `${DEV_AWS_CREDENTIALS} aws cloudformation delete-stack --stack-name ${stackName}`;
  runCmdSync(deleteStackCmd);
  const describeStackCmd = `${DEV_AWS_CREDENTIALS} aws cloudformation describe-stacks --stack-name ${stackName}`;
  while (true) {
    try {
      const result = runCmdSync(describeStackCmd);
      const stackStatus = JSON.parse(result).Stacks[0].StackStatus;
      logger.info(`stack status: ${stackStatus}`);
      if (stackStatus === 'DELETE_FAILED') { // due to api rate limit, retry the delete stack
        logger.info('Delete failed. Retrying...');
        await deleteStack(lambdaApplication, appEnv);
      } else if (stackStatus === 'DELETE_IN_PROGRESS') {
        logger.info('Deletion in progress. Waiting for 1 minute...');
        await sleep(60000);
      } else {
        logger.info('Stack is in an unexpected state. Exiting...');
        break;
      }
    } catch (e) {
      if (e.message.includes(`Stack with id ${stackName} does not exist`)) {
        logger.info(`stack ${stackName} has been deleted`);
        break;
      }
      throw e;
    }
    if (Date.now() - startTime > 5 * 60 * 1000) {
      throw new Error('DeleteStack reached timeout. Exiting loop.');
    }
  }
}

async function deleteAuth0(appEnvironment, appEnv) {
  const daeAuthAccount = 'test';

  const appClientId = await getPropertyServiceClientId(daeAuthAccount);

  const tokenData = await getAuth0Token(daeAuthAccount);

  await deleteAuth0Config(tokenData.access_token, appClientId, appEnv, daeAuthAccount);
}

function deleteTfS3Buckets(env) {
  AWS_ACCOUNTS.forEach((account) => {
    const credentials = ACCOUNT_CREDENTIALS[account];
    const bucket = TF_S3_BUCKETS[account];
    const deleteTfS3Bucket = `${credentials} aws s3 rm s3://${bucket}/${account}/dynamic-environments/app_${env}`;
    const s3Result = runCmdSync(deleteTfS3Bucket);
    logger.info(`deleteTfS3Bucket result: ${s3Result}`);
  });
  runCmdSync(`aws s3 rm s3://qv-terraform/dev/${env}/  --recursive`);
}

function deleteDeploymentS3Buckets(env) {
  const cmd = `${DEV_AWS_CREDENTIALS} aws s3 rm s3://${MONARCH_K8S_BUCKET}/${env}/ --recursive`;
  runCmdSync(cmd);
}

export async function deleteReports(appEnv, applications) {
  for (const application of applications) {
    let appName = application.Name.S;
    if ((await getPlatform(appName)) === Platform.K8S) {
      appName = `monarch-${appName}`;
    }
    const deliveryS3Bucket = `aws s3 rm s3://qv-deployment-reports/${appName}-deploy-${appEnv}/ --recursive`;
    const s3Result = runCmdSync(deliveryS3Bucket);
    logger.info(`deliveryS3Bucket result: ${s3Result}`);
  }
}

export function deleteDeployLogs(appEnv, applications) {
  for (const application of applications) {
    const appName = application.Name.S;
    const groupName = `${appName}-deploy-${appEnv}`;
    deleteLogGroup('', groupName);
  }
}

export async function deleteEcsDeploymentFolders(appEnv) {
  const folders = [
    `qvms-deploy-${appEnv}`,
    `qvms-pipeline-${appEnv}`,
    `public-website-deploy-${appEnv}`,
    `public-website-pipeline-${appEnv}`,
    `report-generator-deploy-${appEnv}`,
    `report-generator-pipeline-${appEnv}`,
  ];
  for (const folder of folders) {
    const deliveryS3Bucket = `aws s3 rm s3://qv-deployment/${folder}/ --recursive`;
    const s3Result = runCmdSync(deliveryS3Bucket);
    logger.info(`deleteEcsDeploymentFolders result: ${s3Result}`);
  }
}

export function deleteLogGroup(accountCredentials, groupName) {
  const deleteLogGroupCmd = `${accountCredentials} aws logs delete-log-group --log-group-name ${groupName}`;
  try {
    const logResult = runCmdSync(deleteLogGroupCmd);
    logger.info(`deleteLogGroup result: ${logResult}`);
  } catch (e) {
    if (!e.message.includes('The specified log group does not exist')) {
      throw e;
    }
    logger.info(`log group ${groupName} does not exist`);
  }
}

// Manually delete ECR repo as it can't be deleted by Terraform as images are still in use
async function forceDeleteECR(appEnv) {
  const repos = ['qvconz'];
  for (const repo of repos) {
    const cmd = ` ${DEV_AWS_CREDENTIALS} aws ecr delete-repository --repository-name ${repo}-${appEnv} --force`;
    try {
      runCmdSync(cmd);
    } catch (e) {
      if (!e.message.includes('RepositoryNotFoundException')) {
        throw e;
      }
      logger.info(`report-generator-${appEnv} does not exist`);
    }
  }
}

export async function undoScriptsOnQivs(appEnvironment) {
  if (appEnvironment.IncludeQivs.BOOL === false) {
    logger.info('qivs internal is not included');
    return;
  }
  const envName = appEnvironment.Name.S;
  const qivsIp = getQivsInternalIp(envName);
  if (qivsIp === '') {
    return;
  }
  await unJoinDomainFromDev(qivsIp);
  await restartQivsInternalEc2Instance(envName);

  logger.info('undo scripts on qivs internal');
}

export async function deleteNetwork(envName) {
  const networkInterface = getQivsSecondNetwork(envName);
  if (networkInterface == null) {
    return;
  }
  const networkInterfaceId = networkInterface.NetworkInterfaceId;
  const ec2InstanceId = getQivsInternalEc2Id(envName);
  logger.info(`ec2InstanceId: ${ec2InstanceId}`);
  const deleteNetworkCmd = `${DEV_AWS_CREDENTIALS} aws ec2 delete-network-interface --network-interface-id ${networkInterfaceId}`;
  runCmdSync(deleteNetworkCmd);
}

async function emptyBuckets(appEnv) {
  try {
    const cmd = `${DEV_AWS_CREDENTIALS} aws s3 rm s3://${appEnv}-${QV_CONTENT_BUCKET_DAE}/ --recursive`;
    runCmdSync(cmd);
  } catch (e) {
    if (e.message.includes('NoSuchBucket')) {
      logger.info(`Bucket ${appEnv}-${QV_CONTENT_BUCKET_DAE} does not exist`);
      return;
    }
    logger.error('ERR-CICD-021', 'Error emptying bucket', e);
  }
}
