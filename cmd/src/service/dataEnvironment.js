import { DELETED, ERROR, STABLE } from 'common/enums/environmentStatus.js';
import {
  getDataEnvironmentByName,
  updateEnvironmentByNameDestroy,
  updateEnvironmentStatusByName,
} from 'common/dal/dataEnvironments.js';
import { getDataStoresByEnv } from 'common/dal/datastores.js';

export async function setEnvironmentStatusByDataStoreStatus(dataEnvironment) {
  const dataStores = await getDataStoresByEnv(dataEnvironment.Name.S);
  if (dataStores.every((dataStore) => dataStore.Status.S === STABLE)) {
    dataEnvironment.Status = { S: STABLE };
  }
  if (dataStores.every((dataStore) => dataStore.Status.S === DELETED)) {
    dataEnvironment.Status = { S: DELETED };
  }
  if (dataStores.some((dataStore) => dataStore.Status.S === ERROR)) {
    dataEnvironment.Status = { S: ERROR };
  }
  logger.info(`updateEnvironmentStatusByDataStoreStatus ${dataEnvironment.Name.S} ${dataEnvironment.Status.S}`);
}

// TODO need a lock to make sure only one concurrent update to avoid environment have a wrong status but won't happy very often
export async function restoreFinishUpdateEnvironment(env) {
  const currentDataEnvironment = (await getDataEnvironmentByName(env))[0];
  await setEnvironmentStatusByDataStoreStatus(currentDataEnvironment);
  let endTime = currentDataEnvironment.EndTime.N;
  if (currentDataEnvironment.Status.S === STABLE) {
    endTime = Date.now().toString();
  }
  await updateEnvironmentStatusByName(env, currentDataEnvironment.Status.S, endTime);
}

// TODO need a lock to make sure only one concurrent update to avoid environment have a wrong status but won't happy very often
export async function destroyFinishUpdateEnvironment(env) {
  const currentDataEnvironment = (await getDataEnvironmentByName(env))[0];
  await setEnvironmentStatusByDataStoreStatus(currentDataEnvironment);
  let teardownEndTime = currentDataEnvironment.TeardownEndTime.N;
  if (currentDataEnvironment.Status.S === DELETED) {
    teardownEndTime = Date.now().toString();
  }
  await updateEnvironmentByNameDestroy(env, currentDataEnvironment.Status.S, teardownEndTime);
}
