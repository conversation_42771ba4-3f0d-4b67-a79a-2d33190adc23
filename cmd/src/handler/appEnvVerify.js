import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import { STATUS_SUCCESS } from 'common/enums/crudStatus.js';
import wrapCmdHandler from '../util/wrapCmdHandler.js';
import appEnvVerify from '../service/appEnvVerify.js';
import { sendSlack } from '../util/slackUtil.js';

const runAppEnvVerify = wrapCmdHandler(
  async (options) => {
    try {
      const result = await appEnvVerify(options);
      if (result.status === STATUS_SUCCESS) {
        await sendSlack(CmdAppTasks.APP_ENV_VERIFY, 'app env verify completed.', ':white_check_mark:');
      } else {
        const message = `app env verify failed. ${result.message}`;
        await sendSlack(CmdAppTasks.APP_ENV_VERIFY, message, ':x:');
      }
    } catch (e) {
      const message = `app env verify failed. ${e.message}`;
      await sendSlack(CmdAppTasks.APP_ENV_VERIFY, message, ':x:');
      throw e;
    }
  },
);
export default runAppEnvVerify;
