import { program } from 'commander';
import Logger from 'common/util/Logger.js';
import { CmdAppTasks } from 'common/enums/cmdAppTasks.js';
import runQvnzRestore from './handler/qvnzRestore.js';

import { handleInvalidOptionsError } from './util/errorHandler.js';
import runQvnzStatic from './handler/qvnzStatic.js';
import runPgRestore from './handler/pgRestore.js';
import runDataEnvCreate from './handler/dataEnvCreate.js';
import runDataEnvDestroy from './handler/dataEnvDestroy.js';
import runAppEnvDestroy from './handler/appEnvDestroy.js';
import runAppEnvCreate from './handler/appEnvCreate.js';
import runAppEnvUpdate from './handler/appEnvUpdate.js';
import runAppEnvVerify from './handler/appEnvVerify.js';
import runDbScripts from './handler/dbScripts.js';
import runDataEnvUpdate from './handler/dataEnvUpdate.js';
import runApplicationDeploy from './handler/applicationDeploy.js';
import runAppEnvReindex from './handler/appEnvReindex.js';

global.logger = new Logger();

program.option('--name <name>');
program.option('--dataEnv <dataEnv>');
program.option('--datastore <datastore>');
program.option('--appEnv <appEnv>');
program.option('--branch <branch>');
program.option('--drive <drive>');
program.option('--deploymentId <deploymentId>');
program.option('--datastore <datastore>');
program.option('--reindex <reindex>');
program.option('--sleepTags <sleepTags>');
program.option('--payload <payload>');
program.parse();

const options = program.opts();
const actions = {
  [CmdAppTasks.QVNZ_RESTORE]: runQvnzRestore,
  [CmdAppTasks.PG_RESTORE]: runPgRestore,
  [CmdAppTasks.DATA_ENV_CREATE]: runDataEnvCreate,
  [CmdAppTasks.DATA_ENV_DESTROY]: runDataEnvDestroy,
  [CmdAppTasks.DATA_ENV_UPDATE]: runDataEnvUpdate,
  [CmdAppTasks.APP_ENV_CREATE]: runAppEnvCreate,
  [CmdAppTasks.APP_ENV_REINDEX]: runAppEnvReindex,
  [CmdAppTasks.APP_ENV_DESTROY]: runAppEnvDestroy,
  [CmdAppTasks.APP_ENV_UPDATE]: runAppEnvUpdate,
  [CmdAppTasks.APP_ENV_VERIFY]: runAppEnvVerify,
  [CmdAppTasks.QVNZ_STATIC]: runQvnzStatic,
  [CmdAppTasks.DB_SCRIPTS]: runDbScripts,
  [CmdAppTasks.APPLICATION_DEPLOY]: runApplicationDeploy,
};
logger.info(`OPTIONS: ${JSON.stringify(options)}`);

const action = actions[options.name];
try {
  if (action) {
    await action(options);
  } else {
    handleInvalidOptionsError(options).then((r) => logger.info('handleInvalidOptionsError done', r));
  }
} catch (e) {
  console.error(e);
} finally {
  logger.info('finally');
  process.exit(0); // make sure process exit
}
