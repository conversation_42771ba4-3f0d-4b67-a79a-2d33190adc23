{"total": {"lines": {"total": 1316, "covered": 133, "skipped": 0, "pct": 10.1}, "statements": {"total": 10149, "covered": 133, "skipped": 0, "pct": 1.31}, "functions": {"total": 2539, "covered": 19, "skipped": 0, "pct": 0.74}, "branches": {"total": 9293, "covered": 33, "skipped": 0, "pct": 0.35}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/qv/api-stats/mochawesome-report/assets/app.js": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2347, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8821, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8776, "covered": 0, "skipped": 0, "pct": 0}}}