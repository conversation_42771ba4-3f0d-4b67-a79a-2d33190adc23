import { expect } from 'chai';
import { pgPreparedInsert } from '../../src/dal/pgCommon.js';

describe('pgPreparedInsert', () => {
  it('should generate correct SQL query for inserting data', () => {
    const table = 'user_profile.user_role';
    const data = {
      user_id: '123',
      role_id: '456',
    };

    const result = pgPreparedInsert(table, data);

    const expectedQuery = {
      text: `INSERT INTO ${table} (user_id, role_id) VALUES ($1, $2)`,
      values: ['123', '456'],
    };

    expect(result).to.deep.equal(expectedQuery);
  });
});
