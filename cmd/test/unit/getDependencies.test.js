import Logger from 'common/util/Logger.js';
import { expect } from 'chai';
import { getDependencies } from '../../src/util/serviceDependencies.js';

global.logger = new Logger();

describe('Dependency Util', () => {
  it('should get correct deployment order', () => {
    const serviceDependencies = {
      audit: {
        dependsOn: [],
        dependedOnBy: [],
      },
      classification: {
        dependsOn: [],
        dependedOnBy: ['home-valuation', 'property', 'reporting', 'ereporting', 'roll-maintenance'],
      },
      'home-valuation': {
        dependsOn: ['property', 'classification', 'sale', 'sale-analysis', 'user-profile', 'stats', 'media'],
        dependedOnBy: ['reporting', 'ereporting'],
      },
      media: {
        dependsOn: ['property'],
        dependedOnBy: ['home-valuation', 'reporting', 'ereporting'],
      },
      property: {
        dependsOn: ['classification', 'stats', 'qivs-stream'],
        dependedOnBy: ['home-valuation', 'roll-maintenance', 'sale', 'sale-analysis', 'media'],
      },
      'qivs-stream': {
        dependsOn: [],
        dependedOnBy: ['property'],
      },
      'roll-maintenance': {
        dependsOn: ['property', 'classification', 'stats', 'qivs-stream'],
        dependedOnBy: ['reporting', 'ereporting'],
      },
      reporting: {
        dependsOn: ['sale', 'sale-analysis', 'stats', 'property', 'media', 'ta', 'home-valuation', 'roll-maintenance', 'user-profile', 'classification'],
        dependedOnBy: ['sale-analysis'],
      },
      ereporting: {
        dependsOn: ['sale', 'sale-analysis', 'stats', 'property', 'media', 'ta', 'home-valuation', 'roll-maintenance', 'user-profile', 'classification'],
        dependedOnBy: [],
      },
      sale: {
        dependsOn: ['qivs-stream', 'property', 'classification'],
        dependedOnBy: ['home-valuation', 'sale-analysis', 'reporting', 'ereporting'],
      },
      'sale-analysis': {
        dependsOn: ['stats', 'sale', 'reporting', 'property'],
        dependedOnBy: ['home-valuation', 'reporting', 'ereporting'],
      },
      ta: {
        dependsOn: ['stats'],
        dependedOnBy: ['reporting', 'ereporting'],
      },
      'user-profile': {
        dependsOn: [],
        dependedOnBy: ['ereporting', 'reporting', 'home-valuation'],
      },
      stats: {
        dependsOn: [],
        dependedOnBy: ['ta', 'sale-analysis', 'home-valuation', 'property', 'reporting', 'ereporting', 'roll-maintenance'],
      },
      'monarch-web': {
        dependsOn: [],
        dependedOnBy: [],
      },
    };

    const result = getDependencies(serviceDependencies);

    const expected = [
      ['audit'],
      ['classification'],
      ['qivs-stream'],
      ['user-profile'],
      ['stats'],
      ['property'],
      ['ta'],
      ['media'],
      ['roll-maintenance'],
      ['sale'],
      ['home-valuation', 'reporting', 'ereporting', 'sale-analysis'],
      ['monarch-web'],
    ];

    expect(result).to.deep.equal(expected);
  });
});
