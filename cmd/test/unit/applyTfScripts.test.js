import Logger from 'common/util/Logger.js';
import { expect } from 'chai';
import { ApplicationStack } from 'common/enums/applicationStack.js';
import { getTfScripts } from '../../src/service/applyTfScripts.js';

global.logger = new Logger();

describe('getTfScripts', () => {
  it('should get public website scripts', async () => {
    // given
    const appEnvironment = {
      IncludeQivs: { BOOL: false },
      Stacks: {
        L: [
          { S: ApplicationStack.PUBLIC_WEBSITE },
        ],
      },
    };
    const ecsApplications = [
    ];
    // when
    const files = await getTfScripts(appEnvironment, ecsApplications);
    // then
    expect(files.length).to.equal(60);
  });

  it('should get monarch scripts', async () => {
    // given
    const appEnvironment = {
      IncludeQivs: { BOOL: false },
      Stacks: {
        L: [
          { S: ApplicationStack.MONARCH },
        ],
      },
    };
    const ecsApplications = [
    ];
    // when
    const files = await getTfScripts(appEnvironment, ecsApplications);
    // then
    expect(files.length).to.equal(51);
  });

  it('should get monarch with qivs scripts', async () => {
    // given
    const appEnvironment = {
      IncludeQivs: { BOOL: true },
      Stacks: {
        L: [
          { S: ApplicationStack.MONARCH },
        ],
      },
    };
    const ecsApplications = [
    ];
    // when
    const files = await getTfScripts(appEnvironment, ecsApplications);
    // then
    expect(files.length).to.equal(54);
  });

  it('should get monarch with qivs, report generator scripts', async () => {
    // given
    const appEnvironment = {
      IncludeQivs: { BOOL: true },
      Stacks: {
        L: [
          { S: ApplicationStack.MONARCH },
        ],
      },
    };
    const ecsApplications = [
      'report-generator',
    ];
    // when
    const files = await getTfScripts(appEnvironment, ecsApplications);
    // then
    expect(files.length).to.equal(55);
  });

  it('should get all optional scripts', async () => {
    // given
    const appEnvironment = {
      IncludeQivs: { BOOL: true },
      Stacks: {
        L: [
          { S: ApplicationStack.MONARCH },
          { S: ApplicationStack.PUBLIC_WEBSITE },
        ],
      },
    };
    const ecsApplications = ['report-generator'];
    // when
    const files = await getTfScripts(appEnvironment, ecsApplications);
    // then
    expect(files.length).to.equal(66);
  });
});
