import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { expect } from 'chai';
import { generateSqlReport } from '../../src/util/sqlReportUtil.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('parse sql results', () => {
  it('should generate report with 500 error code when some sql errors occur', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sqlResults = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/sqlResultsError.json'), 'utf8'));
    // when
    const result = generateSqlReport(sqlResults);
    const report = `
code:500
db-scripts/cicd/qv_apihub/tables/alter.sql executed with errors:
 - Column names in each table must be unique. Column name 't1' in table 'dbo.api_usage' is specified more than once.
db-scripts/cicd/qv_apihub/tables/query.sql executed successfully.
 - Rows affected: 1000
    `;
    expect(result.trim()).to.equal(report.trim());
  });

  it('should generate report with 200 code when some no errors occur', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sqlResults = JSON.parse(fs.readFileSync(path.resolve(__dirname, './files/sqlResultsNoError.json'), 'utf8'));
    // when
    const result = generateSqlReport(sqlResults);
    const report = `
code:200
db-scripts/cicd/qv_apihub/tables/query.sql executed successfully.
 - Rows affected: 1000
    `;
    expect(result.trim()).to.equal(report.trim());
  });
});
