{"stats": {"suites": 7, "tests": 17, "passes": 15, "pending": 0, "failures": 2, "start": "2023-10-30T00:17:07.158Z", "end": "2023-10-30T00:17:07.168Z", "duration": 10, "testsRegistered": 17, "passPercent": 88.23529411764706, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "0051ef89-2172-4044-92ae-11a11b425458", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "c9410a92-415c-4754-a012-7c93d5604c67", "title": "<PERSON><PERSON>", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/Logger.spec.js", "file": "/test/unit/Logger.spec.js", "beforeHooks": [{"title": "\"before each\" hook in \"Logger\"", "fullTitle": "Logger \"before each\" hook in \"Logger\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "// Mock AWS context for testing\nconst mockContext = {\n  awsRequestId: 'mockRequestId',\n  functionName: 'mockFunctionName',\n  functionVersion: 'mockFunctionVersion',\n};\nlogger = new Logger(mockContext);", "err": {}, "uuid": "2145cf84-5256-4dd8-a0cd-223ed9e31276", "parentUUID": "c9410a92-415c-4754-a012-7c93d5604c67", "isHook": true, "skipped": false}], "afterHooks": [], "tests": [{"title": "should log a message with INFO level", "fullTitle": "Logger should log a message with INFO level", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const logMessage = 'This is an info message';\nconst logArgs = [1, 2, 3];\n// Mock the _log function\nlogger._log = (message, args, context, severity) => {\n  expect(severity).to.equal('INFO');\n  expect(message).to.equal(logMessage);\n  expect(args).to.deep.equal(logArgs);\n  expect(context).to.exist;\n};\nlogger.info(logMessage, ...logArgs);", "err": {}, "uuid": "36b2a9a0-03c1-4dda-819b-44eaff16301c", "parentUUID": "c9410a92-415c-4754-a012-7c93d5604c67", "isHook": false, "skipped": false}, {"title": "should log a message with DEBUG level", "fullTitle": "Logger should log a message with DEBUG level", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const logMessage = 'This is a debug message';\nconst logArgs = ['value'];\n// Mock the _log function\nlogger._log = (message, args, context, severity) => {\n  expect(severity).to.equal('DEBUG');\n  expect(message).to.equal(logMessage);\n  expect(args).to.deep.equal(logArgs);\n  expect(context).to.exist;\n};\nlogger.debug(logMessage, ...logArgs);", "err": {}, "uuid": "f23fdcb8-c86b-4a3e-8abc-064c121d7d9d", "parentUUID": "c9410a92-415c-4754-a012-7c93d5604c67", "isHook": false, "skipped": false}, {"title": "should log a message with ERROR level", "fullTitle": "Logger should log a message with ERROR level", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const logMessage = 'This is an error message';\nconst logArgs = ['value'];\nconst errorCode = 'ERROR_CODE';\nconst exception = new Error('Error occurred');\n// Mock the _log function\nlogger._log = (message, args, context, severity, error) => {\n  expect(severity).to.equal('ERROR');\n  expect(message).to.equal(logMessage);\n  expect(args).to.deep.equal(logArgs);\n  expect(context).to.exist;\n  expect(error).to.deep.equal({\n    errorCode: errorCode,\n    error: {\n      message: exception.message,\n      stack: exception.stack,\n    },\n  });\n};\nlogger.error(errorCode, logMessage, exception, ...logArgs);", "err": {}, "uuid": "b057ae43-989c-4c80-b7e0-f90a0bc35cf7", "parentUUID": "c9410a92-415c-4754-a012-7c93d5604c67", "isHook": false, "skipped": false}], "suites": [], "passes": ["36b2a9a0-03c1-4dda-819b-44eaff16301c", "f23fdcb8-c86b-4a3e-8abc-064c121d7d9d", "b057ae43-989c-4c80-b7e0-f90a0bc35cf7"], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 20000}, {"uuid": "1aac330b-a9d9-49f7-890e-0842b95c9dcb", "title": "eventValidator", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/eventValidator.spec.js", "file": "/test/unit/eventValidator.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "92886bf4-1092-42b1-ab10-5eae1f6c8630", "title": "pathParam", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/eventValidator.spec.js", "file": "/test/unit/eventValidator.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return the value of the path parameter if it exists", "fullTitle": "eventValidator pathParam should return the value of the path parameter if it exists", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  pathParameters: {\n    id: '12345',\n  },\n};\nconst param = 'id';\nconst result = eventValidator.pathParam(event, param);\nexpect(result).to.deep.equal({ value: '12345' });", "err": {}, "uuid": "2b777a5c-291d-4575-94ee-65f7801cb957", "parentUUID": "92886bf4-1092-42b1-ab10-5eae1f6c8630", "isHook": false, "skipped": false}, {"title": "should return an error message if the path parameter is missing", "fullTitle": "eventValidator pathParam should return an error message if the path parameter is missing", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  pathParameters: {},\n};\nconst param = 'id';\nconst result = eventValidator.pathParam(event, param);\nexpect(result).to.deep.equal({\n  message: 'Path parameter missing: id',\n  status: STATUS_INVALID,\n});", "err": {}, "uuid": "c70cc1ac-41bf-4540-b261-cd60b3f46eae", "parentUUID": "92886bf4-1092-42b1-ab10-5eae1f6c8630", "isHook": false, "skipped": false}], "suites": [], "passes": ["2b777a5c-291d-4575-94ee-65f7801cb957", "c70cc1ac-41bf-4540-b261-cd60b3f46eae"], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 20000}, {"uuid": "8efc5ed2-5b4d-4a9f-b2cb-78a8df2d9a9f", "title": "queryParam", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/eventValidator.spec.js", "file": "/test/unit/eventValidator.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return the value of the query parameter if it exists", "fullTitle": "eventValidator queryParam should return the value of the query parameter if it exists", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: {\n    name: '<PERSON>',\n  },\n};\nconst param = 'name';\nconst result = eventValidator.queryParam(event, param);\nexpect(result).to.deep.equal({ value: '<PERSON>' });", "err": {}, "uuid": "f030de9e-e334-4d30-a3d9-4121657e490d", "parentUUID": "8efc5ed2-5b4d-4a9f-b2cb-78a8df2d9a9f", "isHook": false, "skipped": false}, {"title": "should return the default value if the query parameter is missing and a default is provided", "fullTitle": "eventValidator queryParam should return the default value if the query parameter is missing and a default is provided", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: {},\n};\nconst param = 'limit';\nconst paramDefault = 10;\nconst result = eventValidator.queryParam(event, param, paramDefault);\nexpect(result).to.deep.equal({\n  value: 10,\n  paramDefault: true,\n});", "err": {}, "uuid": "d7eb9b9c-1e62-4798-80ac-e90a53f478d4", "parentUUID": "8efc5ed2-5b4d-4a9f-b2cb-78a8df2d9a9f", "isHook": false, "skipped": false}, {"title": "should return an error message if the query parameter is missing and no default is provided", "fullTitle": "eventValidator queryParam should return an error message if the query parameter is missing and no default is provided", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: {},\n};\nconst param = 'name';\nconst result = eventValidator.queryParam(event, param);\nexpect(result).to.deep.equal({\n  message: 'Query parameter missing: name',\n  status: STATUS_INVALID,\n});", "err": {}, "uuid": "6c690c1c-6b41-4ae1-87d3-ab0c39bf163f", "parentUUID": "8efc5ed2-5b4d-4a9f-b2cb-78a8df2d9a9f", "isHook": false, "skipped": false}], "suites": [], "passes": ["f030de9e-e334-4d30-a3d9-4121657e490d", "d7eb9b9c-1e62-4798-80ac-e90a53f478d4", "6c690c1c-6b41-4ae1-87d3-ab0c39bf163f"], "failures": [], "pending": [], "skipped": [], "duration": 1, "root": false, "rootEmpty": false, "_timeout": 20000}, {"uuid": "0f6b0f18-6814-48ca-a031-f58e8e58f469", "title": "parseQueryParameters", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/eventValidator.spec.js", "file": "/test/unit/eventValidator.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should parse and return the query parameters with default values", "fullTitle": "eventValidator parseQueryParameters should parse and return the query parameters with default values", "timedOut": false, "duration": 0, "state": "failed", "speed": null, "pass": false, "fail": true, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: {\n    total: 'true',\n    filter: 'example',\n    offset: '10',\n  },\n};\nconst options = {\n  defaultSortBy: 'DATE',\n  defaultLimit: 50,\n  defaultSortDesc: true,\n};\nconst result = eventValidator.parseQueryParameters(event, options);\nexpect(result).to.deep.equal({\n  filter: 'example',\n  offset: 10,\n  limit: 50,\n  total: true,\n  active: false,\n  sortBy: 'DATE',\n  sortDesc: true,\n});", "err": {"message": "ReferenceError: queryParam is not defined", "estack": "ReferenceError: queryParam is not defined\n    at module.exports.parseQueryParameters (src/util/eventValidators.js:34:17)\n    at Context.<anonymous> (test/unit/eventValidator.spec.js:83:37)\n    at process.processImmediate (node:internal/timers:476:21)", "diff": null}, "uuid": "fd29ab9a-c9b2-40e0-850f-9b08474f6bd8", "parentUUID": "0f6b0f18-6814-48ca-a031-f58e8e58f469", "isHook": false, "skipped": false}], "suites": [], "passes": [], "failures": ["fd29ab9a-c9b2-40e0-850f-9b08474f6bd8"], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 20000}, {"uuid": "2c2dfb42-2d37-49e9-8dd0-ed0252d1071f", "title": "parseBody", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/eventValidator.spec.js", "file": "/test/unit/eventValidator.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should parse and return the JSON body if it is valid", "fullTitle": "eventValidator parseBody should parse and return the JSON body if it is valid", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  body: '{\"name\": \"<PERSON>\", \"age\": 30}',\n};\nconst result = eventValidator.parseBody(event);\nexpect(result).to.deep.equal({\n  body: {\n    name: '<PERSON>',\n    age: 30,\n  },\n});", "err": {}, "uuid": "7d8c6aa0-683b-4c14-bb40-105d58ceed20", "parentUUID": "2c2dfb42-2d37-49e9-8dd0-ed0252d1071f", "isHook": false, "skipped": false}, {"title": "should return an error message if the body is not valid JSON", "fullTitle": "eventValidator parseBody should return an error message if the body is not valid JSON", "timedOut": false, "duration": 0, "state": "failed", "speed": null, "pass": false, "fail": true, "pending": false, "context": null, "code": "const event = {\n  body: 'invalid json',\n};\nconst result = eventValidator.parseBody(event);\nexpect(result).to.deep.equal({\n  message: 'Malformed JSON',\n  status: STATUS_INVALID,\n});", "err": {"message": "ReferenceError: logger is not defined", "estack": "ReferenceError: logger is not defined\n    at module.exports.parseBody (src/util/eventValidators.js:65:5)\n    at Context.<anonymous> (test/unit/eventValidator.spec.js:114:37)\n    at process.processImmediate (node:internal/timers:476:21)", "diff": null}, "uuid": "e08b4740-5ca5-44b2-b3f7-9a5e1187d729", "parentUUID": "2c2dfb42-2d37-49e9-8dd0-ed0252d1071f", "isHook": false, "skipped": false}], "suites": [], "passes": ["7d8c6aa0-683b-4c14-bb40-105d58ceed20"], "failures": ["e08b4740-5ca5-44b2-b3f7-9a5e1187d729"], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 20000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 20000}, {"uuid": "c2d63366-7698-4dde-80fc-56798bd4d302", "title": "workUnitValuerSummary", "fullFile": "/Users/<USER>/qv/api-stats/test/unit/workUnitValuerSummary.spec.js", "file": "/test/unit/workUnitValuerSummary.spec.js", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "should return an invalid response if username is missing", "fullTitle": "workUnitValuerSummary should return an invalid response if username is missing", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = { queryStringParameters: {} };\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'Username must be provided',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "0aaeb4f6-e6b0-4708-b3ba-b15bab0fd7e9", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}, {"title": "should return an invalid response if username is not a string", "fullTitle": "workUnitValuerSummary should return an invalid response if username is not a string", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = { queryStringParameters: { username: 123 } };\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'Username is invalid',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "cf5ae264-fb28-41d0-ad1a-6f4d3f469778", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}, {"title": "should return an invalid response if startDate is missing", "fullTitle": "workUnitValuerSummary should return an invalid response if startDate is missing", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = { queryStringParameters: { username: 'john' } };\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'Start date must be provided',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "14144487-6e51-4bbd-add2-e2157e4cf235", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}, {"title": "should return an invalid response if startDate is invalid", "fullTitle": "workUnitValuerSummary should return an invalid response if startDate is invalid", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: { username: 'john', startDate: '2022-13-01' },\n};\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'Start date is invalid',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "9ea99d4f-e6b3-49e2-b0f4-4e4d972a387c", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}, {"title": "should return an invalid response if endDate is missing", "fullTitle": "workUnitValuerSummary should return an invalid response if endDate is missing", "timedOut": false, "duration": 1, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: { username: 'john', startDate: '2022-01-01' },\n};\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'End date must be provided',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "de6d5173-e670-487e-85d9-e773bebb05e8", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}, {"title": "should return an invalid response if endDate is invalid", "fullTitle": "workUnitValuerSummary should return an invalid response if endDate is invalid", "timedOut": false, "duration": 0, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const event = {\n  queryStringParameters: {\n    username: 'john',\n    startDate: '2022-01-01',\n    endDate: '2022-13-01',\n  },\n};\nconst response = await workUnitValuerSummary(event);\nexpect(JSON.parse(response.body)).to.eql({\n  status: 'INVALID',\n  message: 'End date is invalid',\n});\nexpect(response.statusCode).to.equal(400);", "err": {}, "uuid": "791f26cc-84a4-40ab-bf67-c4747fe21030", "parentUUID": "c2d63366-7698-4dde-80fc-56798bd4d302", "isHook": false, "skipped": false}], "suites": [], "passes": ["0aaeb4f6-e6b0-4708-b3ba-b15bab0fd7e9", "cf5ae264-fb28-41d0-ad1a-6f4d3f469778", "14144487-6e51-4bbd-add2-e2157e4cf235", "9ea99d4f-e6b3-49e2-b0f4-4e4d972a387c", "de6d5173-e670-487e-85d9-e773bebb05e8", "791f26cc-84a4-40ab-bf67-c4747fe21030"], "failures": [], "pending": [], "skipped": [], "duration": 3, "root": false, "rootEmpty": false, "_timeout": 20000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 20000}], "meta": {"mocha": {"version": "9.2.2"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}