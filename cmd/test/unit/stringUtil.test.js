import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { expect } from 'chai';
import { DB_PASSWORD_LENGTH } from 'common/util/const.js';
import {
  generatePassword, getAppNameFromPodName, parseConsoleResult, parseQvUserDetail, parseQvUsers,
} from '../../src/util/stringUtil.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('generatePassword', () => {
  it('should generate a password of the correct length', () => {
    const password = generatePassword(DB_PASSWORD_LENGTH);
    expect(password.length).to.equal(DB_PASSWORD_LENGTH);
  });

  it('parse get pods result', () => {
    const data = `
NAME                                                            READY   STATUS             RESTARTS   AGE
home-valuation-0                                                0/1     CrashLoopBackOff   6          9m52s
property-0                                                      1/1     Running            0          91s

`;
    const objects = parseConsoleResult(data);
    expect(objects[0].NAME).to.equal('home-valuation-0');
    expect(objects[0].STATUS).to.equal('CrashLoopBackOff');
    expect(objects[1].NAME).to.equal('property-0');
    expect(objects[1].STATUS).to.equal('Running');
  });

  it('should get the app name', () => {
    const appName = getAppNameFromPodName('property-0');
    expect(appName).to.equal('property');

    const appName2 = getAppNameFromPodName('home-valuation-0');
    expect(appName2).to.equal('home-valuation');

    const appName3 = getAppNameFromPodName('home-valuation-1');
    expect(appName3).to.equal('home-valuation');
  });

  it('should parse Qv Users', () => {
    const input = `
distinguishedName : CN=Jimmy Tang,OU=Development and Testing,OU=Quotable,DC=qv,DC=co,DC=nz
name              : Jimmy Tang
objectClass       : user
objectGUID        : 059b2e2a-e9fe-4846-963a-1175ee6d4016
SamAccountName    : TangJ
SID               : S-1-5-21-*********-**********-**********-45864

distinguishedName : CN=Hayden Brown,OU=Development and Testing,OU=Quotable,DC=qv,DC=co,DC=nz
name              : Hayden Brown
objectClass       : user
objectGUID        : c2c6554b-c4f4-4a4a-94aa-68ac9c33aa83
SamAccountName    : BrownH
SID               : S-1-5-21-*********-**********-**********-46990
`;

    const users = parseQvUsers(input);

    expect(users.length).to.equal(2);
    expect(users[0].name).to.equal('Jimmy Tang');
    expect(users[0].samAccountName).to.equal('TangJ');
    expect(users[0].sid).to.equal('S-1-5-21-*********-**********-**********-45864');
    expect(users[1].name).to.equal('Hayden Brown');
    expect(users[1].samAccountName).to.equal('BrownH');
    expect(users[1].sid).to.equal('S-1-5-21-*********-**********-**********-46990');
  });

  it('should parse Qv User detail', () => {
    const input = `
DistinguishedName : CN=Leon Zhou,OU=Development and Testing,OU=Quotable,DC=qv,DC=co,DC=nz
Enabled           : True
GivenName         : Leon
Name              : Leon Zhou
ObjectClass       : user
ObjectGUID        : ed41fd42-b323-4503-b49f-816f20c7f2db
SamAccountName    : ZhouL
SID               : S-1-5-21-*********-**********-**********-46429
Surname           : Zhou
UserPrincipalName : <EMAIL>
`;

    const user = parseQvUserDetail(input);
    expect(user.enabled).to.equal('True');
  });
});
