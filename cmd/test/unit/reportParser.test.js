import chai from 'chai';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import {
  accumulateTestResults, parseNycCoverage, parseLint, parseMocha,
} from 'common/service/testReports.js';

const { expect } = chai;
describe('report parser', () => {
  it('should parse lint report', () => {
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sampleLintResult = fs.readFileSync(path.resolve(__dirname, './lint.txt'), 'utf8');
    const result = parseLint(sampleLintResult);
    const { problems, errors, warnings } = result;
    expect(problems).to.equal(25);
    expect(errors).to.equal(25);
    expect(warnings).to.equal(0);
  });
  it('should parse lint report with singular', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sampleLintResult = fs.readFileSync(path.resolve(__dirname, './lint2.txt'), 'utf8');
    // when
    const result = parseLint(sampleLintResult);
    // then
    const { problems, errors, warnings } = result;
    expect(problems).to.equal(1);
    expect(errors).to.equal(1);
    expect(warnings).to.equal(0);
  });
  it('parse mocha', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sampleLintResult = fs.readFileSync(path.resolve(__dirname, './mochawesome.json'), 'utf8');
    // when
    const result = parseMocha(sampleLintResult);
    // then
    const {
      passes, pending, failures, passPercent,
    } = result;
    expect(passes).to.equal(15);
    expect(pending).to.equal(0);
    expect(failures).to.equal(2);
    expect(passPercent).to.equal(88.24);
  });
  it('parse coverage', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sampleLintResult = fs.readFileSync(path.resolve(__dirname, './coverage_summary.json'), 'utf8');
    // when
    const result = parseNycCoverage(sampleLintResult);
    // then
    const {
      linesPct, statementsPct, functionsPct, branchesPct,
    } = result;
    expect(linesPct).to.equal(10.1);
    expect(statementsPct).to.equal(1.31);
    expect(functionsPct).to.equal(0.74);
    expect(branchesPct).to.equal(0.35);
  });
  it('parse coverage with unknown value', () => {
    // given
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const sampleLintResult = fs.readFileSync(path.resolve(__dirname, './coverage_summary2.json'), 'utf8');
    // when
    const result = parseNycCoverage(sampleLintResult);
    // then
    const {
      linesPct, statementsPct, functionsPct, branchesPct,
    } = result;
    expect(linesPct).to.equal(0);
    expect(statementsPct).to.equal(0);
    expect(functionsPct).to.equal(0);
    expect(branchesPct).to.equal(0);
  });

  it('add report', () => {
    let sum = {};
    const report = {
      lintProblemsCount: 25,
      lintErrorsCount: 25,
      lintWarningsCount: 0,
      coverageLinesPct: 0,
      coverageStatementsPct: 0,
      coverageFunctionsPct: 0,
      coverageBranchesPct: 0,
      testPasses: 2,
      testPending: 0,
      testSkipped: 86,
      testFailures: 0,
      testPassPercent: '100.00',
    };
    sum = accumulateTestResults(sum, report);
    console.log(sum);
  });
});
