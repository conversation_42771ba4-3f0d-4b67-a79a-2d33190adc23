import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { expect } from 'chai';
import { getMaxPrivateIPInSubnet, getNextIP } from '../../src/util/ipUtil.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('Ip Util', () => {
  it('should get next IP', () => {
    const ip = '***************';
    const nextIp = getNextIP(ip);
    expect(nextIp).to.equal('***************');
  });

  it('should cycle back to 0', () => {
    const ip = '***************';
    const nextIp = getNextIP(ip);
    expect(nextIp).to.equal('*********');
  });

  it('should return the correct maximum private IP in the subnet', () => {
    const privateIp = '************';
    const expectedMaxIp = '***************';
    expect(getMaxPrivateIPInSubnet(privateIp)).to.equal(expectedMaxIp);
  });
});
