import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { expect } from 'chai';
import { updateQivsWebConfig, updateSoapWebConfig } from '../../src/service/appEnvCreate.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('QIVS config script', () => {
  const __dirname = path.dirname(fileURLToPath(import.meta.url));
  const context = fs.readFileSync(path.resolve(__dirname, './files/context.json'), 'utf8');

  it('update qivs web config', async () => {
    // given
    const qivsWebConfigText = fs.readFileSync(path.resolve(__dirname, './files/qivs.config'), 'utf8');
    const appEnv = 'qivs24';
    const qivsIp = '************';

    const baseUrl = 'qivs24.qivs.internal.quotablevalue.co.nz';
    const serverName = 'indae01';
    // when
    const updatedConfig = await updateQivsWebConfig(appEnv, qivsIp, qivsWebConfigText, JSON.parse(context), baseUrl, serverName);

    // then
    const expectedLines = fs.readFileSync(path.resolve(__dirname, './files/expected-qivs.config'), 'utf8').split('\n');
    const actualLines = updatedConfig.split('\n');

    for (let i = 0; i < actualLines.length; i++) {
      expect(actualLines[i]).eq(expectedLines[i]);
    }
  });

  it('update soap web config ', async () => {
    // given
    const qivsWebConfigText = fs.readFileSync(path.resolve(__dirname, './files/soap.config'), 'utf8');
    const appEnv = 'qivs24';
    const qivsIp = '************';

    // when
    const updatedConfig = await updateSoapWebConfig(appEnv, qivsIp, qivsWebConfigText, JSON.parse(context));

    // then
    const expectedLines = fs.readFileSync(path.resolve(__dirname, './files/soap-expected.config'), 'utf8').split('\n');
    const actualLines = updatedConfig.split('\n');

    for (let i = 0; i < actualLines.length; i++) {
      expect(actualLines[i]).eq(expectedLines[i]);
    }
  });
});
