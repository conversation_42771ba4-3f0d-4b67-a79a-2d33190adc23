<?xml version="1.0" encoding="utf-8"?>
<configuration><!-- xmlns="http://schemas.microsoft.com/.NetConfiguration/v2.0"> -->

	<system.web>



			<compilation debug="true" />
			<customErrors mode="Off" />

					<authentication mode="Forms">
				<forms protection="All" slidingExpiration="false" />
			</authentication>

			<authorization>
				<deny users="?" />
			</authorization>


			<trace enabled="false" requestLimit="10" pageOutput="false" traceMode="SortByTime" localOnly="true" />

			<sessionState mode="InProc" stateConnectionString="tcpip=127.0.0.1:42424" sqlConnectionString="data source=127.0.0.1;user id=sa;password=" cookieless="false" timeout="20" />

			<globalization requestEncoding="utf-8" responseEncoding="utf-8" />
		    <browserCaps>
		<case match="^Mozilla/5\.0 \([^)]*\) (Gecko/[-\d]+)(?'VendorProductToken' (?'type'[^/\d]*)([\d]*)/(?'version'(?'major'\d+)(?'minor'\.\d+)(?'letters'\w*)))?">
			browser=Gecko
			<filter>
				<case match="(Gecko/[-\d]+)(?'VendorProductToken' (?'type'[^/\d]*)([\d]*)/(?'version'(?'major'\d+)(?'minor'\.\d+)(?'letters'\w*)))">
				type=${type}
				</case>
				<case>
					<!-- plain Mozilla if no VendorProductToken found -->
				type=Mozilla
				</case>
			</filter>
			frames=true
			tables=true
			cookies=true
			javascript=true
			javaapplets=true
			ecmascriptversion=1.5
			w3cdomversion=1.0
			css1=true
			css2=true
			xml=true
			msdomversion=6.0
			tagwriter=System.Web.UI.HtmlTextWriter
			<case match="rv:(?'version'(?'major'\d+)(?'minor'\.\d+)(?'letters'\w*))">
				version=${version}
				majorversion=0${major}
				minorversion=0${minor}
				<case match="^b" with="${letters}">
					beta=true
				</case>
			</case>
		</case>
		<case match="Chrome/(?'version'(?'major'\d+)\.(?'minor'\d+\.\d+).\d+)">
			browser=Chrome
			version=${version}
			majorversion=${major}
			minorversion=${minor}
			frames=true
			tables=true
			cookies=true
			javascript=true
			javaapplets=true
			ecmascriptversion=1.5
			w3cdomversion=1.0
			msdomversion=6.0
			css1=true
			css2=true
			xml=true
			tagwriter=System.Web.UI.HtmlTextWriter
		</case>
	</browserCaps>

			<identity impersonate="true" userName="gns.net" password="th1nsl1c3r" />
	  <httpRuntime executionTimeout="240" maxRequestLength="10240" />

	</system.web>


	<location path="WebServices">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>

	<location path="Reports/StreamImageForReport.aspx">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>

<appSettings>

    <add key="dbConnQIVS" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=qvnz_test" />
    <add key="dbConnMain" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=qvnz_test" />
    <add key="dbConnLoggingDB" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=qvnz_test" />

    <add key="dbConnInsurance" value="server=devdb02;uid=test_internal_user;Pwd=testing is fun;database=qv_insurance_test" />
    <add key="dbConnDemographics" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=infobase_demographics" />
    <add key="dbConnResources" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=qvnz_resources" />
    <add key="dbConnEsam" value="server=devdb01;uid=test_internal_user;Pwd=testing is fun;database=esam" />
    <add key="dbConnSalesDirect" value="server=devawsapp01;uid=test_internal_user;Pwd=testing is fun;database=SalesDirect_test" />



	<add key="LoggingLevel" value="1" />

		<add key="SmtpServer" value="***********" />


		<add key="BaseURL" value="http://testawsinweb01-qivs" />
        <add key="LocalHostURL" value="http://testawsinweb01-qivs" />
		<add key="BaseQIVSInternalURL" value="http://testawsinweb01-qivs" />



		<add key="PropertyPhotoDirectorySize" value="10000" />
		<add key="PropertyPhotoDirectory" value="\\testawsinweb01\TEST\PropertyPhotos" />
		<add key="PropertyPhotoVirtualDir" value="PhotoImages" />
		<add key="PropertyPhotoImageDir" value="Images" />
		<add key="PropertyPhotoThumbDir" value="Thumbnails" />
		<add key="PropertyPhotoStandardWidth" value="400" />
		<add key="PropertyPhotoThumbnailWidth" value="160" />
		<add key="PropertyPhotoStandardFileSizeInKB" value="40" />
		<add key="PropertyPhotoQuality" value="75" />
		<add key="PropertyPhotoResolution" value="300" />
        <add key="PropertyPhotoExternalAccessDomainName" value="QVNZ" />
		<add key="PropertyPhotoExternalAccessUsername" value="username" />
		<add key="PropertyPhotoExternalAccessPassword" value="password" />
		<add key="PropertyPhotoGetPhotoTemplate" value="/PropertyPhoto/getPropertyPhoto.aspx" />

		<add key="ObjectionSignatureWidth" value="246" />
		<add key="ObjectionSignatureResolution" value="300" />
		<add key="ObjectionSignatureQuality" value="100" />
		<add key="ObjectionSignatureSize" value="20" />
		<add key="ObjectionSignatureHeight" value="52" />

		<add key="xslTemplatePath" value="C:\qvnz\web\QIVS\ERollSlips\Templates\Rollslip.xsl" />
		<!-- <add key="xslTemplatePath" value="http://testawsinweb01-qivs/ERSTemplates/Rollslip.xsl" /> -->
		<add key="xslTemplateVDir" value="~/Templates/" />

		<add key="DirectoryQueryPrefix" value="WinNT://QVNZ/" />
		<add key="DirectoryQueryUsername" value="QIVSSecurity" />
		<add key="DirectoryQueryPassword" value="dr1vers" />

		<!-- settings for caching datasets from the database -->
		<add key="CacheLong_Hours" value="24" />
		<add key="CacheMed_Mins" value="360" />
		<add key="CacheShort_Mins" value="30" />

		<add key="ScheduleReportPath" value="~/RSReports/ScheduleReport.aspx" />
		<add key="ScheduleReportPathvwa" value="~/RSReports/ScheduleReport_vwa.aspx" />
		<add key="ReportPrefix" value="/TEST/QIVS Reports/" />
		<!--add key="QV.Report.ReportingService" value="http://testawsrpt01:7080/ReportServer/ReportService.asmx" /-->
		<!--add key="QV.Report.MSReportingServices.ReportingService" value="http://testawsrpt01:7080/ReportServer/ReportService.asmx"/-->
		<!--add key="QV.Report.ReportStreamer.FileHelper" value="http://testawsrpt01:7080/ReportStreamer_TEST/FileHelper.asmx" /-->

		<add key="QV.Report.MSReportingServices.ReportingService" value="http://devdb02:7080/ReportServer/ReportService2010.asmx"/>
		<add key="QV.Report.ReportStreamer.FileHelper" value="http://devdb02:7080/ReportStreamer/FileHelper.asmx" />

		<add key="ReturnFromScheduleReportURL" value="/reports.asp" />
		<add key="ReportStore" value="\\devdb01\TEST\RS" />
		<add key="ReportShowErrorMessages" value="true" />
		<add key="ReportScheduleDelaySeconds" value="90" />
		<add key="ReportingServicesDomain" value="QVDEV" />
		<add key="ReportingServicesUsername" value="Reporting_Services" />
		<add key="ReportingServicesPassword" value="f@tm@n" />
		<add key="StreamImageForReportPath" value="/Reports/StreamImageForReport.aspx" />

		<!-- Value Maps -->
        <add key="ValueMapPath" value="\\testawsinweb01\value_maps_comments" />
		<add key="ValueMapCachedPath" value="\\testawsinweb01\value_maps_comments\cache" />
		<add key="ValueMapImageWidth" value="1197" />
		<add key="ValueMapImageHeight" value="639" />
		<add key="ValueMapImageResolution" value="200" />
		<add key="ValueMapImageQuality" value="75" />

        <add key="MarketCommentPath" value="\\testawsinweb01\value_maps_comments" />
        <add key="MarketCommentFileName" value="MarketComment" />

        <!-- Objection Documents -->
        <add key="ObjectionDocumentStandardFileSizeInKB" value="10240" />

		<!-- Floor Plans -->
		<add key="FloorPlanCachePath" value="\\testawsinweb01\TEST\Plans\floor_plans\cache" />
		<add key="FloorPlanStandardWidth" value="2048" />
		<add key="FloorPlanThumbnailWidth" value="160" />
		<add key="FloorPlanResolution" value="300" />
		<add key="FloorPlanQuality" value="100" />
		<add key="FloorPlanStandardFileSizeInKB" value="350" />
		<add key="FloorPlanImageEncoderType" value="image/gif" />
		<add key="FloorPlanImageFileFormat" value="gif" />

		<!-- Site Plans -->
		<add key="SitePlanCachePath" value="\\testawsinweb01\TEST\Plans\site_plans\cache" />
		<add key="SitePlanStandardWidth" value="1470" />
		<add key="SitePlanThumbnailWidth" value="160" />
		<add key="SitePlanResolution" value="96" />
		<add key="SitePlanQuality" value="100" />
		<add key="SitePlanStandardFileSizeInKB" value="2048" />
		<add key="SitePlanImageEncoderType" value="image/jpeg" />
		<add key="SitePlanImageFileFormat" value="jpeg" />
		<add key="SitePlanMapCaptureWidth" value="1470" />
		<add key="SitePlanMapCaptureHeight" value="940" />

		<!-- Survey Plans -->
		<add key="SurveyPlanStandardWidth" value="400" />
		<add key="SurveyPlanThumbnailWidth" value="160" />
		<add key="SurveyPlanResolution" value="300" />
		<add key="SurveyPlanQuality" value="75" />
		<add key="SurveyPlanStandardFileSizeInKB" value="2048" />

		<add key="MaxAllowedUncompressedImageFileSize" value="10240"/>

		<!-- these settings required for the sales portal admin screens -->
		<add key="SalesPortalEmailFromAddress" value="<EMAIL>" />
		<add key="SalesPortalEmailRevokeAddress" value="<EMAIL>" />
		<add key="SalesPortalEmailCrlUpdateAddress" value="<EMAIL>" />
		<add key="CertExpiryReportMonths" value="1" />
		<add key="CertInstallInstructionsPDF" value="C:\qvnz\web\SalesPortal\Download certificate.pdf" />

		<!-- ATP certificate services required for Sales Portal -->
		<add key="ATPNZSample.ClientCertificates.ASPNET.IPS.Security.Certificates.ClientCertificates" value="http://qvinweb02/IPSCerts/ClientCertificates.asmx" />
		<add key="IPS.CertificateServices.User" value="Salesportal" />
		<add key="IPS.CertificateServices.Password" value="franklymydearidontgiveadamn04" />
		<add key="IPS.CertificateServices.Domain" value="qvinweb02" />
		<add key="ScheduleReportPath" value="~/RSReports/ScheduleReport.aspx" />
		<add key="ScheduleReportPathvwa" value="~/RSReports/ScheduleReport_vwa.aspx" />

		<add key="WebServerIPAddress" value="***********" />
		<add key="WMSURL" value="http://devawsmap01:8080/QIVSWMS/servlet/WMSServlet" />
		<add key="WMSMapURL" value="http://devawsmap01:8080/QIVSWMS/servlet/MapServlet" />
		<add key="WMSLegendURL" value="http://devawsmap01:8080/QIVSWMS/servlet/LegendServlet" />
		<add key="WMSInvalidHTTPCode" value="403" />
		<add key="WMSErrorHTTPCode" value="500" />
		<add key="WMSBytesToRead" value="1024" />
		<add key="PrintableMap_A4_Width" value="680" />
		<add key="PrintableMap_A4_Height" value="940" />
		<add key="PrintableMap_A3_Width" value="1470" />
		<add key="PrintableMap_A3_Height" value="940" />

		<!-- <add key="ExtractDirectoryPath" value="\testawsinweb01\TEST\ReportsText" /> -->
		<add key="ExtractDirectoryPath" value="C:\ReportsText" />
        <add key="ExtractDirectoryName" value="ReportsText" />

        <add key="CustomAccessLogoPath" value="\\testawsinweb01\CustomLogos\" />

	<!-- Insurance Report  -->
	<add key="ReportGetInsurancePhotoPath" value="\\testawsinweb01\TEST\InsuranceImages\Images" />
	<add key="ReportGetQIVSPhotoPath" value="\\testawsinweb01\TEST\PropertyPhotos\Images" />

	<!-- Monarch ConfigSettings -->

	<add key="MonarchTLARejectedUrl" value="https://test.qvmonarch.co.nz/api/objection/reinstatement-from-qivs"/>
	<add key="MonarchActionObjectionUrl" value="https://test.qvmonarch.co.nz/api/objection/action-from-qivs"/>
	<add key="MonarchWithdrawObjectionUrl" value="https://test.qvmonarch.co.nz/api/objection/withdraw-from-qivs"/>

	<add key="MonarchPhotoUrl" value="https://test.qvmonarch.co.nz/property/property/Search?qupid={0}" />
	<add key="MonarchQuickSearchUrl" value="https://test.qvmonarch.co.nz/property/property" />
	<add key="MonarchPhotoRelinkUrl" value="https://test.qvmonarch.co.nz/property/property/Relink?"/>
	<add key="MonarchSaleUrl" value="https://test.qvmonarch.co.nz/roll-maintenance/{0}/sale/{1}" />

        <add key="MonarchPropertyDetailUrl" value="https://test.qvmonarch.co.nz/property/{0}/detail"/>
        <add key="MonarchRollMaintenanceSearchUrl" value="https://test.qvmonarch.co.nz/roll-maintenance?qpid={0}"/>
        <add key="MonarchRollMaintenanceValuation" value="https://test.qvmonarch.co.nz/roll-maintenance/{0}/latest-valuation"/>

	<add key="QV.SystemFramework.QivsWrapper.QivsWrapperSoap" value="http://soapwrappertest:64206/QivsWrapperWS.asmx"/>
	<add key="MonarchPhotoCountUrl" value="http://soapwrappertest:64206/QivsWrapperWS.asmx/GetPhotoCount?qivsQupid="/>
	<add key="IsMonarchPhotoRelinkCompleteUrl" value="http://soapwrappertest:64206/QivsWrapperWS.asmx/IsRelinkComplete?"/>

	<!-- DEVELOPMENT (Mikes machine) -->
	<!--	<add key="SalesDirectApiUrl" value= "http://REV22427:63183/QvInternal/api"/> -->
	<!-- TEST -->
	<add key="SalesDirectApiUrl" value= "http://devawsapp01:64183/QvInternal/api"/>
	<!-- PROD -->
	<!-- 	<add key="SalesDirectApiUrl" value= "http://qvartapp01:60033/QvInternal/api"/> -->

	<add key="SalesDirectPdfUrl" value= "http://devawsapp01:64184/QvBep/api/download/file/" />
	<add key="SalesDirectMinSaleid" value="75511" />

	<!-- Url of the Objection Workpacket Report Windows service REST API -->
	<add key="ObjectionWorkpacketReport" value="http://devawsapp01:64193/WcfRestService/workpacketreport"/>

	<!-- Url of the Legacy Wrapper SOAP Web Service -->
	<add key="QIVS.LegacyWrapper.Utilities" value="http://testawsrpt01/LegacyWrapper/Utilities.asmx"/>

        <!-- If this flag is FALSE then it acts as an override and hides the Top Menu Quick search option and displays the QIVS Property Photo link  -->
        <add key="IsMonarchAvailable" value="TRUE" />

        <!-- define Auth0 connection parameters to enable QIVS to sign the user into Auth0.  -->
        <add key="Auth0Domain" value="qvmonarch-dev.au.auth0.com" />
        <add key="Auth0ClientId" value="waWLuxADVQVBbooDZ67EXWpddCj5XNmM" />
        <add key="UseAuth0" value="false" />

        <!-- integration between QIVS and vWork for creating / editing valuation jobs -->
	<add key="UseVWork" value="false" />
	<!-- STunnel running locally on TCP Port 8443 -->
	<add key="VWorkAPIUrl" value="http://localhost:8443/api/v4" />
	<!-- <add key="VWorkAPIUrl" value="https://api.vworkapp.com/api/v4" /> -->
	<add key="VWorkAPIKey" value="kHklbK9OWs2wvSk0Jb"/>
	<add key="VWorkAPIProxyHost" value=""/>
	<add key="VWorkAPIProxyPort" value=""/>
	<add key="VWorkAPIProxyUsername" value=""/>
	<add key="VWorkAPIProxyPassword" value=""/>


        <!-- WebUIValidation.js library client side validation -->
        <add key="ClientValidationEnabled" value="true" />
        <add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />

        <add key="MonarchBaseUrl" value="https://test.qvmonarch.co.nz"/>
        <add key="MonarchCurrentRuralWorksheetUrlPath" value="/roll-maintenance/rural-worksheet/{0}"/>
        <add key="MonarchRevisionRuralWorksheetUrlPath" value="/roll-maintenance/rural-revision-worksheet/{0}"/>

	</appSettings>
	<system.webServer>
		<handlers>
			<add name="ASPNET-ISAPI-1.1-WebServiceHandlerFactory" path="*.asmx" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.Net\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
			<add name="ASPNET-ISAPI-1.1-SimpleHandlerFactory" path="*.ashx" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.Net\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
			<add name="ASPNET-ISAPI-1.1-HttpRemotingHandlerFactory-soap" path="*.soap" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.Net\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
			<add name="ASPNET-ISAPI-1.1-PageHandlerFactory" path="*.aspx" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.Net\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
			<add name="ASPNET-ISAPI-1.1-HttpRemotingHandlerFactory-rem" path="*.rem" verb="*" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.Net\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
			<remove name="ASPNET-ISAPI-1.1-AXD" />
			<add name="ASPNET-ISAPI-1.1-AXD" path="*.axd" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll" resourceType="Unspecified" requireAccess="Script" preCondition="classicMode,runtimeVersionv1.1,bitness32" />
		</handlers>
		   <httpErrors errorMode="Detailed" />
       <!-- <defaultDocument>
            <files>
                <remove value="default.aspx" />
                <remove value="iisstart.htm" />
                <remove value="index.html" />
                <remove value="index.htm" />
                <remove value="Default.htm" />
            </files>
        </defaultDocument> -->
        <security>
            <requestFiltering>
                <fileExtensions>
                    <add fileExtension=".GET,HEAD,POST,TRACE" allowed="true" />
                </fileExtensions>
            </requestFiltering>
        </security>
        <defaultDocument>
            <files>
                <clear />
                <add value="Default.asp" />
                <add value="Default.htm" />
                <add value="index.htm" />
                <add value="index.html" />
                <add value="iisstart.htm" />
                <add value="default.aspx" />
            </files>
        </defaultDocument>

	</system.webServer>

 </configuration>
