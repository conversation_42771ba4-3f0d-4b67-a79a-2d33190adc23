{"$metadata": {"httpStatusCode": 200, "requestId": "3b5a6415-243f-421e-9044-f9b9fd883393", "attempts": 1, "totalRetryDelay": 0}, "created": "2025-02-24T00:07:38.954Z", "pipelineName": "api-stats-pipeline-fp10", "pipelineVersion": 1, "stageStates": [{"actionStates": [{"actionName": "Source", "currentRevision": {"revisionId": "QTm6o3NNT5inLqEW4I3N3OreoiwZPvO."}, "entityUrl": "https://console.aws.amazon.com/s3/home?region=ap-southeast-2#", "latestExecution": {"actionExecutionId": "2e229edf-a3c5-4b0d-93e0-0a07eb93054a", "externalExecutionId": "QTm6o3NNT5inLqEW4I3N3OreoiwZPvO.", "lastStatusChange": "2025-02-24T02:08:40.493Z", "status": "Succeeded", "summary": "v0.0.2-1-g8d15b9c master"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "d69ee05d-d249-4539-b3b7-46ab86161d73", "status": "Succeeded"}, "stageName": "Source"}, {"actionStates": [{"actionName": "Terraform-Plan", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-stats-terraform-fp10/view", "latestExecution": {"actionExecutionId": "12c7483b-c106-4015-aeb3-cdd2e756cf1e", "externalExecutionId": "api-stats-terraform-fp10:5985dbec-2fc0-439c-82f7-2b41beea0c40", "externalExecutionUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/builds/api-stats-terraform-fp10:5985dbec-2fc0-439c-82f7-2b41beea0c40/view/new", "lastStatusChange": "2025-02-24T02:08:40.886Z", "status": "InProgress"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "d69ee05d-d249-4539-b3b7-46ab86161d73", "status": "InProgress"}, "stageName": "Terraform"}, {"actionStates": [{"actionName": "Deploy", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-stats-deploy-fp10/view", "latestExecution": {"actionExecutionId": "140ad826-7d3c-4cdf-b069-684683aa40f1", "errorDetails": {"code": "JobFailed", "message": "Build terminated with state: FAILED. Phase: BUILD, Code: COMMAND_EXECUTION_ERROR, Message: Error while executing command: npx serverless deploy --stage $ENV --verbose -r $AWS_REGION --aws-profile deploy. Reason: exit status 1"}, "externalExecutionId": "api-stats-deploy-fp10:b0479579-8eae-4cfb-9107-f65a06dcffa3", "externalExecutionUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/builds/api-stats-deploy-fp10:b0479579-8eae-4cfb-9107-f65a06dcffa3/view/new", "lastStatusChange": "2025-02-24T02:08:10.141Z", "status": "Failed"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "746dff2a-f03c-4b2e-bac2-09b5c9f3c1ba", "status": "Failed"}, "stageName": "Deploy"}], "updated": "2025-02-24T00:07:38.954Z"}