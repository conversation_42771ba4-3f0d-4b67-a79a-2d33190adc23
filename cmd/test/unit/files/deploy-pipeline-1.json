{"$metadata": {"httpStatusCode": 200, "requestId": "e78d1bf6-878e-4c33-a0fa-e9c767402cc0", "attempts": 1, "totalRetryDelay": 0}, "created": "2025-02-25T00:18:47.169Z", "pipelineName": "api-floor-plan-pipeline-fptest2", "pipelineVersion": 1, "stageStates": [{"actionStates": [{"actionName": "Source", "currentRevision": {"revisionId": "YMzOeRHvOG37raIyrSR0iZyrqlkiCX6a"}, "entityUrl": "https://console.aws.amazon.com/s3/home?region=ap-southeast-2#", "latestExecution": {"actionExecutionId": "361ee1fb-af35-49ce-9d66-544a8fc217ba", "externalExecutionId": "YMzOeRHvOG37raIyrSR0iZyrqlkiCX6a", "lastStatusChange": "2025-02-25T00:36:18.801Z", "status": "Succeeded", "summary": "v0.0.1-2-ge54b34e master"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "8940f093-ff5b-4c64-a35f-f4d42a0e74bb", "status": "Succeeded"}, "stageName": "Source"}, {"actionStates": [{"actionName": "Terraform-Plan", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-floor-plan-terraform-fptest2/view", "latestExecution": {"actionExecutionId": "2483f123-fcde-48f0-bed0-1f4b6bae9303", "externalExecutionId": "api-floor-plan-terraform-fptest2:f133e360-3e6d-4118-ab8f-35f88ab37cf5", "externalExecutionUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/builds/api-floor-plan-terraform-fptest2:f133e360-3e6d-4118-ab8f-35f88ab37cf5/view/new", "lastStatusChange": "2025-02-25T00:39:56.791Z", "status": "Succeeded"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "8940f093-ff5b-4c64-a35f-f4d42a0e74bb", "status": "Succeeded"}, "stageName": "Terraform"}, {"actionStates": [{"actionName": "Deploy", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-floor-plan-deploy-fptest2/view", "latestExecution": {"actionExecutionId": "da74a251-1adf-48db-835d-23b92f770796", "externalExecutionId": "api-floor-plan-deploy-fptest2:1fb293f5-dbd1-494f-b945-6a4973309477", "externalExecutionUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/builds/api-floor-plan-deploy-fptest2:1fb293f5-dbd1-494f-b945-6a4973309477/view/new", "lastStatusChange": "2025-02-25T00:39:57.211Z", "status": "InProgress"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "8940f093-ff5b-4c64-a35f-f4d42a0e74bb", "status": "InProgress"}, "stageName": "Deploy"}], "updated": "2025-02-25T00:18:47.169Z"}