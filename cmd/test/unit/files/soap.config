<?xml version="1.0" encoding="utf-8"?>
<!--
    Note: As an alternative to hand editing this file you can use the
    web admin tool to configure settings for your application. Use
    the Website->Asp.Net Configuration option in Visual Studio.
    A full list of settings and comments can be found in
    machine.config.comments usually located in
    \Windows\Microsoft.Net\Framework\vx.x\Config
-->
<configuration>
  <configSections>
    <sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
        <section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
        <sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
          <section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="Everywhere" />
          <section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
          <section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
          <section name="roleService" type="System.Web.Configuration.ScriptingRoleServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
        </sectionGroup>
      </sectionGroup>
    </sectionGroup>
  </configSections>
  <appSettings>
    <!-- log4net Config-->
    <add key="log4net.Config" value="log4net.config" />
    <add key="log4net.Config.Watch" value="True" />
    <!-- configuration for Nginx -->
    <add key="nginxUserID" value="%QIVSWRAPPERSOAP_NGINXUSER_ID" />
    <add key="nginxPassword" value="%QIVSWRAPPERSOAP_NGINXUSER_PASSWORD" />
    <add key="nginxEnabled" value="true" />
    <add key="authHeaderName" value="X-Authenticated-User" />
    <add key="authHeaderValue" value="qivs" />
    <!-- Get Photo UUid Config-->
    <add key="propertyKind" value="identifierLodgement" />
    <add key="propertyAction" value="OBTAIN_IF_NOT_EXIST" />
    <add key="propertyqivsSelf" value="/consents/u980fy89yhjef323kjyhg8765s45s" />
    <!--add key="propertyUriStringValue" value="https://test.qvmonarch.co.nz/" /-->
    <add key="propertyUriStringValue" value="http://internal-Monarch-test-PrivateALB-178025222.ap-southeast-2.elb.amazonaws.com:8080/" />
    <add key="propertyRequestUriValue" value="property/properties/identifier_lodgement" />
    <add key="propertyMediaTypeValue" value="application/json" />
    <!-- GetPhotoMetaData Config-->
    <!--add key="photoMetaDataUriStringValue" value="https://test.qvmonarch.co.nz/" /-->
    <add key="photoMetaDataUriStringValue" value="http://internal-Monarch-test-PrivateALB-178025222.ap-southeast-2.elb.amazonaws.com:8080/" />
    <add key="photoMetaDataRequestUriValue" value="property/properties/" />
    <add key="photoMetaDataMediaTypeValue" value="application/json" />
    <!-- Insert or Update Photo and MetaData Config-->
    <add key="updateKind" value="photoUploadInitiation" />
    <add key="updateStatus" value="UPLOAD_COMPLETED" />
    <!--UPLOAD_INITIATED-->
    <add key="updateSelf" value="property/properties/u980fy89yhjef323kjyhg8765s45s/photos" />
    <!--add key="updateMetaDataUriStringValue" value="https://test.qvmonarch.co.nz/" /-->
    <add key="updateMetaDataUriStringValue" value="http://internal-Monarch-test-PrivateALB-178025222.ap-southeast-2.elb.amazonaws.com:8080/" />
    <add key="updateMetaDataRequestUriValue" value="property/properties/" />
    <add key="updateMetaDataMediaTypeValue" value="application/json" />



  </appSettings>
  <connectionStrings />
  <system.web>
    <webServices>
      <protocols>
        <add name="HttpGet" />
      </protocols>
    </webServices>
    <!--
            Set compilation debug="true" to insert debugging
            symbols into the compiled page. Because this
            affects performance, set this value to true only
            during development.
        -->
    <compilation>
      <assemblies>
        <add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
      </assemblies>
    </compilation>
    <!--
          The <authentication> section enables configuration
          of the security authentication mode used by
          ASP.NET to identify an incoming user.
        -->
    <authentication mode="Windows" />
    <!--
           The <customErrors> section enables configuration
           of what to do if/when an unhandled error occurs
           during the execution of a request. Specifically,
           it enables developers to configure html error pages
           to be displayed in place of a error stack trace.

           <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
             <error statusCode="403" redirect="NoAccess.htm" />
             <error statusCode="404" redirect="FileNotFound.htm" />
           </customErrors>
        -->
    <pages>
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </controls>
    </pages>
    <httpHandlers>
      <remove verb="*" path="*.asmx" />
      <add verb="*" path="*.asmx" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add verb="*" path="*_AppService.axd" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
    </httpHandlers>
    <httpModules>
      <add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    </httpModules>
  </system.web>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" type="Microsoft.CSharp.CSharpCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
        <providerOption name="CompilerVersion" value="v3.5" />
        <providerOption name="WarnAsError" value="false" />
      </compiler>
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" type="Microsoft.VisualBasic.VBCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
        <providerOption name="CompilerVersion" value="v3.5" />
        <providerOption name="OptionInfer" value="true" />
        <providerOption name="WarnAsError" value="false" />
      </compiler>
    </compilers>
  </system.codedom>
  <!--
        The system.webServer section is required for running ASP.NET AJAX under Internet
        Information Services 7.0.  It is not necessary for previous version of IIS.
    -->
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules>
      <remove name="ScriptModule" />
      <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    </modules>
    <handlers>
      <remove name="WebServiceHandlerFactory-Integrated" />
      <remove name="ScriptHandlerFactory" />
      <remove name="ScriptHandlerFactoryAppServices" />
      <remove name="ScriptResource" />
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    </handlers>
  </system.webServer>
  <runtime>
    <assemblyBinding appliesTo="v2.0.50727" xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Extensions" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Extensions.Design" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.net>
    <defaultProxy useDefaultCredentials="true">
      <proxy usesystemdefault="True" />
    </defaultProxy>
  </system.net>
</configuration>
<!--ProjectGuid: {d5667900-538c-4ad8-b168-29b7114e3dfc}-->