{"$metadata": {"httpStatusCode": 200, "requestId": "a52134c4-41da-43ad-bf2b-463050e23ea2", "attempts": 1, "totalRetryDelay": 0}, "created": "2025-02-25T00:19:04.053Z", "pipelineName": "api-maps-pipeline-fptest2", "pipelineVersion": 1, "stageStates": [{"actionStates": [{"actionName": "Source", "currentRevision": {"revisionId": "s5mcQNlpr.qaUvX.klSasCD.TKLO.y9b"}, "entityUrl": "https://console.aws.amazon.com/s3/home?region=ap-southeast-2#", "latestExecution": {"actionExecutionId": "791abd8b-6e9a-484b-8b05-61e40eecad29", "externalExecutionId": "s5mcQNlpr.qaUvX.klSasCD.TKLO.y9b", "lastStatusChange": "2025-02-25T00:43:27.322Z", "status": "Succeeded", "summary": "v1.2.1-22-g6517d1e master"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "99938e67-8065-44a7-81cb-4c68d6f8241c", "status": "Succeeded"}, "stageName": "Source"}, {"actionStates": [{"actionName": "Terraform-Plan", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-maps-terraform-fptest2/view", "latestExecution": {"actionExecutionId": "9ee24bde-40f3-4784-ae24-20d36adddf03", "externalExecutionId": "api-maps-terraform-fptest2:3b4f9da5-c25a-4bd4-bafa-0f8d88a59c85", "externalExecutionUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/builds/api-maps-terraform-fptest2:3b4f9da5-c25a-4bd4-bafa-0f8d88a59c85/view/new", "lastStatusChange": "2025-02-25T00:43:27.816Z", "status": "InProgress"}}], "inboundTransitionState": {"enabled": true}, "latestExecution": {"pipelineExecutionId": "99938e67-8065-44a7-81cb-4c68d6f8241c", "status": "InProgress"}, "stageName": "Terraform"}, {"actionStates": [{"actionName": "Deploy", "entityUrl": "https://console.aws.amazon.com/codebuild/home?region=ap-southeast-2#/projects/api-maps-deploy-fptest2/view"}], "inboundTransitionState": {"enabled": true}, "stageName": "Deploy"}], "updated": "2025-02-25T00:19:04.053Z"}