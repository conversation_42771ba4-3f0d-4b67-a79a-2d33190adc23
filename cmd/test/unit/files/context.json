{"PUBLIC_WEB_URL": "https://dev.quotablevalue.co.nz", "APPLICATION_SECRET": "APPLICATION_SECRET", "GOOGLE_API_KEY": "GOOGLE_API_KEY", "QVWEB-S3-BUCKET": "https://s3.ap-southeast-2.amazonaws.com/devqvconz-content/media/reports/", "QVWEB-GENERATE-PROPERTY-HOMEVAL-ID": "bf874b5f-b0dd-497b-9dc5-2dc006283703", "GOOGLE-API-KEY": "AIzaSyDBOFd5GsFpz2GgcUm_572oR4AikIaAqho", "AWS_S3_QVCONZ_BUCKET": "devqvconz-content/media", "POSTGRES_SCHEMA": "property", "S3_QV_ORIGINAL": "qv-property-photos-migration-original", "S3_QV_RESIZED": "qv-property-photos-migration-resized", "S3_CHART_IMAGE_BUCKET": "devqvconz-content", "S3_BUCKET_ACCESS_KEY_ID": "AKIAVNXUHZWWW67WTKUE", "S3_BUCKET_ACCESS_KEY_SECRET": "S3_BUCKET_ACCESS_KEY_SECRET", "QIVS_WEB_UI": "https://qivs24.qivs.internal.quotablevalue.co.nz", "QIVS_MAPPING_SERVICE_URL": "http://devawsmap01.local:8080/QIVSWMS", "PLAY_SECURE_SESSION_COOKIE": "TRUE", "AWS_PEER_REVIEW_BUCKET": "peerreviewform-dev", "AWS_PEER_REVIEW_FORM": "PeerReviewForm.docx", "EMAIL_TO": "<EMAIL>", "EMAIL_FROM": "<EMAIL>", "EMAIL_FROM_NAME": "DEV ACC", "REVIEW_FAILED_RECIPIENT_OVERRIDE": "<EMAIL>", "EMAIL_SMTP_PORT": "587", "EMAIL_HOST": "email-smtp.us-west-2.amazonaws.com", "EMAIL_USER": "AKIAJZ3RPMH5W2HCLL4A", "EMAIL_PASS": "pwd", "MAPS_GEOSERVER_URL": "https://testqvms.quotable.co.nz:8443", "MAPS_GEOSERVER_PROXY_URL": "/api/maps", "AUTH0_CLIENT_SECRET": "AUTH0_CLIENT_SECRET", "AUTH0_CLIENT_ID": "waWLuxADVQVBbooDZ67EXWpddCj5XNmM", "AUTH0_DOMAIN": "login.dev.qvmonarch.co.nz", "SAMBA_PASSWORD": "pwd", "ENV_NAME": "qivs24", "POSTGRES_HOST": "************", "POSTGRES_USER": "test4_pg_user", "POSTGRES_PASS": "pwd", "POSTGRES_DB": "test4_property_service", "POSTGRES_DB_URL": "*******************************************************************", "DB_APIHUB_URL": "*************************************************************************************************************************", "DB_QIVS_URL": "***********************************************************************************************************************", "DB_QVNZ_PUBLIC_URL": "***************************************************************************************************************************", "MSSQL_DB_HOST": "************", "QIVS_MAIN_DB": "test4_qvnz", "QIVS_MAIN_USER": "test4_monarch_services", "QIVS_MAIN_PASSWORD": "pwd", "QIVS_USER": "test4_qivs_user", "QIVS_PASSWORD": "pwd", "APIHUB_DB": "test4_qv_apihub", "APIHUB_USER": "test4_qvapihub_user", "APIHUB_PASSWORD": "pwd", "REPORT_DB": "test4_report_generator", "REPORT_USER": "test4_report_generator_user", "REPORT_PASSWORD": "pwd", "QVMS_DB": "qvwms3", "QVMS_DB_HOST": "***********", "QVMS_USER": "test4_monarch_services", "QVMS_PASSWORD": "pwd", "REDSHIFT_SERVER": "kaba-test-redshiftcluster.c2unr0sd7noa.ap-southeast-2.redshift.amazonaws.com", "REDSHIFT_PORT": "5439", "REDSHIFT_DATABASE": "dw_test", "REDSHIFT_USERNAME": "qvadmin", "REDSHIFT_PASSWORD": "pwd", "MONARCH_WEB_URL": "https://qivs24.monarch.internal.quotablevalue.co.nz", "AUTH0_REDIRECT_URI": "https://qivs24.monarch.internal.quotablevalue.co.nz/callback", "AUTH0_RETURN_URI": "https://qivs24.monarch.internal.quotablevalue.co.nz/property", "QVWEB-API-URL": "https://qivs24.dae.qvapi.co.nz/customer-reports", "RURAL_SALES_LAMBDA_API_URL": "https://qivs24.dae.qvapi.co.nz/sale-analysis", "LAMBDA_API_URL": "https://qivs24.dae.qvapi.co.nz/customer-reports", "SALES_LAMBDA_API_URL": "https://qivs24.dae.qvapi.co.nz/sales", "MAPS_API_URL": "https://qivs24.dae.qvapi.co.nz/maps", "PROPERTY_API_URL": "https://qivs24.dae.qvapi.co.nz/property", "FLOOR_PLAN_MEASURE_API_URL": "https://qivs24.dae.qvapi.co.nz/floor-plan", "LINZ_SEARCH_API_URL": "https://qivs24.dae.qvapi.co.nz/search", "RURAL_WORKSHEET_API_URL": "https://qivs24.dae.qvapi.co.nz/worksheet", "SEARCH_API_HOST": "https://qivs24.dae.qvapi.co.nz/search", "PDF_GENERATOR_API_URL": "https://qivs24.dae.qvapi.co.nz/pdf-report-generator", "RTV_API_URL": "https://qivs24.dae.qvapi.co.nz/rtv", "API_PICKLIST_API_URL": "https://qivs24.dae.qvapi.co.nz/picklist", "API_OBJECTION_API_URL": "https://qivs24.dae.qvapi.co.nz/objection", "API_STATS_API_URL": "https://qivs24.dae.qvapi.co.nz/stats", "API_REPORT_GENERATOR_API_URL": "https://qivs24.dae.qvapi.co.nz/report-generator", "REPORT_GENERATOR_SQS_URL": "https://sqs.ap-southeast-2.amazonaws.com/373100629421/report-generator-qivs24", "REPORT_GENERATOR_S3_BUCKET": "qv-dev-reports", "REPORT_GENERATOR_S3_DIRECTORY": "report-generator-qivs24", "MONARCH_SERVICES_URL": "https://internal-Monarch-qivs24-PrivateALB-1641608650.ap-southeast-2.elb.amazonaws.com", "KAFKA-BOOTSTRAP-SERVERS": "************:9092", "API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "API_PICKLIST_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "API_OBJECTION_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "API_STATS_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "API_REPORT_GENERATOR_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "RTV_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "QVWEB-API-KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "RURAL_SALES_LAMBDA_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "CUSTOMER_REPORTS_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "SALES_LAMBDA_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "MAPS_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "PROPERTY_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "FLOOR_PLAN_MEASURE_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "LINZ_SEARCH_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "RURAL_WORKSHEET_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "SEARCH_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "PDF_GENERATOR_API_KEY": "kUV7KQcsph8xYTiv2TkdG6RL8opXjZFCaKX8eWAv", "TA_PODS": 1, "STATS_PODS": 2, "MEDIA_PODS": 1, "SALE_PODS": 1, "AUDIT_PODS": 1, "ROLL-MAINTENANCE_PODS": 1, "REPORTING_PODS": 1, "HOME-VALUATION_PODS": 1, "MONARCH-WEB_PODS": 1, "CLASSIFICATION_PODS": 1, "SALE-ANALYSIS_PODS": 1, "EREPORTING_PODS": 1, "PROPERTY_PODS": 1, "USER-PROFILE_PODS": 1}