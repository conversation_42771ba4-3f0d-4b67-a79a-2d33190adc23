
/Users/<USER>/qv/api-objection/src/dal/esComparableSale.js
    5:10  error  'Client' is defined but never used  no-unused-vars
  633:7   error  Unexpected constant condition       no-constant-condition

/Users/<USER>/qv/api-objection/src/dal/objection.js
   843:15  error  'res' is assigned a value but never used                                   no-unused-vars
  1012:24  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins

/Users/<USER>/qv/api-objection/src/dal/objectorContact.js
  245:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  248:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  251:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  254:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  258:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  262:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  265:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  268:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  274:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  281:18  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins

/Users/<USER>/qv/api-objection/src/dal/otherParties.js
  2:8  error  'mssql' is defined but never used  no-unused-vars

/Users/<USER>/qv/api-objection/src/dal/pgConnectionPool.js
  16:25  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins

/Users/<USER>/qv/api-objection/src/dal/sqlConnectionPool.js
  16:25  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins

/Users/<USER>/qv/api-objection/src/handler/completeObjectionJobValuation.js
  21:12  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
  29:12  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins

/Users/<USER>/qv/api-objection/src/handler/exportObjection.js
  4:26  error  'STATUS_SUCCESS' is defined but never used  no-unused-vars

/Users/<USER>/qv/api-objection/src/services/actionObjectionJob.js
   2:3   error  'STATUS_INVALID' is defined but never used                    no-unused-vars
   8:10  error  'TLA_APPROVAL_FOR_RECOMMENDATIONS' is defined but never used  no-unused-vars
  32:10  error  'SEND' is defined but never used                              no-unused-vars

/Users/<USER>/qv/api-objection/src/services/reinstateObjectionJob.js
   9:3  error  'updateJobStatus' is defined but never used  no-unused-vars
  18:3  error  'STATUS_INVALID' is defined but never used   no-unused-vars

✖ 25 problems (25 errors, 0 warnings)

