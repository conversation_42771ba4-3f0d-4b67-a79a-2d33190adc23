import Logger from 'common/util/Logger.js';
import { parsePipelineStatus } from 'common/service/codepipeline.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { expect } from 'chai';

global.logger = new Logger();
describe('Pipeline Status Parsing Tests', () => {
  const __dirname = path.dirname(fileURLToPath(import.meta.url));
  it('should return false when doing redeployment and last stage keeps the status of the last failure', async () => {
    const context = fs.readFileSync(path.resolve(__dirname, './files/deploy-pipeline-4.json'), 'utf8');
    const response = JSON.parse(context);
    const result = parsePipelineStatus(response);
    expect(result).to.equal(false);
  });
  it('should return false when the last stage is in process', async () => {
    const context = fs.readFileSync(path.resolve(__dirname, './files/deploy-pipeline-1.json'), 'utf8');
    const response = JSON.parse(context);
    const result = parsePipelineStatus(response);
    expect(result).to.equal(false);
  });
  it('should return false when the last stage is not stated', async () => {
    const context = fs.readFileSync(path.resolve(__dirname, './files/deploy-pipeline-3.json'), 'utf8');
    const response = JSON.parse(context);
    const result = parsePipelineStatus(response);
    expect(result).to.equal(false);
  });
  it('should return true when all stages succeed', async () => {
    const context = fs.readFileSync(path.resolve(__dirname, './files/deploy-pipeline-2.json'), 'utf8');
    const response = JSON.parse(context);
    const result = parsePipelineStatus(response);
    expect(result).to.equal(true);
  });
});
