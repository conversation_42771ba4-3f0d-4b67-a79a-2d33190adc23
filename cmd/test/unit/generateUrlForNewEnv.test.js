import { expect } from 'chai';
import { generateUrlForNewEnv } from '../../src/dal/qvCustomAccess.js';

describe('generate url for new env', () => {
  it('from original DDE URLs', () => {
    const recordset = [
      { url: 'uatbdc.quotable.co.nz' },
      { url: 'uathorizons.acuity.org.nz' },
    ];
    const newEnv = 'dae';

    const result = generateUrlForNewEnv(recordset, newEnv);
    const expected = [
      { newUrl: 'bdc.dae.qivs.external.quotablevalue.co.nz' },
      { newUrl: 'horizons.dae.acuity.external.quotablevalue.co.nz' },
    ];
    for (let i = 0; i < result.length; i++) {
      expect(result[i].newUrl).equal(expected[i].newUrl);
    }
  });

  it('from updated DAE URLs', () => {
    const recordset = [
      { url: 'bdc.qve8.qivs.external.quotablevalue.co.nz' },
      { url: 'horizons.qve8.acuity.external.quotablevalue.co.nz' },
    ];
    const newEnv = 'dae2';

    const result = generateUrlForNewEnv(recordset, newEnv);

    const expected = [
      { newUrl: 'bdc.dae2.qivs.external.quotablevalue.co.nz' },
      { newUrl: 'horizons.dae2.acuity.external.quotablevalue.co.nz' },
    ];
    for (let i = 0; i < result.length; i++) {
      expect(result[i].newUrl).equal(expected[i].newUrl);
    }
  });
});
