import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import {
  createAuth0Config,
  deleteAuth0Config,
  getAuth0Token,
  getPropertyServiceClientId,
} from '../../src/util/auth0Util.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('create and delete auth0', () => {
  it('should work', async () => {
    const appEnv = 'mytest';
    const accountName = 'test';
    const appClientId = await getPropertyServiceClientId(accountName);
    const tokenData = await getAuth0Token(accountName);
    await createAuth0Config(tokenData.access_token, appClientId, appEnv, accountName);
    await deleteAuth0Config(tokenData.access_token, appClientId, appEnv, accountName);
  }).timeout(40000);
});
