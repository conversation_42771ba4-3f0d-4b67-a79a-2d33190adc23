import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { deleteMssqlLoginIfExist, dropUsersOnStaticDbs } from '../../src/dal/destroyMssqlObjects.js';
import { createMssqlLogins } from '../../src/dal/createMssqlObjects.js';
import { cloneDbScriptsRepo } from '../../src/service/qvnzRestore.js';
import { executeSqlScripts } from '../../src/util/sqlUtil.js';
import { createQivsUserOnDynamicDb } from '../../src/service/mssqlDynamicDbRestore.js';
import staticDbRestore from '../../src/service/mssqlStaticDbRestore.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('create and delete MSSQL logins', () => {
  it('should create and delete MSSQL logins', async () => {
    const jsonConfig = await createMssqlLogins('local_test');
    console.log(jsonConfig);
    await deleteMssqlLoginIfExist('local_test');
  }).timeout(20000);

  it('should create user on static databases', async () => {
    const dataEnv = 'nov24dev';
    await staticDbRestore(dataEnv);
  }).timeout(20000);

  it('should delete user on static databases', async () => {
    await dropUsersOnStaticDbs('nov24dev');
  }).timeout(20000);

  it('should create qivs users', async () => {
    await createQivsUserOnDynamicDb('test4');
  }).timeout(20000);

  it('should run the scripts', async () => {
    const dataEnvironment = {
      Branch: { S: 'master' },
    };
    await cloneDbScriptsRepo(dataEnvironment);
    await executeSqlScripts('leondde6', 'qvnz');
  }).timeout(2000000);
});
