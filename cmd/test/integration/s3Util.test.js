import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import {
  ARTIFACT_FILE_NAME, DEPLOY_BUCKET_NAME, MONARCH_K8S_BUCKET, TMP_FOLDER,
} from 'common/util/const.js';
import { deleteObjectIfManaged, downloadFile, uploadText } from 'common/util/s3util.js';
import { expect } from 'chai';
import { cicdS3Client, devS3Client } from '../../src/util/awsConfig.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('s3 util', () => {
  it('should create object in S3', async () => {
    const result = await uploadText(devS3Client, MONARCH_K8S_BUCKET, 'leon/kubeconfig.yml', 'hello');
    console.log(result);
    const result2 = await deleteObjectIfManaged(devS3Client, MONARCH_K8S_BUCKET, 'leon/kubeconfig.yml');
    console.log(result2);
  });

  it('should skip deleting non exist object', async () => {
    const result = await deleteObjectIfManaged(devS3Client, MONARCH_K8S_BUCKET, 'leon/nono.yml');
    expect(result.message).to.be.equal('Object does not exist or is not accessible.');
  });

  it('download file', async () => {
    const bucketKey = 'api-search/build/feature_DEV3-522-generic-config/deploy.zip';
    const appName = 'appName';
    const fileName = `${TMP_FOLDER}/${appName}/${ARTIFACT_FILE_NAME}`;
    const result = await downloadFile(cicdS3Client, DEPLOY_BUCKET_NAME, bucketKey, fileName);
    console.log(result);
  });
});
