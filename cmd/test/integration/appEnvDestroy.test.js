import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import appEnvDestroy from '../../src/handler/appEnvDestroy.js';

global.logger = new Logger();
dotenv.config({
  debug: true,
});

describe('deleter app environment resources', () => {
  const appEnv = 'webt2';
  it('should delete all resources', async () => {
    const options = {
      appEnv,
    };
    await appEnvDestroy(options);
  }).timeout(400000);
});
