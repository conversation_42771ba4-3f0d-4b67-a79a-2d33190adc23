import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { expect } from 'chai';
import { getInstanceIdByTag } from '../../src/util/ec2Util.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('ec2 util', () => {
  it('getInstanceIdByTag', async () => {
    const instanceId = await getInstanceIdByTag('Name', 'Monarch-Dev Kubernetes');
    const id = 'i-01dc2d91138202e44';
    expect(instanceId).to.equal(id);
  });
});
