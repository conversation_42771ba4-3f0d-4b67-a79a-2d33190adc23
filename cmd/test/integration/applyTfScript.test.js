import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { getDaeApplicationsByEnv } from 'common/dal/applications.js';
import { groupApplicationByPlatform } from 'common/dal/appStacks.js';
import { getAppEnvironmentByName } from 'common/dal/appEnvironments.js';
import { applyTfScripts } from '../../src/service/applyTfScripts.js';

global.logger = new Logger();
dotenv.config({
  debug: true,
});

it('applyTfScript.test.js', async () => {
  const appEnv = 'dp4';
  const queryResults = await getAppEnvironmentByName(appEnv);
  const appEnvironment = queryResults[0];
  const existingApplications = await getDaeApplicationsByEnv(appEnv);
  const {
    lambdaApplications,
    ecsApplications,
    k8sApplications,
  } = await groupApplicationByPlatform(existingApplications);
  await applyTfScripts(appEnvironment, lambdaApplications.map((app) => app.Name.S), k8sApplications.map((app) => app.Name.S), ecsApplications.map((app) => app.Name.S));
}).timeout(40000);
