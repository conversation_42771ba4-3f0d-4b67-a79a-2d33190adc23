import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { runRemoteLinuxCmdOnQivs } from 'common/util/cmdUtil.js';
import { configPermissions } from '../../src/service/appEnvCreate.js';

global.logger = new Logger();
dotenv.config({
  debug: true,
});

describe('qvis command test', () => {
  const qivsIp = '************';
  it('should list files', async () => {
    await runRemoteLinuxCmdOnQivs(qivsIp, 'ls');
  }).timeout(30000);

  it('should run powershell cmd', async () => {
    const result = await configPermissions(qivsIp);
  }).timeout(30000);
});
