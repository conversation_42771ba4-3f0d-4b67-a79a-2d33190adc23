import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { PG_USER } from 'common/util/const.js';
import { createPgUser, dropPgUserIfExists } from '../../src/dal/createPgObjects.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('create and delete PG logins', () => {
  it('should create and delete PG logins', async () => {
    const dataEnv = 'local_test';
    const username = `${dataEnv}_${PG_USER}`;
    const password = 'password';

    await createPgUser(username, password);
    await dropPgUserIfExists(dataEnv);
  }).timeout(20000);
});
