import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { checkJobStatus } from '../../src/util/batchUtil.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('batch util', () => {
  it('batch', async () => {
    const jobIds = [
      '84cc799e-a3a7-4518-a845-fac8f6f57772',
      'bd79a49b-7a46-4810-8958-3c207a9617f3',
    ];
    const test = await checkJobStatus(jobIds);
    console.log(test);
  }).timeout(20000);
});
