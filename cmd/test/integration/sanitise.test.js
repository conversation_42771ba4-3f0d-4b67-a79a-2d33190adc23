import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { sanitiseDb } from '../../src/dal/sanitise.js';
import { getAppUsers } from '../../src/util/qvUserUtil.js';

global.logger = new Logger();
dotenv.config({ debug: true });

describe('setup of user accounts and permissions for Developers and Testers', async () => {
  const users = await getAppUsers();
  const dataEnv = 'feb25dev';
  it('Monarch', async () => {
    await sanitiseDb(dataEnv, 'property_service', users);
  }).timeout(20000);

  it('QIVS', async () => {
    await sanitiseDb(dataEnv, 'qvnz', users);
  }).timeout(10000);

  it('Public Website', async () => {
    await sanitiseDb(dataEnv, 'qvconz', users);
  }).timeout(20000);
});
