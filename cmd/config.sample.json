{"MSSQL_SERVER": "10.152.10.62", "MSSQL_USERNAME": "cicd_sql_user", "MSSQL_PASSWORD": "PWD", "PG_SERVER": "10.152.10.62", "PG_USERNAME": "cicd_pg_user", "PG_PASSWORD": "PWD", "DEV_AWS_ACCESS_KEY_ID": "AKIAVNXUHZWWX2K3XM76", "DEV_AWS_SECRET_ACCESS_KEY": "KEY", "SSH_USERNAME": "cicd_user", "SSH_SERVER": "10.152.10.62", "SSH_PASSWORD": "6EYhaTgERRw-3vsw", "GITHUB_TOKEN": "****************************************", "TEST_PROPERTY_SERVICE_CLIENT_ID": "ySSYy5zwI53NXpsIZYbp2Teprr6HqoGi", "TEST_PROPERTY_SERVICE_CLIENT_SECRET": "", "TEST_AUTH_API_CLIENT_ID": "9BwTjpJGysCQ23j9hJOpS2IDMj3GSM90", "TEST_AUTH_API_CLIENT_SECRET": "KEY", "TEST_AUTH_API_AUDIENCE": "https://qvmonarch-systest.au.auth0.com/api/v2/", "TEST_AUTH_API_BASE_URL": "https://qvmonarch-systest.au.auth0.com", "PROD_PROPERTY_SERVICE_CLIENT_ID": "waWLuxADVQVBbooDZ67EXWpddCj5XNmM", "PROD_AUTH_API_CLIENT_ID": "jNvygNFkLPfIeGSn0yRbedaRrPHE62au", "PROD_AUTH_API_CLIENT_SECRET": "KEY", "PROD_AUTH_API_AUDIENCE": "https://qvmonarch-dev.au.auth0.com/api/v2/", "PROD_AUTH_API_BASE_URL": "https://qvmonarch-dev.au.auth0.com", "CHARGIFY_API_KEY": "", "CHARGIFY_SHARED_KEY": "", "DJANGO_SECRET_KEY": "", "RECAPTCHA_PRIVATE_KEY": "", "SENDGRID_API_KEY": "", "WINDCAVE_API_KEY": "", "MAILCHIMP_API_KEY": "", "CHARGIFYJS_PRIVATE_KEY": "", "ECS_TASK_ENABLED": "false", "SAMBA_PASSWORD": "f@tm@n", "APP_ENV_TESTS": {"HEALTH_CHECK": "true"}, "QIVS_AMI_ID": "ami-0316a05a6f447312e", "QV_DEV": {"HOST": "qv.dev", "USERNAME": "QVNZ\\DAEDomainJoin", "PASSWORD": "PWD"}, "CICD_QIVS": {"USERNAME": "<PERSON><PERSON><PERSON>", "PASSWORD": "PWD"}, "GNS_NET_PASSWORD": "PWD", "SLACK": {"URL": "*******************************************************************************", "CHANNEL": "#cicd-data-env"}, "EXTERNAL_USER_PASSWORD": "", "INTERNAL_USER_PASSWORD": "", "INTERNAL_USER_CUSTOMER_CARE_PASSWORD": "", "INTERNAL_USER_STANDARD_PASSWORD": "", "STAGE": "local"}