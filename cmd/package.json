{"name": "dynamic-environments-cmd", "version": "0.0.1", "private": true, "main": "index.js", "type": "module", "config": {"appEnv": "reinz1", "dataEnv": "reinz", "datastore": "qvnz_mapping"}, "scripts": {"test": "ENV=local mocha test/unit --reporter mochawesome", "integration": "ENV=local mocha test/integration --reporter mochawesome", "qvnz_restore": "node src/index.js --name qvnz-restore --dataEnv $npm_package_config_dataEnv  --datastore $npm_package_config_datastore", "pg_restore": "node src/index.js --name pg-restore --dataEnv $npm_package_config_dataEnv --datastore $npm_package_config_datastore", "es_restore": "node src/index.js --name es-restore --dataEnv $npm_package_config_dataEnv", "data_env_destroy": "node src/index.js --name data-env-destroy --dataEnv $npm_package_config_dataEnv", "app_env_destroy": "node src/index.js --name app-env-destroy --appEnv $npm_package_config_appEnv", "qvnz_static": "node src/index.js --name qvnz-static --drive D", "app_env_create": "ENV=local node src/index.js --name app-env-create --appEnv $npm_package_config_appEnv", "app_env_update": "ENV=local node src/index.js --name app-env-update --appEnv $npm_package_config_appEnv", "data_env_create": "node src/index.js --name data-env-create --dataEnv $npm_package_config_dataEnv", "app_env_reindex": "ENV=local node src/index.js --name app-env-reindex --appEnv $npm_package_config_appEnv --payload '{\"indexName\":\"mar25dev-home-valuation-1741557358894\",\"createNewIndex\":true}'", "app_env_reindex_all": "ENV=local node src/index.js --name app-env-reindex --appEnv $npm_package_config_appEnv", "app_env_verify": "ENV=local node src/index.js --name app-env-verify --payload '{\"branch\":\"master\",\"dde\":\"\"}'", "application_deploy": "ENV=local node src/index.js --name application-deploy --deploymentId 1739775239101 --payload '{\"type\":\"dde\",\"applicationName\":\"applicationName\",\"envName\":\"load5\"}' ", "application_deploy_all": "node src/index.js --name application-deploy --deploymentId 1740357225175 --reindex false --sleepTags false --payload '{\"type\":\"dae\",\"applicationName\":\"\",\"envName\":\"fp10\"}' ", "db_scripts": "ENV=local node src/index.js --name db-scripts --dataEnv $npm_package_config_dataEnv --datastore $npm_package_config_datastore", "test_single": "export ENV=\"local\"; mocha test/unit/applyTfScripts.test.js ", "test_single_integration": "export ENV=\"local\"; mocha test/integration/sanitise.test.js", "tf_scripts_count:ps": "$env:ENV=\"local\";  mocha test/unit/applyTfScripts.test.js", "tf_scripts_count:bash": "export ENV=\"local\"; mocha test/unit/applyTfScripts.test.js"}, "dependencies": {"@aws-sdk/client-batch": "^3.654.0", "adm-zip": "^0.5.10", "axios": "^1.4.0", "commander": "^10.0.0", "common": "file:../common", "dotenv": "^16.0.3", "ip": "^1.1.8", "mssql": "^10.0.2", "pg": "^8.9.0", "pg-pool": "^3.5.2", "uuid": "^11.0.5"}, "devDependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.758.0", "@aws-sdk/client-apigatewayv2": "^3.750.0", "@aws-sdk/client-api-gateway": "^3.624.0", "@aws-sdk/client-cloudformation": "^3.645.0", "@aws-sdk/client-codebuild": "^3.427.0", "@aws-sdk/client-codepipeline": "^3.427.0", "@aws-sdk/client-dynamodb": "^3.363.0", "@aws-sdk/client-ec2": "^3.629.0", "@aws-sdk/client-elastic-load-balancing-v2": "^3.624.0", "@aws-sdk/client-s3": "^3.363.0", "@aws-sdk/client-ssm": "^3.363.0", "@aws-sdk/util-dynamodb": "^3.326.0", "@aws-sdk/util-utf8-node": "^3.259.0", "chai": "^4.3.7", "chai-http": "^4.3.0", "chai-json-schema": "^1.5.1", "chai-string": "^1.5.0", "eslint": "^8.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.6", "mocha": "^10.2.0", "mochawesome": "^7.1.3", "mockery": "^2.1.0", "sinon": "^17.0.1"}}