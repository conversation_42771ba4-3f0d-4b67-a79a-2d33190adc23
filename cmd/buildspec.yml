version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_MESSAGE
    - COMMIT_SHA
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - cd common
      - npm install
      - cd ../cmd
      - npm install --include=dev
  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
  build:
    commands:
      - echo $ARTIFACT_TEMP
      - echo $BUILD_VERSION > $ARTIFACT_TEMP/version.txt
      - echo $COMMIT_SHA > $ARTIFACT_TEMP/sha.txt
      - echo $BUILD_BRANCH > $ARTIFACT_TEMP/branch.txt
      - cp config.sample.json config.json
      - npm run test

      - echo "uploading unit test report started"
      - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
      - . ./create_test_report.sh "$PROJECT_NAME" "build" "unit" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
      - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
      - aws s3 cp mochawesome-report/ s3://$REPORT_FOLDER --recursive || true
      - echo "uploading unit test report completed"

      - cd ../
      - rsync -av --exclude='node_modules' common $ARTIFACT_TEMP/
      - rsync -av --exclude='node_modules' cmd $ARTIFACT_TEMP/
      - cd $ARTIFACT_TEMP
      - zip -r $ARTIFACT_OUTPUT/deploy.zip .
      - METADATA=$(printf '{"codepipeline-artifact-revision-summary":"%s"}' "$BUILD_VERSION $BUILD_BRANCH")
      - aws s3 cp $ARTIFACT_OUTPUT/deploy.zip $BUILD_BASE_S3_URL/$CLEAN_BRANCH/deploy.zip --metadata="$METADATA"
  post_build:
    commands: |
      echo Build completed on `date`
cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'

#artifacts:
#  files:
#    - '**/*'
#  base-directory: /reports
