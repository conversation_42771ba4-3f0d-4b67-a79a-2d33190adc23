## cmd

### Setup

```bash
# switch to CICD account
aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account
cd cmd

# create .env
cp .env.sample .env
# create config.json

aws ssm get-parameter --name "/cicd/dynamic-environments-cmd" --output text --query 'Parameter.Value' > config.json

# get ssh key to access cicd managed ec2
aws ssm get-parameter --name "/cicd/cicd-managed-pem" --output text --query 'Parameter.Value' > cicd-managed.pem
chmod 400 cicd-managed.pem
```