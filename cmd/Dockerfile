FROM alpine:3.21.3
WORKDIR /app
ARG TERRAFORM_VERSION=1.9.5
# Install Git, OpenSSH client, PostgreSQL client, AWS client
RUN apk --no-cache add sshpass curl git openssh-client postgresql16-client  aws-cli nodejs  npm


# Install Terraform using the specified version
RUN apk --update add curl unzip \
    && curl -fsSL -o terraform.zip https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && unzip terraform.zip \
    && mv terraform /usr/local/bin/ \
    && rm terraform.zip
# Copy known_hosts to the container to prevent SSH connection issues
COPY cmd/known_hosts /root/.ssh/known_hosts

COPY common ./common
RUN cd common && npm install

COPY cmd ./cmd
COPY cmd/.env ./cmd/
COPY cicd-managed.pem ./cmd/cicd-managed.pem
RUN chmod 400 ./cmd/cicd-managed.pem
RUN cd cmd && npm install

CMD ["sh", "-c", "cd /app/cmd, node src/index.js --name placeholder, --env placeholder, --drive placeholder"]
