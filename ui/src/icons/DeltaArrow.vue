<script setup>
import {computed} from 'vue';

const props = defineProps({
  direction: String,
  up: <PERSON><PERSON>an,
  down: <PERSON><PERSON><PERSON>,
  left: <PERSON><PERSON>an,
  right: <PERSON><PERSON><PERSON>
});

const d = computed(() => {
  if (props.direction == 'down' || props.down) {
    return 'M0,1 l1,1 l1,-1 z';
  }
  if (props.direction == 'left' || props.left) {
    return 'M1,0 l-1,1 l1,1 z';
  }
  if (props.direction == 'right' || props.right) {
    return 'M1,0 l1,1 l-1,1 z';
  }

  return 'M0,1 l1,-1 l1,1 z';
});
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2">
    <path fill="currentColor" :d="d"/>
  </svg>
</template>
