FROM alpine:3.21.3
WORKDIR /app

RUN apk --no-cache add sshpass curl  postgresql16-client  openssh-client samba-client coreutils aws-cli nodejs  npm

RUN pg_dump --version && node --version && npm --version

# Copy known_hosts to the container to prevent SSH connection issues
COPY pgdump/known_hosts /root/.ssh/known_hosts

COPY common ./common
RUN cd common && npm install

COPY pgdump ./pgdump
COPY pgdump/.env ./pgdump/
RUN cd pgdump && npm install

WORKDIR /app/pgdump

CMD ["node", "src/index.js"]
