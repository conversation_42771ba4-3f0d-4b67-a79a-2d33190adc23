import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { expect } from 'chai';

import { PROD_RDS_ROLE } from 'common/util/const.js';
import { assumeRole, getProdSnapshotId } from '../../src/service/rds.js';

global.logger = new Logger();
dotenv.config();

describe('rds utl', () => {
  it('should create get ProdSnapshotId for monarch', async () => {
    const prodCredentials = await assumeRole(PROD_RDS_ROLE);
    const snapshotIdentifier = await getProdSnapshotId(prodCredentials, 'monarch');
    expect(snapshotIdentifier).to.equal('monarch-prod-snapshot-cicd-manual');
  }).timeout(100000);
});
