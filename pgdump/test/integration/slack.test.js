import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import { sendSlack } from '../../src/service/slack.js';

global.logger = new Logger();
dotenv.config();

describe('slack test', () => {
  it('should notify a channel', async () => {
    await sendSlack('test error, don\'t worry, checkout https://ap-southeast-2.console.aws.amazon.com/cloudwatch/home?region=ap-southeast-2#logsV2:log-groups/log-group/$252Faws$252Fecs$252Ftask$252Fpg-dump', ':x:', false);
  }).timeout(100000);
});
