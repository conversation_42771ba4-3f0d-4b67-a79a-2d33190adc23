import Logger from 'common/util/Logger.js';
import dotenv from 'dotenv';
import {
  createDevRds, waitRdsDeleted,
} from '../../src/service/rds.js';

global.logger = new Logger();
dotenv.config();

describe('rds utl', () => {
  it('should create an instance', async () => {
    const dbName = 'qvconz';
    const dbhost = await createDevRds(dbName);
    console.log(dbhost);
  }).timeout(1000000);

  it('should delete an instance', async () => {
    const dbName = 'qvconz';
    await waitRdsDeleted(dbName);
  }).timeout(1000000);
});
