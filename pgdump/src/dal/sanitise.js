import { PROPERTY_SERVICE_DB_NAME, QVCONZ_DB_NAME } from 'common/util/const.js';
import { runQuery } from './pgDumpStatus.js';
import { sendSlack } from '../service/slack.js';
/**
 *  due to foreign key constraints, we need to delete table contents in a specific order
 */
export const CLEAR_TABLES_QVCONZ = [
  'account_accountverificationtoken',
  'account_usersession',
  'audit_logging_auditlog',
  'audit_logging_discountcode',
  'audit_logging_discountcodeuse',
  'audit_logging_invoice',
  'audit_logging_order',
  'audit_logging_windcavedetails',
  'costbuilder_costbuildersubscriptioninvite',
  'darroch_darrochcontactussubmission',
  'darroch_darrochpropertymanagementsubmission',
  'darroch_darrochpropertyvaluationsubmission',
  'django_admin_log',
  'django_celery_beat_clockedschedule',
  'django_celery_beat_crontabschedule',
  'django_celery_beat_intervalschedule',
  'django_celery_beat_periodictask',
  'django_celery_beat_periodictasks',
  'django_celery_beat_solarschedule',
  'django_celery_results_chordcounter',
  'django_celery_results_groupresult',
  'django_celery_results_taskresult',
  'django_session',
  'property_info_addresssearchlog',
  'property_info_addresssearchmetrics',
  'property_info_propertydetailsfile',
  'property_info_propertydetails',
  'qvau_qvaucontactformsubmission',
  'rv_objections_ratingvalueobjectionfile',
  'rv_objections_ratingvalueobjectionrecipient',
  'rv_objections_ratingvalueobjection',
  'services_requestvaluationfile',
  'services_urgentratingrequestfile',
  'services_urgentratingrequest',
  'services_urgentratingrequestrecipientorderable',
  'services_requestvaluation',
  'valuations_valuation',
  'wagtailcore_comment',
  'wagtailcore_commentreply',
  'wagtailcore_taskstate',
  'wagtailcore_workflowstate',
  'wagtailforms_formsubmission',
  'wagtailcore_pageviewrestriction',
  'wagtailcore_pageviewrestriction_groups',
  'wagtailusers_userprofile',
  'costbuilder_costbuildersubscription_before_ops_1089',
  'house_price_index_priceindex_20210305',
  'house_price_index_priceindex_ops_703',
  'rating_authority_revision_date_20210305',
];
export const CLEAR_TABLES_PROPERTY_SERVICE = [
  'audit.audit_log',
];
const sanitiseFunctions = {
  [QVCONZ_DB_NAME]: sanitiseQvconz,
  [PROPERTY_SERVICE_DB_NAME]: sanitisePropertyService,
};

export async function sanitiseDb(dbName, server) {
  logger.info('sanitiseDb', { dbName, server });
  const sanitiseFunction = sanitiseFunctions[dbName];
  if (sanitiseFunction) {
    try {
      await sanitiseFunction(dbName, server);
    } catch (e) {
      await sendSlack(`sanitise db ${dbName} error, log group https://ap-southeast-2.console.aws.amazon.com/cloudwatch/home?region=ap-southeast-2#logsV2:log-groups/log-group/$252Faws$252Fecs$252Ftask$252Fpg-dump`, ':x:', true);
    }
  } else {
    logger.warn('sanitiseDb', { dbName, server, message: 'No sanitise function found' });
  }
}

async function sanitiseQvconz(dbName, server) {
  logger.info('sanitise qvconz start', { dbName, server });
  for (const table of CLEAR_TABLES_QVCONZ) {
    const query = `
            DELETE
            FROM website.${table}
        `;
    await runQuery(query, server, dbName);
  }
  logger.info('sanitise qvconz done');
}

async function sanitisePropertyService(dbName, server) {
  logger.info('sanitisePropertyService', { dbName, server });
  for (const table of CLEAR_TABLES_PROPERTY_SERVICE) {
    const query = `
            DELETE
            FROM ${table}
        `;
    await runQuery(query, server, dbName);
  }
  logger.info('sanitise property service done');
}
