import pkg from 'pg';
import { ERROR_CODES } from 'common/util/const.js';
import getConfig from 'common/config/getConfig.js';

export async function getDumpStatus(server) {
  const cmd = `SELECT datname,
                      pid,
                      usename,
                      application_name,
                      client_addr,
                      client_hostname,
                      client_port,
                      query_start
               FROM pg_stat_activity
               WHERE usename = 'dba'
                 and application_name = 'pg_dump';`;
  const results = await runQuery(cmd, server);
  return results.rows;
}

export async function runQuery(sqlQuery, server, dbName = 'postgres') {
  const { Client } = pkg;
  const { USERNAME: username, PASSWORD: password } = await getConfig('RDS');
  const url = `postgresql://${username}:${encodeURIComponent(password)}@${server}/${dbName}`;
  const client = new Client({
    connectionString: url,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    logger.info(`Running query: ${sqlQuery}`);
    await client.connect();
    logger.info('Connected to postgresql');
    return await client.query(sqlQuery);
  } catch (error) {
    logger.error(ERROR_CODES.PG_DB_ERROR.code, ERROR_CODES.PG_DB_ERROR.message, error);
    throw error;
  } finally {
    await client.end();
  }
}
