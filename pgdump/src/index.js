import Logger from 'common/util/Logger.js';
import { runRemoteLinuxCmdSync } from 'common/util/cmdUtil.js';
import {
  DB_SNAPSHOTS, PG_BACKUP_FOLDER, PG_BACKUP_PATH, REFRESH_INTERVAL_IN_MILLISECONDS,
} from 'common/util/const.js';
import { sleep } from 'common/util/sleepUtil.js';
import getConfig from 'common/config/getConfig.js';
import dotenv from 'dotenv';
import { sendSlack } from './service/slack.js';
import pgdump from './service/pgdump.js';
import { sendLocalFiles } from './service/samba.js';
import { getDumpStatus } from './dal/pgDumpStatus.js';
import { createDevRds, deleteRds } from './service/rds.js';
import { sanitiseDb } from './dal/sanitise.js';

global.logger = new Logger();
dotenv.config();
const databases = DB_SNAPSHOTS.keys();
for (const dbName of databases) {
  try {
    const snapshotName = DB_SNAPSHOTS.get(dbName);
    await sendSlack(`Postgres dump ${dbName} started`, ':floppy_disk:');
    const dbhost = await createDevRds(snapshotName);
    await sanitiseDb(dbName, dbhost);
    await dumpFromRds(dbName, dbhost);
    await deleteRds(snapshotName);
    await sendSlack(`Postgres dump ${dbName} finished.`, ':floppy_disk:');
  } catch (e) {
    await sendSlack(`Postgres dump ${dbName} error, log group https://ap-southeast-2.console.aws.amazon.com/cloudwatch/home?region=ap-southeast-2#logsV2:log-groups/log-group/$252Faws$252Fecs$252Ftask$252Fpg-dump`, ':x:', true);
    logger.error('ERR-CICD-PGDUMP-100', 'Postgres dump error', e);
  }
}

export async function dumpFromRds(dbName, dbhost) {
  await runRemoteLinuxCmdSync(`rm -rf ${PG_BACKUP_PATH}/${dbName}/*`);
  const { USERNAME: dbusername, PASSWORD: dbpassword } = await getConfig('RDS');
  await pgdump(dbhost, dbusername, dbpassword, dbName);
  await sleep(REFRESH_INTERVAL_IN_MILLISECONDS);
  const smbFolder = `${PG_BACKUP_FOLDER}/${dbName}`;

  while (true) {
    const result = await getDumpStatus(dbhost);
    logger.info('dump status', { result });
    if (result.length === 0) {
      break;
    }
    await sendLocalFiles(smbFolder, true);
    await sleep(REFRESH_INTERVAL_IN_MILLISECONDS);
  }
  await sendLocalFiles(smbFolder, false);
}
