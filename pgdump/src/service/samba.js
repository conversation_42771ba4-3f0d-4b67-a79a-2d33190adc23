import SambaClient from 'samba-client';
import { listFilesByModifiedTime } from 'common/util/fileUtil.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import getConfig from 'common/config/getConfig.js';
import { sleep } from 'common/util/sleepUtil.js';

export async function getSambaClient() {
  const {
    USERNAME: username, PASSWORD: password, ADDRESS: address, DOMAIN: domain,
  } = await getConfig('SAMBA');
  return new SambaClient({
    address,
    username,
    password,
    domain,
  });
}

const sambaClient = await getSambaClient();

export async function sendLocalFiles(smbfolder, isGenerating) {
  logger.info('Sending files scanning started');
  const dumpFiles = await listFilesByModifiedTime('./');
  logger.info(`Found ${dumpFiles.length} files`);
  if (isGenerating) {
    dumpFiles.pop();
  }
  for (const dumpFile of dumpFiles) {
    logger.info(`Sending ${dumpFile}`);
    await sendLocalFileWithRetry(smbfolder, dumpFile);
    // delete the local file
    const result = runCmdSync(`rm ${dumpFile}`);
    logger.info(result);
  }
}

async function sendLocalFileWithRetry(smbfolder, localPath, maxRetries = 5, retryInterval = 60000) {
  let attempt = 0;
  const smbPath = `${smbfolder}/${localPath}`;

  while (attempt < maxRetries) {
    try {
      attempt++;
      logger.info(`Attempt ${attempt}: Sending file to ${smbPath}...`);
      const result = await sambaClient.sendFile(localPath, smbPath);
      logger.info('File sent successfully');
      return result;
    } catch (error) {
      logger.error(`Attempt ${attempt} failed: ${error}`);
      if (attempt < maxRetries) {
        logger.info(`Waiting ${retryInterval} ms before retrying...`);
        await sleep(retryInterval);
      } else {
        logger.info(`Max retries (${maxRetries}) reached. Exiting without success.`);
        logger.error(error);
        throw error;
      }
    }
  }
}
