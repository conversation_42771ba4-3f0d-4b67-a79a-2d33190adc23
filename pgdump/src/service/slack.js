import dotenv from 'dotenv';
import getConfig from 'common/config/getConfig.js';

dotenv.config();

export async function sendSlack(text, iconEmoji, notify = false) {
  const { URL: url, CHANNEL: channel } = await getConfig('SLACK');
  logger.info(`Slack channel:${url}`);
  const username = 'Postgres Backup';
  const notificationText = notify ? `<!channel> ${text}` : text;

  const payload = {
    channel,
    username,
    text: notificationText,
    icon_emoji: iconEmoji,
  };
  logger.info('Sending Slack message', { payload });
  try {
    const resp = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    if (!resp.ok) {
      logger.error(`Failed to send Slack message ${resp.status} ${resp.statusText}`);
    }
  } catch (error) {
    logger.error('', 'Error sending Slack message:', error);
  }
}
