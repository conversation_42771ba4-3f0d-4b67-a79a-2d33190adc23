import { runCmd } from 'common/util/cmdUtil.js';

export default async function pgdump(dbhost, dbusername, dbpassword, dbName) {
  logger.info('pgdump started');

  const dumpCmd = `PGPASSWORD=${dbpassword} pg_dump --encoding=UTF8 --no-owner --no-comments --no-publications -h ${dbhost} -p 5432 -b -Fc -v -U ${dbusername} -d ${dbName}`;
  const splitCmd = `split -b 512m --filter="gzip > ./${dbName}-\\$FILE.gz"`;
  const cmd = ` ${dumpCmd} | ${splitCmd}`;
  const dumpResult = runCmd(cmd);
  logger.info(dumpResult);
  logger.info('pgdump finished');
}
