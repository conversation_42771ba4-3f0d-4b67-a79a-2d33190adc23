import getConfig from 'common/config/getConfig.js';
import { runCmdSync } from 'common/util/cmdUtil.js';
import { DEV_ACCOUNT, PROD_RDS_ROLE } from 'common/util/const.js';
import { sleep } from 'common/util/sleepUtil.js';
import { RdsStatus } from 'common/enums/rdsStatus.js';
import { getDevCredentials } from '../util/awsconfig.js';

export async function createDevRds(dbName) {
  logger.info(`Creating temp RDS for ${dbName}`);
  await createTmpRds(dbName);
  const { PASSWORD: password } = await getConfig('RDS');
  await updatePwd(dbName, password);
  const dbhost = await getRdsUrl(dbName);

  logger.info(`dbhost ${dbhost} created for ${dbName}`);
  return dbhost;
}

export async function waitRdsAvailable(dbname) {
  const available = 'available';
  const checkRdsCmd = `${await getDevCredentials()} aws rds describe-db-instances --db-instance-identifier ${getInstanceId(dbname)} --query "DBInstances[0].DBInstanceStatus" --output text`;
  let result = '';
  while (result !== available) {
    result = (await runCmdSync(checkRdsCmd)).replace(/\n/g, '');
    await sleep(60000);
  }
}

export async function waitRdsDeleted(dbname) {
  const checkRdsCmd = `${await getDevCredentials()} aws rds describe-db-instances --db-instance-identifier ${getInstanceId(dbname)} --query "DBInstances[0].DBInstanceStatus" --output text`;
  let result = RdsStatus.DELETING;
  while (result === RdsStatus.DELETING) {
    try {
      result = runCmdSync(checkRdsCmd).replace(/\n/g, '');
    } catch (e) {
      if (e.message.includes('DBInstanceNotFound')) {
        logger.info('RDS deleted');
        return;
      }
      throw e;
    }
    await sleep(60000);
  }
}

async function restoreRds(dbname, snapshotIdentifier) {
  logger.info(`Restoring RDS for ${snapshotIdentifier}`);
  const restoreCmd = `
       ${await getDevCredentials()} aws rds restore-db-instance-from-db-snapshot  \\
    --db-instance-identifier ${getInstanceId(dbname)} \\
    --db-snapshot-identifier arn:aws:rds:ap-southeast-2:************:snapshot:${snapshotIdentifier} \\
    --db-subnet-group-name devvpc-privatedata \\
    --vpc-security-group-ids sg-07308ec2bf4bc5bac sg-0999116e
    `;
  await runCmdSync(restoreCmd);
}

export async function shareProdRds(prodCredentials, snapshotIdentifier) {
  const modifyDbCmd = `${prodCredentials} aws rds modify-db-snapshot-attribute --db-snapshot-identifier ${snapshotIdentifier} --attribute-name restore --values-to-add ${DEV_ACCOUNT}`;
  await runCmdSync(modifyDbCmd);
}

export async function waitUntilSnapshotAvailable(snapshotIdentifier) {
  let status = await getSnapshotStatus(snapshotIdentifier);
  while (status !== 'available') {
    await sleep(60000);
    status = await getSnapshotStatus(snapshotIdentifier);
  }
}

export async function getSnapshotStatus(snapshotIdentifier) {
  const statusCmd = `
   ${await getDevCredentials()} aws rds describe-db-snapshots \\
    --db-snapshot-identifier ${snapshotIdentifier} --query 'DBSnapshots[0].Status' --output text`;
  return runCmdSync(statusCmd).replace(/\n/g, '');
}
export async function copyProdRds(snapshotIdentifier, targetDbSnapshot) {
  const copyCmd = `
   ${await getDevCredentials()} aws rds copy-db-snapshot \\
    --source-db-snapshot-identifier arn:aws:rds:ap-southeast-2:************:snapshot:${snapshotIdentifier} \\
    --target-db-snapshot-identifier ${targetDbSnapshot} \\
    --kms-key-id arn:aws:kms:ap-southeast-2:************:key/7d3d2119-9d2b-4aee-9a91-e28f35111889
  `;
  await runCmdSync(copyCmd);
}
export async function deleteSnapshot(snapshotIdentifier) {
  const copyCmd = `
   ${await getDevCredentials()} aws rds delete-db-snapshot \
    --db-snapshot-identifier ${snapshotIdentifier}
  `;
  await runCmdSync(copyCmd);
}

export async function getProdSnapshotId(prodCredentials, dbname) {
  const manualSnapshotQuery = `\`${dbname}-prod-snapshot-cicd-manual\``;
  let result = await queryProdSnapshot(prodCredentials, manualSnapshotQuery);
  if (result === 'null' || !isWithinSixDays(JSON.parse(result).SnapshotCreateTime)) {
    logger.info('No manual snapshot found. Getting the latest snapshot.');
    const queryForAll = `\`${dbname}\``;
    result = await queryProdSnapshot(prodCredentials, queryForAll);
    if (result === 'null') {
      logger.error('No snapshot found');
      return null;
    }
  }
  return JSON.parse(result).DBSnapshotIdentifier;
}

async function queryProdSnapshot(prodCredentials, query) {
  const latestSnapshotCmd = `${prodCredentials} aws rds describe-db-snapshots --query 'DBSnapshots[?starts_with(DBSnapshotIdentifier, ${query})] | sort_by(@, &SnapshotCreateTime)[-1]'`;
  return runCmdSync(latestSnapshotCmd).replace(/\n/g, '');
}

export async function createTmpRds(dbname) {
  const prodCredentials = await assumeRole(PROD_RDS_ROLE);
  const snapshotIdentifier = await getProdSnapshotId(prodCredentials, dbname);
  const targetDbSnapshot = `dde-${dbname}-prod-snapshot`;
  await shareProdRds(prodCredentials, snapshotIdentifier);
  await copyProdRds(snapshotIdentifier, targetDbSnapshot);
  await waitUntilSnapshotAvailable(targetDbSnapshot);
  await restoreRds(dbname, targetDbSnapshot);
  await waitRdsAvailable(dbname);
  await deleteSnapshot(targetDbSnapshot);
}

export async function updatePwd(dbName, password) {
  logger.info(`Updating password for ${dbName}`);
  const updatePwdCmd = `${await getDevCredentials()} aws rds modify-db-instance --db-instance-identifier ${getInstanceId(dbName)}  --master-user-password '${password}' --apply-immediately`;
  await runCmdSync(updatePwdCmd);
  await waitRdsAvailable(dbName);
  // wait for the new password to be applied.
  await sleep(60000);
}

export async function deleteRds(dbName) {
  const cmd = ` ${await getDevCredentials()} aws rds delete-db-instance --db-instance-identifier  ${getInstanceId(dbName)} --skip-final-snapshot`;
  await runCmdSync(cmd);
  await waitRdsDeleted(dbName);
}

export async function getRdsUrl(dbName) {
  const cmd = `${await getDevCredentials()} aws rds describe-db-instances --db-instance-identifier ${getInstanceId(dbName)} --query 'DBInstances[0].[Endpoint.Address]' --output text`;
  return (runCmdSync(cmd)).replace(/\n/g, '');
}

export async function assumeRole(roleARN) {
  const roleSessionName = 'RDSAccessTest';
  const DEV_AWS_ACCESS_KEY_ID = await getConfig('DEV_AWS_ACCESS_KEY_ID');
  const DEV_AWS_SECRET_ACCESS_KEY = await getConfig('DEV_AWS_SECRET_ACCESS_KEY');
  const DEV_AWS_CREDENTIALS = `AWS_ACCESS_KEY_ID=${DEV_AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${DEV_AWS_SECRET_ACCESS_KEY}`;
  const assume = `${DEV_AWS_CREDENTIALS} aws sts assume-role --role-arn ${roleARN} --role-session-name ${roleSessionName}`;
  const result = JSON.parse(runCmdSync(assume));
  const awsAccessKeyId = result.Credentials.AccessKeyId;
  const awsSecretAccessKey = result.Credentials.SecretAccessKey;
  const awsSessionToken = result.Credentials.SessionToken;
  return `AWS_ACCESS_KEY_ID=${awsAccessKeyId} AWS_SECRET_ACCESS_KEY=${awsSecretAccessKey} AWS_SESSION_TOKEN=${awsSessionToken}`;
}

export function getInstanceId(dbname) {
  return `dde${dbname}`;
}

function isWithinSixDays(timestamp) {
  const currentDate = new Date();
  const targetDate = new Date(timestamp);

  const differenceInTime = currentDate.getTime() - targetDate.getTime();
  const differenceInDays = differenceInTime / (1000 * 3600 * 24);

  return differenceInDays <= 6;
}
