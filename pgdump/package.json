{"name": "dynamic-environments-pgdump", "version": "0.0.1", "private": true, "main": "index.js", "type": "module", "scripts": {"start": "export ENV=local && node src/index.js", "test": "ENV=local mocha test/unit", "integration": "ENV=local mocha test/integration --reporter mochawesome", "test-single": "export ENV=local && mocha test/integration/getProdSnapshotId.test.js"}, "dependencies": {"common": "file:../common", "dotenv": "^16.0.3", "samba-client": "^7.2.0", "pg": "^8.13.1"}, "devDependencies": {"eslint": "^8.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.6", "mocha": "^10.2.0", "mochawesome": "^7.1.3", "chai": "^4.3.7"}}