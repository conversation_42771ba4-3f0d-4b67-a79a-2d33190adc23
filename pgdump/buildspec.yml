version: 0.2

env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_MESSAGE
    - COMMIT_SHA
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands: |
      cd pgdump
      npm install --include=dev
  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
  build:
    commands:
      - echo $ARTIFACT_TEMP
      - echo $BUILD_VERSION > $ARTIFACT_TEMP/version.txt
      - echo $COMMIT_SHA > $ARTIFACT_TEMP/sha.txt
      - echo $BUILD_BRANCH > $ARTIFACT_TEMP/branch.txt
      - cd ../
      - rsync -av --exclude='node_modules' common $ARTIFACT_TEMP/
      - rsync -av --exclude='node_modules' pgdump $ARTIFACT_TEMP/
      - cd $ARTIFACT_TEMP
      - zip -r $ARTIFACT_OUTPUT/deploy.zip .
      - METADATA=$(printf '{"codepipeline-artifact-revision-summary":"%s"}' "$BUILD_VERSION $BUILD_BRANCH")
      - aws s3 cp $ARTIFACT_OUTPUT/deploy.zip $BUILD_BASE_S3_URL/$CLEAN_BRANCH/deploy.zip --metadata="$METADATA"
  post_build:
    commands: |
      echo Build completed on `date`
cache:
  paths:
    - '/usr/local/lib/node_modules/**/*'